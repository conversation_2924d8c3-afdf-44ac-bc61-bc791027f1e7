<div class="bg-white bg-no-repeat custom-dropshadow footer-bg dark:bg-slate-700 flex
justify-around items-center
backdrop-filter backdrop-blur-[40px] fixed left-0 bottom-0 w-full z-[9999]
bothrefm-0 py-[12px] px-4 md:hidden">
<a wire:navigate href="profile" class="relative bg-white bg-no-repeat backdrop-filter backdrop-blur-[40px]
rounded-full footer-bg dark:bg-slate-700 h-[65px] w-[65px] z-[-1] -mt-[40px] flex justify-center items-center">
  <div class="h-[50px] w-[50px] rounded-full relative left-[0px] hrefp-[0px] custom-dropshadow">
    @if (empty(auth()->user()->image))
        <img src="{{ asset('assets/images/avatar/'). '/' .  auth()->user()->role }}.png" alt="user" class="block w-full h-full object-cover rounded-full bg-gray-900" />
    @else
        <image src="storage/{{ auth()->user()->image }}" class="rounded-full block w-full h-full object-fill aspect-square overflow-hidden" />
    @endif
  </div>
</a>
</div>
