<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesReport extends Model
{
    use HasFactory;

    protected $table = 'sales_reports';

    protected $fillable = [
        'sales_id',
        'student_id',
        'subscription',
        'paid',
        'bank_id',
        'trans_code',
        'numofclasses',
        'startdate',
        'reg_status',
        'lastupdateuser_id',
    ];

    public function bank()
    {
        return $this->belongsTo(Banks::class, 'bank_id', 'id');
    }
    public function student()
    {
        return $this->belongsTo(User::class, 'student_id', 'id');
    }
    public function sales()
    {
        return $this->belongsTo(User::class, 'sales_id', 'id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'lastupdateuser_id', 'id');
    }

    public function scopeSearch($query, $value)
    {
        return $query->when($value, function($query, $value){
            return $query->whereHas('student', function ($query) use ($value) {
                $query->where('name', 'like', '%' . $value . '%');
            });
        });
    }
    public function scopeFiltersalesman($query, $value)
    {
        return $query->when($value, function($query, $value){
            return $query->whereHas('sales', function ($query) use ($value) {
                    $query->where('sales_id',$value);
            });
        });
    }
    public function scopeFiltercountry($query, $value)
    {
        return $query->when($value, function($query, $value){
            return $query->whereHas('student', function ($query) use ($value) {
                    $query->where('country',$value);
            });
        });
    }
    public function scopeFilterregstatus($query, $value)
    {
        return $query->when($value, function($query, $value){
            return $query->where('reg_status', $value);
        });
    }
    public function scopeFilterdate($query, $startdate, $enddate)
    {
        return $query->when($startdate, function ($query, $startdate){
            return $query->whereDate('updated_at', '>=', $startdate);
        })
        ->when($enddate, function ($query, $enddate){
            return $query->whereDate('updated_at', '<=', $enddate);
        });
    }
}
