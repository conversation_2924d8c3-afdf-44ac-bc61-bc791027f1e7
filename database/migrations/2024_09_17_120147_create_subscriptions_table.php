<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->integer('numofclasses');
            $table->decimal('amount_sar')->default(0);
            $table->decimal('amount_aed')->default(0);
            $table->decimal('amount_kwd')->default(0);
            $table->decimal('amount_qar')->default(0);
            $table->decimal('amount_omr')->default(0);
            $table->decimal('amount_egp')->default(0);
            $table->decimal('amount_usd')->default(0);
            $table->longText('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
