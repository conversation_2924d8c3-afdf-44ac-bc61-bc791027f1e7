<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Achievements extends Model
{
    use HasFactory;

    protected $table = 'achievements';

    protected $fillable = [
        'student_id',
        'teacher_id',
        'name',
        'description',
        'notes',
    ];

    public function student()
    {
        return $this->belongsTo(User::class, 'student_id', 'id');
    }
    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_id', 'id');
    }


    public function scopeSearch($query, $value)
    {
        return $query->where('name', 'like', '%' . $value . '%');
    }
    public function scopeFilterteacher($query, $value)
    {
        $query->when($value, function($query, $value) {
            return $query->where('teacher_id', $value );
        });
    }
    public function scopeFilterstudent($query, $value)
    {
        $query->when($value, function($query, $value) {
            return $query->where('student_id', $value);
        });
    }
}
