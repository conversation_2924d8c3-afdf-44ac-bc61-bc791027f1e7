<?php

namespace App\Livewire;

use App\Models\Clss;
use App\Models\Settings;
use App\Models\Teacher;
use App\Models\User;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;
use App\Traits\UseFilter;

class Finances extends Component
{
    use WithPagination, UseFilter;

// selected / select all
    public $teacherloggedinduration = 0;
    public $studentloggedinduration = 0;
    public $teacherrate = 0;

    public $students = [];
    public $teachers = [];
    public $supervisors = [];

//update teacher rate per class
    #[Computed]
    public function updatedfilterteacher()
    {
        $this->teacherrate = Teacher::where('user_id', $this->filterteacher)->first()->rateperclass ?? 0;
    }
    public function mount()
    {
        $this->perpage = Settings::find(1)->perpage;
        $this->sortBy = 'date_time';
        $this->sortDir = 'ASC';
        if(auth()->user()->role == 'teacher')
        {
            $this->filterteacher = auth()->user()->id;
        }
        $this->teacherrate = Teacher::where('user_id', $this->filterteacher)->first()->rateperclass ?? 0;
        $this->filterbymonth();

        $this->students = User::filterrole('student')->get();
        $this->teachers = User::filterrole('teacher')->get();
        $this->supervisors = User::filterrole('supervisor')->get();
    }

    public function render()
    {
        return view('livewire.finances', [
            'classes' => Clss::filterdate($this->filterdatestart, $this->filterdateend)
            ->with('clssreport')
            ->search($this->search)
            ->filterteacher($this->filterteacher)
            ->filterstudent($this->filterstudent)
            ->filteractive($this->filteractive)
            ->orderBy($this->sortBy, $this->sortDir)
            ->paginate($this->perpage),
        ]);
    }
}
