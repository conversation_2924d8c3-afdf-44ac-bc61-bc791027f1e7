<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Student>
 */
class StudentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => fake()->numberBetween(1,10),
            'dateofbirth' => fake()->date('Y-m-d', 'now'),
            'gender_prefer' => fake()->randomElement(['male', 'female', 'no-pref']),
            'subscription' => fake()->randomElement(['fixed', 'suspended', 'trial']),
            'subscription_id' => 1,
            'paid' => fake()->randomElement(['200', '250', '300', '350', '400', '450', '500']),
            'bank_id' => 1,
            'numofclasses' => fake()->randomElement(['12', '15', '18', '20', '24', '30', '40']),
            'startdate' => fake()->dateTime('now'),
            'reg_status' => 'new',
        ];
    }
}
