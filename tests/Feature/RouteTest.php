<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class RouteTest extends TestCase
{
    /**
     * A basic feature test example.
     */
    public function test_main_dashboard_is_working_and_showing_to_authrized(): void
    {
        $user = User::factory()->create(['role' => fake()->randomElement(['superadmin', 'admin', 'accountant'])]);

        $this->actingAs($user);

        $response = $this->get('/');

        $response->assertOk();
    }
    public function test_class_section_is_working_and_showing_to_authrized(): void
    {
        $user = User::factory()->create(['role' => fake()->randomElement(['superadmin', 'admin', 'accountant', 'moderator', 'supervisor', 'sales', 'teacher', 'student'])]);

        $this->actingAs($user);

        $response = $this->get('class');

        $response->assertOk();
    }
}
