<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeacherQualifications extends Model
{
    use HasFactory;

    protected $table = 'teacher_qualifications';

    protected $fillable = [
        'name',
        'rateperhour',
        'notes',
    ];
    public function teacher()
    {
        return $this->belongsTo(Teacher::class, 'qualifications', 'id');
    }
    public function clss()
    {
        return $this->hasOne(Clss::class, 'qualifi_id', 'id');
    }
    public function clssreport()
    {
        return $this->hasOne(ClssReport::class, 'qualifi_id', 'id');
    }

        public function scopeSearch($query, $value)
        {
            return $query->where('name', 'like', '%' . $value . '%');
        }

}
