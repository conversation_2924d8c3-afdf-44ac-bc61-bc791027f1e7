<div>
    {{-- add modal --}}
    <x-g-modal name="add">
        <div class="bg-white dark:bg-blue-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">إضافة مستخدم جديد</h1>
                <button type="button" wire:click.prevent="closemodal('add')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="add-teacher"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم:</label>
                    <input type="text" wire:model.live="name" id="name" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسم المعلم" required>
                    @error('name')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم المستخدم:</label>
                    <input type="text" wire:model.live="username" id="username" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسم المستخدم" required>
                    @error('username')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الالكتروني</label>
                    <input type="email" wire:model.live="email" id="email" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب بريدك الالكتروني"  required>
                    @error('email')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كلمة السر</label>
                    <input type="{{ $showpass ? 'text' : 'password' }}" wire:model.live="password" id="password" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="ادخل كلمة السر" required><br/>
                    <label for="showpass" class="flex place-items-center mt-2">
                        <input wire:model.live="showpass" id="showpass" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="showpass">
                        <span class="mx-2 text-sm text-white">{{ __('اظهار الباسورد') }}</span>
                    </label>
                    @error('password')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم التليفون:</label>
                    <input type="text" wire:model.live="phone" id="phone" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب رقم التليفون" required>
                    @error('phone')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="rateperclass" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">سعر الحصة:</label>
                    <input type="text" wire:model.live="rateperclass" id="rateperclass" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب سعر الحصة" required>
                    @error('rateperclass')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="gender" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">النوع:</label>
                    <select wire:model.live="gender" id="gender" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;" value="" selected>اختر النوع</option>
                        <option style="direction:rtl;" value="male" >معلم</option>
                        <option style="direction:rtl;" value="female" >معلمة</option>
                    </select>
                    @error('gender')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البلد:</label>
                    <select wire:model.live="country" id="country" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  selected>اختر البلد</option>
                        @foreach ( $listcountries as $key => $listcountry)
                            <option style="direction:rtl;" value="{{ $listcountry }}">{{ $listcountry }}</option>
                        @endforeach
                    </select>
                    @error('country')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="zoomuser_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم العضوية:</label>
                    <input type="text" dir="ltr" wire:model.live="zoomuser_id" id="zoomuser_id" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب رقم العضوية في الزوم" required>
                    @error('zoomuser_id')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="qualifications" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المهارات:</label>
                    <div class="mb-4 grid grid-cols-3 gap-3">
                    @foreach ( $listqualifications as $qualification)
                    <div class="flex justify-start gap-2">
                        <input type="checkbox" wire:model.live="qualifications" value="{{ $qualification->id }}" id="{{ $qualification->name .'-'.$qualification->id}}">
                        <label for="qualifications" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ $qualification->name }}</label>
                    </div>
                    @endforeach
                    </div>
                    @error('qualifications')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="transfersalary" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تحويل الراتب:</label>
                    <select wire:model.live="transfersalary" id="transfersalary" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;" value="" selected>اختر الطريقة</option>
                        <option style="direction:rtl;" value="e-wallet" >محافظ الكترونية</option>
                        <option style="direction:rtl;" value="instapay" >انستاباي</option>
                        <option style="direction:rtl;" value="bankaccount" >حساب بنكي</option>
                        <option style="direction:rtl;" value="others" >اخرى</option>
                    </select>
                    @error('transfersalary')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                @if ($showdetails)
                    <div class="mb-4">
                        <label for="transferdetails" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">بيانات {{ $this->transferetitle($transfersalary) }}:</label>
                        <input type="text" wire:model.live="transferdetails" id="transferdetails" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب البيانات" required>
                        @error('transferdetails')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                @else

                @endif
                <div class="mb-4">
                    <label for="upfiles" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملفات مرفقة:</label>
                    @foreach ($upfiles as $index => $upfile)
                    <div class="flex justify-between gap-2 place-items-center">
                        <input type="text" wire:model.live="upfiles.{{ $index }}.filename" id="upfiles.{{ $index }}.filename" class="shadow-sm rounded-md w-1/2 h-12 px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسم الملف" required>
                        @error('upfiles.{{ $index }}.filename')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                        <input type="file" wire:model.live="upfiles.{{ $index }}.uploadedfile" id="upfiles.{{ $index }}.uploadedfile" class=" my-4 h-12 shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        @error('upfiles.{{ $index }}.uploadedfile')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                        <button type="button" wire:click.prevent="removeupfiles({{ $index }})" class="w-12 mr-2 h-12 bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-2 px-2 flex justify-center place-items-center rounded align-middle">
                            <iconify-icon icon="heroicons:minus-circle-16-solid" style="font-size: 32px;color:white;font-weight: bold;"></iconify-icon>
                        </button>
                    </div>
                    @php
                        $curr = $index ;
                    @endphp
                    @endforeach
                    @if($upfilesnum >= 5)
                    <button type="button" disabled wire:click.prevent="addupfiles({{ $curr+1 }})" class="w-12 mr-2 h-12 bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-2 px-2 flex justify-center place-items-center rounded align-middle">
                        <iconify-icon icon="heroicons:plus-circle-16-solid" style="font-size: 32px;color:white;font-weight: bold;"></iconify-icon>
                    </button>
                    @else
                    <button type="button" wire:click.prevent="addupfiles({{ $curr+1 }})" class="w-12 mr-2 h-12 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-2 flex justify-center place-items-center rounded align-middle">
                        <iconify-icon icon="heroicons:plus-circle-16-solid" style="font-size: 32px;color:white;font-weight: bold;"></iconify-icon>
                    </button>
                    @endif
                </div>
                <div class="mb-4">
                    <div class="flex items-center mb-4">
                    <label class="relative flex items-center  cursor-pointer">
                    <input type="checkbox" wire:model.live="status" class="sr-only peer">
                    <div class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0  rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600 hover:peer-checked:bg-indigo-700 "></div>
                    </label>
                    <span class="mr-3 text-white font-bold text-lg ">{{ $status ? 'مفعل' : 'غير مفعل' }}</span>
                    </div>
                    @error('status')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="notes" class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">ملاحظات:</label>
                    <textarea wire:model.live="notes" name="notes" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                    @error('notes')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                @if (is_array($qualifications))
                    @foreach ($qualifications as $quali)
                        {{ $quali }},
                    @endforeach
                @else
                    None
                @endif
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="save" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="save"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('add')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- edit modal --}}
    <x-g-modal name="edit">
        <div class="bg-white dark:bg-yellow-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">تعدبل بيانات "{{ $name }}" </h1>
                <button type="button" wire:click.prevent="closemodal('edit')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="edit-teacher"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {plus
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم:</label>
                    <input type="text" wire:model.live="name" id="name" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" value="{{ $name }}" placeholder="اكتب اسم المعلم" required>
                    @error('name')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم المستخدم:</label>
                    <input type="text" wire:model.live="username" id="username" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" value="{{ $username }}" placeholder="اكتب اسم المستخدم" required>
                    @error('username')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الالكتروني</label>
                    <input type="email" wire:model.live="email" id="email" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" value="{{ $email }}" placeholder="اكتب بريدك الالكتروني"  required>
                    @error('email')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كلمة السر</label>
                    <input type="{{ $showpass ? 'text' : 'password' }}" wire:model.live="password" id="password" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                    <span class="text-sm font-bold text-black-500">* ادخل كلمة سر جديدة اذا كنت تريد تغييرها </span><br/>
                    <label for="showpass" class="flex place-items-center mt-2">
                        <input wire:model.live="showpass" id="showpass" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="showpass">
                        <span class="mx-2 text-sm text-white">{{ __('اظهار الباسورد') }}</span>
                    </label>
                    @error('password')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم التليفون:</label>
                    <input type="text" wire:model.live="phone" value="{{ $phone }}" id="phone" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب رقم التليفون" required>
                    @error('phone')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="rateperclass" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">سعر الساعة:</label>
                    <input type="text" wire:model.live="rateperclass" id="rateperclass" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب سعر الحصة" required>
                    @error('rateperclass')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="gender" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">النوع:</label>
                    <select wire:model.live="gender" id="gender" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;" value="">اختر النوع</option>
                        <option style="direction:rtl;" value="male" {{ $gender == 'male' ? ' selected' : ''}}>معلم</option>
                        <option style="direction:rtl;" value="female" {{ $gender == 'female' ? ' selected' : ''}}>معلمة</option>
                    </select>
                    @error('gender')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البلد:</label>
                    <select wire:model.live="country" id="role" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"   value="{{ $country }}" selected>{{ $country }}</option>
                        @foreach ( $listcountries as $key => $listcountry)
                            <option style="direction:rtl;" value="{{ $listcountry }}">{{ $listcountry }}</option>
                        @endforeach
                    </select>
                    @error('country')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="zoomuser_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم العضوية:</label>
                    <input type="text" wire:model.live="zoomuser_id" id="zoomuser_id" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب رقم العضوية في الزوم" required>
                    @error('zoomuser_id')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="qualifications" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المهارات:</label>
                    <div class="mb-4 grid grid-cols-3 gap-3">
                    @foreach ( $listqualifications as $qualification)
                    <div class="flex justify-start gap-2">
                        <input type="checkbox" wire:model.live="qualifications" value="{{ $qualification->id }}" id="{{ $qualification->name .'-'.$qualification->id}}">
                        <label for="qualifications" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ $qualification->name }}</label>
                    </div>
                    @endforeach
                    </div>
                    @error('qualifications')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="transfersalary" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تحويل الراتب:</label>
                    <select wire:model.live="transfersalary" id="transfersalary" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;" value="" selected>اختر الطريقة</option>
                        <option style="direction:rtl;" value="e-wallet" >محافظ الكترونية</option>
                        <option style="direction:rtl;" value="instapay" >انستاباي</option>
                        <option style="direction:rtl;" value="bankaccount" >حساب بنكي</option>
                        <option style="direction:rtl;" value="others" >اخرى</option>
                    </select>
                    @error('transfersalary')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                @if ($showdetails)
                    <div class="mb-4">
                        <label for="transferdetails" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">بيانات {{ $this->transferetitle($transfersalary) }}:</label>
                        <input type="text" wire:model.live="transferdetails" id="transferdetails" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب البيانات" required>
                        @error('transferdetails')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                @endif
                <div class="mb-4" wire:poll.keepalive>
                    <label for="upfiles" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملفات مرفقة:</label>
                    @if (!empty($oldupfiles))
                    <div class="">
                        @foreach ($oldupfiles as $file )
                        <span class="flex justify-between mb-4 place-items-center text-lg font-bold text-gray-300">
                            تحميل الملف : <a href="storage/{{ $file->url }}" target="_blank" rel="noopener noreferrer">{{ $file->name }}</a>
                            <button type="button" wire:click.prevent="removeoldupfile({{ $file->id }})" class="w-8 mr-2 h-8 bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-2 px-2 flex justify-center place-items-center rounded align-middle">
                                <iconify-icon icon="heroicons:minus-circle-16-solid" style="font-size: 24px;color:white;font-weight: bold;"></iconify-icon>
                            </button>
                        </span>
                        @endforeach
                    </div>
                    @endif
                    @foreach ($upfiles as $index => $upfile)
                    <div class="flex justify-between gap-2 place-items-center">
                        <input type="text" wire:model.live="upfiles.{{ $index }}.filename" id="upfiles.{{ $index }}.filename" class="shadow-sm rounded-md w-1/2 h-12 px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسم الملف" required>
                        @error('upfiles.{{ $index }}.filename')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                        <input type="file" wire:model.live="upfiles.{{ $index }}.uploadedfile" id="upfiles.{{ $index }}.uploadedfile" class=" my-4 h-12 shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        @error('upfiles.{{ $index }}.uploadedfile')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                        <button type="button" wire:click.prevent="removeupfiles({{ $index }})" class="w-12 mr-2 h-12 bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-2 px-2 flex justify-center place-items-center rounded align-middle">
                            <iconify-icon icon="heroicons:minus-circle-16-solid" style="font-size: 32px;color:white;font-weight: bold;"></iconify-icon>
                        </button>
                    </div>
                    @php
                        $curr = $index ;
                    @endphp
                    @endforeach
                    @if($upfilesnum >= 5)
                    <button type="button" disabled wire:click.prevent="addupfiles({{ $curr+1 }})" class="w-12 mr-2 h-12 bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-2 px-2 flex justify-center place-items-center rounded align-middle">
                        <iconify-icon icon="heroicons:plus-circle-16-solid" style="font-size: 32px;color:white;font-weight: bold;"></iconify-icon>
                    </button>
                    @else
                    <button type="button" wire:click.prevent="addupfiles({{ $curr+1 }})" class="w-12 mr-2 h-12 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-2 flex justify-center place-items-center rounded align-middle">
                        <iconify-icon icon="heroicons:plus-circle-16-solid" style="font-size: 32px;color:white;font-weight: bold;"></iconify-icon>
                    </button>
                    @endif
                </div>
                <div class="mb-4">
                    <div class="flex items-center mb-4">
                    <label class="relative flex items-center  cursor-pointer">
                    <input type="checkbox" wire:model.live="status" class="sr-only peer">
                    <div class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0  rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600 hover:peer-checked:bg-indigo-700 "></div>
                    </label>
                    <span class="mr-3 text-white font-bold text-lg ">{{ $status ? 'مفعل' : 'غير مفعل' }}</span>
                    </div>
                    @error('status')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="notes" class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">ملاحظات:</label>
                    <textarea wire:model.live="notes" name="notes" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required></textarea>
                    @error('notes')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="update" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="update"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('edit')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- view modal --}}
    <x-g-modal name="view">
        <div class="bg-white dark:bg-gray-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">{{ $name }}</h1>
                <button type="button" wire:click.prevent="closemodal('view')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <div class="flex justify-between gap-4">
                <div class="">
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">الاسم:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $name }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">البريد الالكتروني:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $email }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">اسم المستخدم:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $username }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">تليفون:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $phone }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">البلد:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $country }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">رقم العضوية في زووم:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $zoomuser_id }}</span>
                    </div>
                </div>
                <div class="">
                    <div class="mb-4">
                        @if (empty($image))
                            <div class="block w-52 h-52">
                                <img src="{{ asset('assets/images/avatar/'). '/teacher' }}.png" alt="user" class="block border-2 object-fill aspect-square overflow-hidden border-gray-700 rounded-full bg-gray-900" width="208" height="208" />
                            </div>
                        @else
                            <div class="block w-52 h-52">
                                <image src="storage/{{ $image }}" class="rounded-full block border-2 object-fill aspect-square overflow-hidden border-gray-700" width="208" height="208" />
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            <div class="flex justify-between gap-4">
                <div class="">
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">معلم / معلمة:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $gender == 'male' ? 'ذكر' : 'انثى' }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">سعر الساعة:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $rateperclass }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">تحويل الراتب:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $this->transferetitle($transfersalary) }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">بيانات التحويل:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $transferdetails }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">المهارات:</span>
                        <span class="block text-lg font-bold text-gray-300">
                            {{-- {{ dd($qualifications) }} --}}
                            [
                            @if (count($qualifications)>0)
                                @foreach ( $qualifications as $qualification)
                                    " {{ $this->qualiname($qualification) }} ",
                                @endforeach
                            @else
                            <span class="block text-lg font-bold text-gray-300 px-2">
                                لا يوجد مهارات
                            </span>
                            @endif
                         ]</span>
                    </div>
                    <div class="mb-4 block">
                        <span class="block text-lg font-bold text-gray-300 px-2">مرفقات:</span>
                        @unless (count($oldupfiles) == 0)
                        @foreach ( $oldupfiles as $file)
                        <span class="block text-lg font-bold text-gray-300 mr-4">
                            <a href="storage/{{ $file->url }}" target="_blank" rel="noopener noreferrer" class="flex justify-start place-items-center gap-1">
                            <iconify-icon icon="heroicons:cloud-arrow-down-20-solid" style="font-size: 24px;color:white;font-weight: bold;"></iconify-icon>
                            {{ $file->name }}
                            </a>
                        </span>
                        @endforeach
                        @else
                        <span class="block text-lg font-bold text-gray-300 px-2">
                        لا يوجد مرفقات
                        </span>
                        @endunless
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">الحالة:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $status == true ? 'مفعل' : 'غير مفعل' }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">ملاحظات:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $notes }}</span>
                    </div>
                </div>
            </div>
            <div class="flex justify-end">
                <button type="button" wire:click.prevent="printteacher({{ $teacherid }})" class="w-24 py-2 flex justify-center place-items-center gap-1 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <iconify-icon icon="heroicons:printer-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="طباعة"></iconify-icon>
                    تحميل
                </button>
                <button type="button" wire:click.prevent="showteacher({{ $teacherid }})" class="w-24 py-2 flex justify-center place-items-center gap-1 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <iconify-icon icon="heroicons:eye-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="مشاهدة"></iconify-icon>
                    مشاهدة
                </button>
                <button type="button" wire:click.prevent="closemodal('view')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
            </div>
        </div>
    </x-g-modal>
    {{-- delete modal --}}
    <x-g-modal name="delete">
        <div class="bg-white dark:bg-red-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">حذف المستخدم "{{ $name }}"</h1>
                <button type="button" wire:click.prevent="closemodal('delete')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="delete-teacher">
                <div class="mb-4">
                    <div class="block text-right text-lg font-bold text-white">هل انت متأكد من حذف المستخدم "{{ $name }}"؟!</div>
                </div>
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="delete" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>

                    <x-loading method="delete"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('delete')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- delete selected modal --}}
    <x-g-modal name="deleteselected">
        <div class="bg-white dark:bg-gray-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">حذف المستخدمين</h1>
                <button type="button" wire:click.prevent="closemodal('deleteselected')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="delete-selectedteachers">
                @if (empty($selectedteachers))
                    <div class="mb-4">
                        <div class="block text-right text-lg font-bold text-white">لابد من اختيار عناصر حتى يتم حذفهم!</div>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" disabled wire:click.prevent="deleteselected" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 h focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>
                        <button type="button" wire:click.prevent="closemodal('deleteselected')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                    </div>
                @else
                    <div class="mb-4">
                        <div class="block text-right text-lg font-bold text-white">هل انت متأكد من حذف المستخدمين؟!</div>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" wire:loading.remove wire:click.prevent="deleteselected" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>

                        <x-loading method="deleteselected"></x-loading>

                        <button type="button" wire:click.prevent="closemodal('deleteselected')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                    </div>
                @endif
            </form>
        </div>
    </x-g-modal>


    <!-- flash save successful message -->
    <x-flash-message on="flashsaved">
        <div class="flex items-center bg-green-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تسجيل العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    <!-- flash update successful message -->
    <x-flash-message on="flashupdated">
        <div class="flex items-center bg-yellow-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تعديل العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    <!-- flash delete successful message -->
    <x-flash-message on="flashdeleted">
        <div class="flex items-center bg-red-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم حذف العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    {{-- data tables --}}
    <div class="card">
        <header class=" block card-header noborder">
          <h4 class="text-primary-500 font-bold text-3xl">إدارة المعلمين</h4>
        </header>
        <div x-data="{open: false }" class="card-body px-6 pb-6">
          <div class="sm:grid sm:my-2 md:flex sm:place-content-center md:justify-between w-auto mr-0">
            <div class="grid sm:grid-cols-1 lg:grid-cols-6 gap-3">
                {{-- add modal button --}}
                @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                <button type="button" x-data="" x-on:click.prevent="$dispatch('open-modal', 'add')" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div class="flex place-items-center">
                    <iconify-icon icon="heroicons:plus-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    اضافة عضو جديد
                    </div>
                </button>
                {{-- delete selected modal button --}}
                <button type="button"  wire:click.prevent="showdeleteselected"  class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div class="flex place-items-center">
                    <iconify-icon icon="heroicons:trash-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="حذف"></iconify-icon>
                    حذف المحددين
                    </div>
                </button>
                @endif
                {{-- advanced section modal button --}}
                <button type="button" x-on:click="open = ! open" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div x-show="! open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اظهار البحث الــمتقدم
                    </div>
                    <div x-show="open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اخفاء البحث الــمتقدم
                    </div>
                </button>
                @if (!empty($filtercountry) || !empty($filtergender) || !empty($filteractive))
                    <button wire:click.prevent="resetallfilter" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div class="flex place-items-center">
                            <iconify-icon icon="heroicons:arrow-uturn-down-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                            إعادة تعيين التصفية
                        </div>
                    </button>
                @endif
            </div>
            <div class="my-3 sm:w-auto sm:mt-0 sm:ml-auto md:ml-0">
                <div class="relative flex w-80 text-gray-700 mr-auto my-auto place-items-center">
                    @if (!empty($search))
                    <button type="button" wire:click.prevent="resetsearch" class="absolute inset-y-0 left-0 pl-3 flex items-center z-1">
                        <iconify-icon icon="heroicons:x-mark-20-solid" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                    </button>
                    @endif
                    <iconify-icon icon="heroicons:magnifying-glass-solid" style="font-size: 24px;color:#016241;" alt="تعديل" class="ml-2"></iconify-icon>
                    <input wire:model.live.debounce.500="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-lg rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="بحث..." wire:ignore>
                </div>
            </div>
          </div>
          <div x-show="open" class="grid sm:grid-cols-1 lg:grid-cols-5 gap-2 w-full my-2">
            <div class="flex place-items-center">
            <label for="filtercountry" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">البلد: </label>
            <select name="filtercountry" wire:model.live="filtercountry" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;" value="" selected>اختر البلد</option>
                    @foreach ( $listcountries as $key => $listcountry)
                        <option style="direction:rtl;" value="{{ $listcountry }}">{{ $listcountry }}</option>
                    @endforeach
            </select>
            @if (!empty($filtercountry))
            <button type="button" wire:click.prevent="resetfilter('filtercountry')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
            <div class="flex place-items-center">
            <label for="filtergender" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 mx-2 whitespace-nowrap">معلم / معلمة:</label>
            <select name="filtergender" wire:model.live="filtergender" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;"  value="" selected>اختر ممايلي</option>
                <option style="direction:rtl;" value="male">معلم</option>
                <option style="direction:rtl;" value="female">معلمة</option>
            </select>
            @if (!empty($filtergender))
            <button type="button" wire:click.prevent="resetfilter('filtergender')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
            <div class="flex place-items-center"></div>
            <div class="flex place-items-center">
            <label for="filterqualifications" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 mx-2 whitespace-nowrap">المهارات:</label>
            <select name="filterqualifications" wire:model.live="filterqualifications" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;"  value="">اختر ممايلي</option>
                @foreach ( $listqualifications as $key => $listqualification)
                    <option style="direction:rtl;" value="{{ $listqualification->id }}">{{ $listqualification->name }}</option>
                @endforeach
            </select>
            @if (!empty($filterqualifications))
            <button type="button" wire:click.prevent="resetfilter('filterqualifications')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
            <div class="flex place-items-center">
            <label for="filteractive" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 mx-2">الحالة:</label>
            <select name="filteractive" wire:model.live="filteractive" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:ltr;" value="" selected>اختر الحالة</option>
                <option style="direction:ltr;" value=0>متاح</option>
                <option style="direction:ltr;" value=1>غير متاح</option>
            </select>
            @if ($filteractive >= 0)
            <button type="button" wire:click.prevent="resetfilter('filteractive')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
          </div>
          <div class="overflow-x-auto -mx-6 dashcode-data-table">
            <div class="inline-block min-w-full align-middle">
              <div class="overflow-hidden">
                @unless ($teachers->count() == 0)
                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl">
                  <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                    <tr>
                      <th scope="col" class=" table-th text-xl font-medium"><input type="checkbox" wire:model.live="selectall" class="border-gray-300 rounded bg-gray-50 borded ml-1"> الكل </th>
                      <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('name')">الإسم</a></th>
                      <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('username')">اسم المستخدم</a></th>
                      <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('email')">البريد الالكتروني</a></th>
                      <th scope="col" class=" table-th text-xl font-medium">رقم التليفون</th>
                      <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('country')">البلد</a></th>
                      <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">النوع</th>
                      @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  || auth()->user()->role == 'accountant')
                      <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]"><a href="#" wire:click.prevent="sortfields('rateperclass')" class="mx-auto">س . ح</a></th>
                      @endif
                      <th scope="col" class=" table-th text-xl flex font-medium"><a href="#" wire:click.prevent="sortfields('is_active')" class="mx-auto">الحالة</a></th>
                      @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                      <th scope="col" class=" table-th text-xl font-medium ">أجراءات</th>
                      @endif
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                    @foreach ($teachers as $teacher)
                    <tr wire:key="teacher-{{ $teacher->id }}">
                      <td class="table-td text-xl font-normal"><input type="checkbox" wire:model.live="selectedteachers" value="{{ $teacher->id }}" class="border-gray-300 rounded bg-gray-50 borded"></td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $teacher->name }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $teacher->username }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $teacher->email }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $teacher->phone }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $teacher->country }}</span>
                      </td>
                      <td class="table-td text-center text-xl font-normal">
                        @if ($teacher->teacher->gender === 'male')
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white border-[#016241] border bg-primary-500">
                                معلم
                        @else
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white border-[#016241] border bg-success-500">
                                معلمة
                        @endif
                        </div>
                      </td>
                      @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  || auth()->user()->role == 'accountant')
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $teacher->teacher->rateperclass }}</span>
                      </td>
                      @endif
                      <td class="table-td text-center text-xl font-normal">
                        @if ($teacher->is_active === true)
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white border-[#016241] border bg-danger-500">
                                غير متاح
                        @else
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white border-[#016241] border bg-success-500">
                                متاح
                        @endif
                        </div>
                      </td>
                      @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                      <td class="table-td  text-xl font-normal">
                        <div class="flex space-x-3 rtl:space-x-reverse">
                            <button type="button" wire:click.prevent="showview({{ $teacher }})">
                              <iconify-icon icon="heroicons:eye-solid" style="font-size: 24px;color:#016241;font-weight:bold;" alt="مشاهدة"></iconify-icon>
                            </button>
                          <button type="button" wire:click.prevent="showedit({{ $teacher }})">
                            <iconify-icon icon="heroicons:pencil-square" style="font-size: 24px;color:#016241;font-weight:bold;" alt="تعديل"></iconify-icon>
                          </button>
                          <button type="button" wire:click.prevent="showdelete({{ $teacher }})">
                            <iconify-icon icon="heroicons:trash" style="font-size: 24px;color:#016241;font-weight:bold;" alt="حذف"></iconify-icon>
                          </button>
                        </div>
                      </td>
                      @endif
                    </tr>
                    @endforeach
                  </tbody>
                </table>
                @else
                <div class="text-center text-xl font-bold text-[#016241]">لا يوجد معلمين حتى الآن.</div>
                @endunless
                @foreach ($selectedteachers as $selectedteacher)
                    {{ $selectedteacher }},
                @endforeach
              </div>
            </div>
          </div>
        </div>
    </div>
<div class="flex my-4">
    <div class="flex justify-start w-[50%] items-center">
        <label for="perpage" class="text-lg font-medium text-gray-700 dark:text-[#be9539]">عدد النتائج في كل صفحة:</label>
        <select wire:model.live="perpage" id="role" style="direction:ltr;" class="w-20 shadow-sm rounded-md mx-4 px-5 py-1 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            @foreach ($perpageoptions as $option)
                <option style="direction:rtl;" value="{{ $option }}">{{ $option === 'all' ? 'الكل' : $option }}</option>
            @endforeach
        </select>
    </div>
    @if ($perpage === 'all')

    @else
    <div class="w-[100%] mr-4">{{ $teachers->links('vendor.livewire.tailwind') }}</div>
    @endif
</div>
@push('scripts')
<script>
function printReport() {
    const reportContent = document.getElementById('report-content');
    const printWindow = window.open();
    printWindow.document.write(reportContent.outerHTML);
    printWindow.print();
    printWindow.close();
}
// window.addEventListener('print-cert', function () {
//     console.log('printed');
//     const certContent = document.getElementById('cert-content');
//     const printCertWindow = window.open();
//     printCertWindow.document.write(certContent.outerHTML);
//     printCertWindow.print();
//     printCertWindow.close();
// })

// window.addEventListener('print-cert', function () {
//     console.log('printed');
//     const reportContent = document.getElementById('cert-content');
//     const pdf = new jsPDF({
//         orientation: 'l',
//         unit: 'mm',
//         format: 'a4',
//         putOnlyUsedFonts:true
//     });
//     pdf.fromHTML(reportContent, 15, 15 );
//     pdf.save('certificate.pdf');
// })
    window.addEventListener('open-new-window', function(event){
        // console.log(typeof event.detail[0]);
        const url = event.detail[0];
        window.open(url, '_blank');
    })
</script>
@endpush
</div>
