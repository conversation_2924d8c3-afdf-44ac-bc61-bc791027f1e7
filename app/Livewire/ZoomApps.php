<?php

namespace App\Livewire;

use Carbon\Carbon;
use App\Models\Settings;
use App\Traits\UseFilter;
use App\Traits\UseDefault;
use App\Models\Notification;
use App\Models\User;
use App\Models\ZoomApp;

use Livewire\WithPagination;
use Livewire\Attributes\Computed;

use Livewire\Component;

class ZoomApps extends Component
{
    use WithPagination, UseFilter, UseDefault;

    public $selectedzoomapps = [];
    public $listusers = [];
    public $selectall = false;

    public $zoomapp_id;
    public $user_id;
    public $zoom_user_id;
    public $account_id;
    public $client_id;
    public $client_secret;
    public $notes = '';

    public $lastupdatedat = '';
    public $lastupdateuser_id = '';

    public function save()
    {
        $validated = $this->validate([
            'user_id' => 'required|numeric',
            'zoom_user_id' => 'required|string|max:12',
            'account_id' => 'required|string|max:22',
            'client_id' => 'required|string|max:22',
            'client_secret' => 'required|string|max:32',
            'notes' => 'string|nullable|sometimes|max:500',
        ]);

        $zoomapp = ZoomApp::create($validated);

        // $notifiedusers = User::whereIn('role', ['superadmin','admin','acountant'])->pluck('id');
        // foreach ($notifiedusers as $notifieduser) {
        //     Notification::create([
        //         'user_id' => $notifieduser,
        //         'title' => 'new course',
        //         'url' => 'courses?q=' . $zoomapp->name,
        //         'message' => 'تم اضافة كورس جديد باسم "' . $zoomapp->name . '" بواسطة "' . auth()->user()->name . '".',
        //     ]);
        // }

        $this->closemodal('add');
        $this->dispatch('flashsaved');
    }
    public function showedit(ZoomApp $zoomapp)
    {
        $this->zoomapp_id = $zoomapp->id;
        $this->user_id = $zoomapp->user_id;
        $this->zoom_user_id = str_replace(' ', '', $zoomapp->zoom_user_id);
        $this->account_id = $zoomapp->account_id;
        $this->client_id = $zoomapp->client_id;
        $this->client_secret = $zoomapp->client_secret;
        $this->notes = $zoomapp->notes;
        $this->lastupdateuser_id = auth()->user()->id;
        $this->lastupdatedat = Carbon::parse($zoomapp->updated_at)->format('Y-m-d || A g:i:s');
        $this->openmodal('edit');
    }
    public function update()
    {
        $validated = $this->validate([
            'user_id' => 'required|numeric',
            'zoom_user_id' => 'required|string|max:12',
            'account_id' => 'required|string|max:22',
            'client_id' => 'required|string|max:22',
            'client_secret' => 'required|string|max:32',
            'notes' => 'string|nullable|sometimes|max:500',
        ]);

        $zoomapp = ZoomApp::find($this->zoomapp_id);
        $zoomapp->update($validated);

        // $notifiedusers = User::where('role', 'admin')->pluck('id');
        // foreach ($notifiedusers as $notifieduser) {
        //     Notification::create([
        //         'user_id' => $notifieduser,
        //         'title' => 'course updated',
        //         'url' => 'courses?q=' . $zoomapp->name,
        //         'message' => 'تم تعديل كورس باسم "' . $zoomapp->name . '" بواسطة "' . auth()->user()->name . '".',
        //     ]);
        // }

        $this->closemodal('edit');
        $this->dispatch('flashupdated');
    }
    public function showdelete(ZoomApp $zoomapp)
    {
        $this->zoomapp_id = $zoomapp->id;
        $this->openmodal('delete');
    }
    public function delete(){

        $zoomapp = ZoomApp::find($this->zoomapp_id);
        $zoomapp->delete();

        // $notifiedusers = User::where('role', 'admin')->pluck('id');
        // foreach ($notifiedusers as $notifieduser) {
        //     Notification::create([
        //         'user_id' => $notifieduser,
        //         'title' => 'course updated',
        //         'url' => '',
        //         'message' => 'تم حذف كورس باسم "' . $zoomapp->name . '" بواسطة "' . auth()->user()->name . '".',
        //     ]);
        // }
        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }
    public function deleteselected()
    {
        ZoomApp::whereIn('id', $this->selectedzoomapps)->delete();
        $this->selectedzoomapps = [];
        $this->closemodal('deleteselected');
    }
    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedzoomapps = ZoomApp::paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedzoomapps = [];
        }
    }
    #[Computed]
    public function updated($prop)
    {
        if($prop == 'user_id')
        {
            $this->zoom_user_id = str_replace(' ', '', User::find($this->user_id)->teacher->zoomuser_id);
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'zoomapp_id',
            'user_id',
            'zoom_user_id',
            'account_id',
            'client_id',
            'client_secret',
            'notes',
            'selectedzoomapps',
            'selectall',
            'lastupdatedat',
            'lastupdateuser_id',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function mount()
    {
        $this->settings = Settings::find(1);
        $this->listusers = User::filterrole('teacher')->get();

        if (empty($this->perpage)) {
            $this->perpage = $this->settings->perpage;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'DESC';
    }
    public function render()
    {
        return view('livewire.zoom-apps', [
            'zoomapps' => ZoomApp::search($this->search)
            ->paginate($this->perpage),
        ]);
    }
}
