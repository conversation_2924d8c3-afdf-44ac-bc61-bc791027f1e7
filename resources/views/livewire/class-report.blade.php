<div>
    {{-- data tables --}}
    <div class="card">
        <header class=" block card-header noborder">
          <h4 class="text-primary-500 font-bold text-3xl">التقارير المالية للحلقات</h4>
        </header>
        <div x-data="{open: false, shown: true }" x-defer class="card-body px-6 pb-6">
          <div class="sm:grid sm:my-2 md:flex sm:place-content-center md:justify-between w-auto mr-0">
            {{-- control buttons --}}
            <div class="grid sm:grid-cols-1 lg:grid-cols-6 gap-3">
                {{-- advanced section modal button --}}
                <button type="button" x-on:click="open = ! open" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div x-show="! open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اظهار البحث الــمتقدم
                    </div>
                    <div x-show="open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اخفاء البحث الــمتقدم
                    </div>
                </button>
                @if (!empty($filterdatestart) || !empty($filterdateend) || !empty($filterteacher) || !empty($filteractive))
                    <button wire:click.prevent="resetallfilter" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div class="flex place-items-center">
                            <iconify-icon icon="heroicons:arrow-uturn-down-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                            إعادة تعيين التصفية
                        </div>
                    </button>
                @endif
                {{-- print report button --}}
                <button type="button" onclick="printReport()" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div class="flex place-items-center">
                        <iconify-icon icon="heroicons:printer-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="طباعة"></iconify-icon>
                        طباعة التقرير
                    </div>
                </button>
            </div>
            {{-- search field --}}
            <div class="my-3 sm:w-auto sm:mt-0 sm:ml-auto md:ml-0">
                <div class="relative flex w-80 text-gray-700 mr-auto my-auto place-items-center">
                    @if (!empty($search))
                        <button type="button" wire:click.prevent="resetsearch" class="absolute inset-y-0 left-0 pl-3 flex items-center z-1">
                            <iconify-icon icon="heroicons:x-mark-20-solid" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                        </button>
                    @endif
                    <iconify-icon icon="heroicons:magnifying-glass-solid" style="font-size: 24px;color:#016241;" alt="تعديل" class="ml-2"></iconify-icon>
                    <input wire:model.live.debounce.500="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-lg rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="بحث..." wire:ignore>
                </div>
            </div>
          </div>
          {{-- advanced filter by status and teacher --}}
          <div x-show="open" class="sm:grid sm:my-2 md:flex sm:place-content-center md:justify-between place-items-center">
            @if (auth()->user()->role !== 'teacher')
            <div class="flex w-full justify-start place-items-center">
            <label for="filterteacher" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">المعلم: </label>
            <select name="filterteacher" wire:model.live="filterteacher" style="direction:ltr;" class="shadow-sm rounded-md lg:w-1/2 sm:w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;" value="" selected>اختر المعلم</option>
                @foreach ( $teachers as $teacher)
                    <option style="direction:rtl;" value="{{ $teacher->id }}">{{ $teacher->name }}</option>
                @endforeach
            </select>
            @if (!empty($filterteacher))
            <button type="button" wire:click.prevent="resetfilter('filterteacher')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
            @endif
          </div>
          {{-- advanced date filter --}}
          <div x-show="open" class="grid sm:grid-cols-1 lg:grid-cols-3 gap-3 place-items-center my-2">
                <div class="flex w-full place-items-center">
                    <label for="filterdatestart" class="block text-wrap text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">من: </label>
                    <input type="date" id="filterdatestart" wire:model.live="filterdatestart" value="{{ $filterdatestart }}" name="filterdatestart" min="2023-01-01" max="2050-12-31" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
                    @if (!empty($filterdatestart))
                    <button type="button" wire:click.prevent="resetfilter('filterdatestart')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 mr-2 p-1 dark:hover:bg-slate-600 dark:hover:text-white">
                        <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                    </button>
                    @endif
                </div>
                <div class="flex w-full place-items-center">
                <label for="filterdateend" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 lg:mx-2 ml-2">حتى: </label>
                <input type="date" id="filterdateend" wire:model.live="filterdateend" value="{{ $filterdateend }}" name="filterdateend" min="2023-01-01" max="2050-12-31" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
                @if (!empty($filterdateend))
                <button type="button" wire:click.prevent="resetfilter('filterdateend')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 mr-2 p-1 dark:hover:bg-slate-600 dark:hover:text-white">
                    <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                </button>
                @endif
                </div>
            <div class="grid sm:grid-cols-1 lg:grid-cols-3 gap-3 w-full">
                {{-- filter day/week/month button --}}
                <button type="button" wire:click.prevent="filterbyday" class="{{ $filterby == 'today' ? 'bg-[#b36700]' : 'bg-[#906842]' }} hover:bg-[#b36700] text-white font-bold py-4 px-4 rounded-lg place-content-center">
                    <iconify-icon icon="heroicons:calendar-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    حلقات اليوم
                </button>
                <button type="button" wire:click.prevent="filterbyweek" class="{{ $filterby == 'week' ? 'bg-[#b36700]' : 'bg-[#906842]' }} hover:bg-[#b36700] text-white font-bold py-4 px-4 rounded-lg place-content-center">
                    <iconify-icon icon="heroicons:calendar-days-16-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    حلقات الاسبوع
                </button>
                <button type="button" wire:click.prevent="filterbymonth" class="{{ $filterby == 'month' ? 'bg-[#b36700]' : 'bg-[#906842]' }} hover:bg-[#b36700] text-white font-bold py-4 px-4 rounded-lg align-middle mr-0 flex place-content-center">
                    <iconify-icon icon="heroicons:calendar-days-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    حلقات الشهر
                </button>
            </div>
          </div>
          {{-- data table --}}
          <div id="report-content" class="overflow-x-auto -mx-6">
            <div class="inline-block min-w-full align-middle">
              <div class="overflow-hidden">
                <div class="text-lg font-medium text-white">{{ $filterdatestart }}, {{ $filterdateend }}</div>
                @unless ($classes->count() == 0)
                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl" style="width:100%;direction:rtl;">
                  <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                    <tr>
                        <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('username')">اسم الطالب</a></th>
                        <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('name')">اسم المعلم</a></th>
                        <th scope="col" class=" table-th text-xl font-medium">وقت الحصة</th>
                        <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">تقييم المعلم</th>
                        <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">تقييم المشرف</th>
                        <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">تقييم الطالب</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                    @foreach ($classes as $class)
                    <tr wire:key="class-{{ $class->id }}">
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $class->student->name }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $class->teacher->name }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $class->date_time }}</span>
                      </td>
                      <td class=" text-center text-lg font-medium text-[#cbd5e1]">
                        <span class=" text-slate-600 dark:text-white capitalize">
                            {{ $this->getTeacherReport($class->id) }}
                        </span>
                      </td>
                      <td class=" text-center text-lg font-medium text-[#cbd5e1]">
                        <span class=" text-slate-600 dark:text-white capitalize">
                            {{ $this->getSupervisorReport($class->id) }}
                        </span>
                      </td>
                      <td class=" text-center text-lg font-medium text-[#cbd5e1]">
                        <span class=" text-slate-600 dark:text-white capitalize">
                            {{ $this->getStudentReport($class->id) }}
                        </span>
                      </td>
                    </tr>
                    @endforeach
                  </tbody>
                </table>
                @else
                <div class="text-center text-xl font-bold text-[#016241]">لا يوجد تقارير حتى الآن.</div>
                @endunless
              </div>
            </div>
          </div>
        </div>
    </div>
    <div class="flex my-4">
        <div class="flex justify-start w-[50%] items-center">
            <label for="perpage" class="text-lg font-medium text-gray-700 dark:text-[#be9539]">عدد النتائج في كل صفحة:</label>
            <select wire:model.live="perpage" id="role" style="direction:ltr;" class="w-20 shadow-sm rounded-md mx-4 px-5 py-1 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;" value="10">10</option>
                <option style="direction:rtl;" value="20">20</option>
                <option style="direction:rtl;" value="30">30</option>
                <option style="direction:rtl;" value="50">50</option>
                <option style="direction:rtl;" value="2000">الكل</option>
            </select>
        </div>
        <div class="w-[100%] mr-4">{{ $classes->links('vendor.livewire.tailwind') }}</div>
    </div>
    @push('scripts')
    <script>
    function printReport() {
        const reportContent = document.getElementById('report-content');
        const printWindow = window.open();
        printWindow.document.write(reportContent.outerHTML);
        printWindow.print();
        printWindow.close();
    }
    </script>
    @endpush
</div>
