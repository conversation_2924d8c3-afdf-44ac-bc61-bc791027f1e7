<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('classperiod')->default('30');
            $table->string('perpage')->default('10');
            $table->integer('tax')->unsigned();
            $table->integer('safe')->unsigned();
            $table->string('meeting_app')->default('zoom');
            $table->decimal('saudirate')->default(1.0);
            $table->decimal('egyptrate')->default(0.0);
            $table->decimal('uaerate')->default(0.0);
            $table->decimal('kuwaitrate')->default(0.0);
            $table->decimal('qatarrate')->default(0.0);
            $table->decimal('omanrate')->default(0.0);
            $table->decimal('usdrate')->default(0.0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
