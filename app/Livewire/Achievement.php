<?php

namespace App\Livewire;

use App\Models\Achievements;
use App\Models\Settings;
use App\Models\User;
use Livewire\Attributes\Computed;
use Livewire\WithPagination;
use App\Traits\UseFilter;

use Livewire\Component;

class Achievement extends Component
{
    use WithPagination, UseFilter;

    public $selectedachievements = [];
    public $selectall = false;

    public $achieve_id;
    public $student_id;
    public $teacher_id;
    public $name = '';
    public $description = '';
    public $notes = '';

    // default data properties
    public $allstudents = [];
    public $allteachers = [];


    public function save()
    {
        $validated = $this->validate([
            'student_id' => 'required|integer',
            'teacher_id' => 'required|integer',
            'name' => 'required|string|max:150',
            'description' => 'required|string|max:500',
            'notes' => 'string|max:500',
        ]);

        Achievements::create($validated);

        $this->closemodal('add');
        $this->dispatch('flashsaved');

    }
    public function showedit(Achievements $achieve)
    {
        $this->achieve_id = $achieve->id;
        $this->student_id = $achieve->student_id;
        $this->teacher_id = $achieve->teacher_id;
        $this->name = $achieve->name;
        $this->description = $achieve->description;
        $this->notes = $achieve->notes;

        $this->openmodal('edit');
    }
    public function update()
    {
        $validated = $this->validate([
            'student_id' => 'required|integer',
            'teacher_id' => 'required|integer',
            'name' => 'required|string|max:500',
            'description' => 'required|string|max:50',
            'notes' => 'string|max:500',
        ]);

        Achievements::find($this->achieve_id)->update($validated);

        $this->closemodal('edit');
        $this->dispatch('flashupdated');

    }
    public function showdelete(Achievements $achieve)
    {
        $this->achieve_id = $achieve->id;
        $this->name = $achieve->name;

        $this->openmodal('delete');
    }
    public function delete(){

        Achievements::find($this->achieve_id)->delete();

        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }
    public function deleteselected()
    {
        Achievements::whereIn('id', $this->selectedachievements)->delete();
        $this->selectedachievements = [];
        $this->closemodal('deleteselected');
    }


    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedachievements = Achievements::paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedachievements = [];
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'achieve_id',
            'student_id',
            'teacher_id',
            'name',
            'description',
            'notes',
            'allstudents',
            'allteachers',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function redirectToUrl($url)
    {
        $this->dispatch('open-new-window', $url);
    }
    public function mount()
    {
        $this->allstudents = User::filterrole('student')->get();
        $this->allteachers = User::filterrole('teacher')->get();

        if(empty($this->perpage))
        {
            $this->perpage = Settings::find(1)->perpage;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'DESC';
    }
    public function render()
    {
        return view('livewire.achievement', [
            'allachievements' => Achievements::filterteacher($this->filterteacher)
            ->filterstudent($this->filterstudent)
            ->search($this->search)
            ->paginate($this->perpage),
        ]);
    }
}
