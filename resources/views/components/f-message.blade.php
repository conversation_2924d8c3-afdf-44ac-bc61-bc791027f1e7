@props(['name'])

<div x-data="{ shown: false, name: '{{ $name }}' }"
    x-show.transition.out.opacity.duration.1500ms="shown"
    x-transition:leave.opacity.duration.1500ms
    x-on:flash-message.window="$event.detail.name === '{{ $name }}' ? show = true : null"
    x-effect="setTimeout(() => shown = false , 3000)"
    style="display: none;"
    {{ $attributes->merge(['class' => 'text-lg text-white']) }}>
    {{ $slot }}
</div>
