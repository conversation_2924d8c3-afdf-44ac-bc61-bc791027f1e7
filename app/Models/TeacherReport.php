<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeacherReport extends Model
{
    use HasFactory;

    protected $table = 'teacher_reports';

    protected $fillable = [
        'class_id',
        'user_id',
        'recite_eval',
        'revision_eval',
        'revision',
        'recite',
    ];

    public function clss()
    {
        return $this->belongsTo(Clss::class, 'class_id', 'id');
    }

    public function teacher()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
