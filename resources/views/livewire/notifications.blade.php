<div>

    {{-- delete selected modal --}}
    <x-g-modal name="notification">
        <div class="bg-white dark:bg-gray-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">حذف المستخدمين</h1>
                <button type="button" wire:click.prevent="closemodal('notification')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <div class="mb-4">
                @unless ($allnotifications->count() == 0)
                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl">
                    <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                      <tr>
                        <th scope="col" class=" table-th text-xl font-medium">الرسالة</th>
                        <th scope="col" class=" table-th text-xl font-medium">
                            <button type="button" wire:click.prevent="notiseenall({{ auth()->user()->id }})" class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">قراءة الكل</button></th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                      @foreach ($allnotifications as $allnotification)
                      <tr wire:key="noti-{{ $allnotification->id }}" class=" {{ $allnotification->is_seen ? '' : ' bg-red-500 bg-opacity-70' }}">
                        <td class="table-td text-xl font-normal">
                            <span class=" text-slate-600 dark:text-white capitalize">{{ $this->titletoar($allnotification->title) }}</span>
                            <div>
                                <span class=" text-slate-600 dark:text-white capitalize">{{ $allnotification->message }}</span>
                            </div>
                            <div class=" text-slate-500 dark:text-white text-sm">{{ $allnotification->created_at->diffForHumans() }}</div>
                        </td>
                        <td class="table-td  text-xl font-normal">
                            <span class=" text-slate-600 dark:text-white capitalize">
                                <button type="button" wire:click.prevent="notiseen({{ $allnotification->id }}, '{{ $allnotification->url }}')" class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">مشاهدة</button>
                            </span>
                        </td>
                      </tr>
                      @endforeach
                    </tbody>
                  </table>
                <div class="flex my-4">
                    <div class="w-[100%] mr-4">{{ $allnotifications->links('vendor.livewire.tailwind') }}</div>
                </div>
                  @else
                  <div class="text-center text-xl font-bold text-[#016241]">لا يوجد تنبيهات حتى الآن.</div>
                  @endunless
            </div>
        </div>
    </x-g-modal>

    <div wire:poll.keep-alive.15s class="relative md:block hidden x-cloak">
        <button class="lg:h-[48px] lg:w-[48px] dark:bg-[#016241] dark:text-white text-slate-900 cursor-pointer rounded-full text-[20px] flex flex-col items-center justify-center" type="button" data-bs-toggle="dropdown" aria-expanded="true">
          <iconify-icon class="{{ $unseen > 0 ? 'animate-tada ' : '' }} text-white text-xl" icon="heroicons-outline:bell" style="font-size: 30px;"></iconify-icon>
          @if ($unseen > 0)
            <span class="absolute -ml-5 lg:top-0 -top-[6px] h-6 w-6 bg-red-500 text-[12px] font-semibold flex flex-col items-center justify-center rounded-full text-white z-[99]">{{ $unseen }}</span>
          @endif
        </button>
        <!-- Notifications Dropdown -->
        <div class="dropdown-menu z-10 hidden bg-white divide-y divide-slate-100 dark:divide-slate-900 shadow w-[335px] dark:bg-slate-800 border dark:border-slate-900 !top-[23px] rounded-md overflow-hidden lrt:origin-top-right rtl:origin-top-left"style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate(0px, 34px);" data-popper-placement="bottom-end">
          <div class="flex items-center justify-between py-4 px-4">
            <h3 class="text-sm font-Inter font-medium text-slate-700 dark:text-white font-cairo">الإشعارات</h3>
            <button type="button" wire:click.prevent="openmodal('notification')" class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">شاهد الكل</button>
          </div>
          <div class="divide-y divide-slate-100 dark:divide-slate-900" role="none">
            @foreach ($notifications as $notification)
                <div class=" block w-full px-4 py-2 text-sm relative{{ $notification->is_seen ? ' hover:bg-slate-700' : ' bg-red-700 hover:bg-red-500 ' }}">
                    <div class="flex ltr:text-left rtl:text-right">
                        <div class="flex-1">
                            <button type="button" wire:click.prevent="notiseen({{ $notification->id }}, '{{ $notification->url }}')" class="dark:text-white text-sm font-medium mb-1 before:w-full before:h-full before:absolute before:top-0 before:left-0">{{ $this->titletoar($notification->title) }}</button>
                            <div class="text-slate-500 dark:text-slate-200 text-xs leading-4 max-w-[300px] truncate ...">{{ substr($notification->message, 47) }}</div>
                            <div class="text-slate-400 dark:text-slate-400 text-xs mt-1">{{ $notification->created_at->diffForHumans() }}</div>
                        </div>
                    </div>
                </div>
            @endforeach
          </div>
        </div>
    </div>
</div>
