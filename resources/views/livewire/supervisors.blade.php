<div>
    {{-- view supervisor report modal --}}
    <x-g-modal name="view-supervisor-report">
        <div class="bg-white dark:bg-green-700 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">إضافة تقرير للحلقة</h1>
                <button type="button" wire:click.prevent="closemodal('view-supervisor-report')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <div class="flex justify-between gap-2">
                <div class="w-1/2">
                    <label class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">التقييم الفني</label>
                    <div class="mb-4 flex justify-start gap-2">
                        <label class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">جودة الانترنت:</label>
                        <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">{{ $internet * 10 }}%</span>
                    </div>
                    <div class="mb-4 flex justify-start gap-2">
                        <label class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">استخدام الكاميرة:</label>
                        <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">{{ $camera * 10 }}%</span>
                    </div>
                    <div class="mb-4 flex justify-start gap-2">
                        <label class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">استخدام السمارت بورد:</label>
                        <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">{{ $smartboard * 10 }}%</span>
                    </div>
                    <div class="mb-4 flex justify-start gap-2">
                        <label class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">جودة الصوت:</label>
                        <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">{{ $audio * 10 }}%</span>
                    </div>
                </div>
                <div class="w-1/2">
                    <label class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">التقييم المهني</label>
                    <div class="mb-4 flex justify-start gap-2">
                        <label  class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">خلفية المعلم وهيئته:</label>
                        <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">{{ $teachbackground * 10 }}%</span>
                    </div>
                    <div class="mb-4 flex justify-start gap-2">
                        <label class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">مكان المعلم اثناء الحلقة:</label>
                        <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">{{ $teachenvironment * 10 }}%</span>
                    </div>
                    <div class="mb-4 flex justify-start gap-2">
                        <label class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">الآداء المعني للمعلم مع الطالب:</label>
                        <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">{{ $teachrelationship * 10 }}%</span>
                    </div>
                    <div class="mb-4 flex justify-start gap-2">
                        <label class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">الآداء التعليمي للمعلم:</label>
                        <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">{{ $teacheducational * 10 }}%</span>
                    </div>
                </div>
            </div>
            <div class="mb-4">
                <label for="notes" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات:</label>
                <textarea wire:model.live="notes" name="notes" disabled class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required></textarea>
            </div>
            <div class="flex justify-between">
                <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">مجموع التقييم: {{ $this->totalevaluation() ?? 0 }} %</span>
                <div class="flex justify-end">
                    <button type="button" wire:click.prevent="closemodal('view-supervisor-report')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </div>
        </div>
    </x-g-modal>

    {{-- data tables --}}
    <div class="card">
        <header class=" block card-header noborder">
          <h4 class="text-primary-500 font-bold text-3xl">التقارير المالية للإشراف</h4>
        </header>
        <div x-data="{open: false, shown: true }" x-defer class="card-body px-6 pb-6">
          <div class="sm:grid sm:my-2 md:flex sm:place-content-center md:justify-between w-auto mr-0">
            {{-- control buttons --}}
            <div class="grid sm:grid-cols-1 lg:grid-cols-6 gap-3">
                {{-- advanced section modal button --}}
                <button type="button" x-on:click="open = ! open" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div x-show="! open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اظهار البحث الــمتقدم
                    </div>
                    <div x-show="open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اخفاء البحث الــمتقدم
                    </div>
                </button>
                @if (!empty($filterdatestart) || !empty($filterdateend) || !empty($filterteacher) || !empty($filteractive))
                    <button wire:click.prevent="resetallfilter" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div class="flex place-items-center">
                            <iconify-icon icon="heroicons:arrow-uturn-down-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                            إعادة تعيين التصفية
                        </div>
                    </button>
                @endif
            </div>
            {{-- search field --}}
            <div class="my-3 sm:w-auto sm:mt-0 sm:ml-auto md:ml-0">
                <div class="relative flex w-80 text-gray-700 mr-auto my-auto place-items-center">
                    @if (!empty($search))
                        <button type="button" wire:click.prevent="resetsearch" class="absolute inset-y-0 left-0 pl-3 flex items-center z-1">
                            <iconify-icon icon="heroicons:x-mark-20-solid" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                        </button>
                    @endif
                    <iconify-icon icon="heroicons:magnifying-glass-solid" style="font-size: 24px;color:#016241;" alt="تعديل" class="ml-2"></iconify-icon>
                    <input wire:model.live.debounce.500="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-lg rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="بحث..." wire:ignore>
                </div>
            </div>
          </div>
          {{-- advanced filter by status and teacher --}}
          <div x-show="open" class="grid sm:grid-cols-1 lg:grid-cols-2 place-items-center gap-2 my-2">
            @if (auth()->user()->role !== 'teacher')
            <div class="flex w-full place-items-center">
            <label for="filtersupervisor" class="block text-lg font-medium text-gray-700 dark:text-[#016241] ml-2">المشرف: </label>
            <select name="filtersupervisor" wire:model.live="filtersupervisor" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;" value="" selected>اختر المشرف</option>
                @foreach ( $supervisors as $supervisor)
                    <option style="direction:rtl;" value="{{ $supervisor->id }}">{{ $supervisor->name }}</option>
                @endforeach
            </select>
            @if (!empty($filtersupervisor))
            <button type="button" wire:click.prevent="resetfilter('filtersupervisor')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
            @endif
            <div class="flex w-full place-items-center">
            <label for="filteractive" class="block text-lg font-medium text-gray-700 dark:text-[#016241] ml-2 lg:mx-2">الحالة:</label>
            <select name="filteractive" wire:model.live="filteractive" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:ltr;" value="" selected>اختر الحالة</option>
                <option style="direction:ltr;" value=false>متاح</option>
                <option style="direction:ltr;" value=true>غير متاح</option>
                <option style="direction:ltr;" value="ended">منتهية</option>
            </select>
            @if (!empty($filteractive))
            <button type="button" wire:click.prevent="resetfilter('filteractive')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
          </div>
          {{-- advanced date filter --}}
          <div x-show="open" class="grid sm:grid-cols-1 lg:grid-cols-3 place-items-center gap-2 my-2 w-full">
                <div class="flex w-full justify-start place-items-center">
                    <label for="filterdatestart" class="block text-wrap text-lg font-medium text-gray-700 dark:text-[#016241] ml-2">من: </label>
                    <input type="date" id="filterdatestart" wire:model.live="filterdatestart" value="{{ $filterdatestart }}" name="filterdatestart" min="2023-01-01" max="2050-12-31" class="shadow-sm w-full rounded-md px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
                    @if (!empty($filterdatestart))
                    <button type="button" wire:click.prevent="resetfilter('filterdatestart')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 mr-2 p-1 dark:hover:bg-slate-600 dark:hover:text-white">
                        <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                    </button>
                    @endif
                </div>
                <div class="flex w-full justify-start place-items-center">
                    <label for="filterdateend" class="block text-lg font-medium text-gray-700 dark:text-[#016241] ml-2 lg:mx-2">حتى: </label>
                    <input type="date" id="filterdateend" wire:model.live="filterdateend" value="{{ $filterdateend }}" name="filterdateend" min="2023-01-01" max="2050-12-31" class="shadow-sm w-full rounded-md px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
                    @if (!empty($filterdateend))
                    <button type="button" wire:click.prevent="resetfilter('filterdateend')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 mr-2 p-1 dark:hover:bg-slate-600 dark:hover:text-white">
                        <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                    </button>
                    @endif
                </div>
            <div class="grid sm:grid-cols-1 lg:grid-cols-3 gap-3 w-full">
                {{-- filter day/week/month button --}}
                <button type="button" wire:click.prevent="filterbyday" class="{{ $filterby == 'today' ? 'bg-[#b36700]' : 'bg-[#906842]' }} hover:bg-[#b36700] text-white font-bold py-4 px-4 rounded-lg place-content-center">
                    <iconify-icon icon="heroicons:calendar-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    حلقات اليوم
                </button>
                <button type="button" wire:click.prevent="filterbyweek" class="{{ $filterby == 'week' ? 'bg-[#b36700]' : 'bg-[#906842]' }} hover:bg-[#b36700] text-white font-bold py-4 px-4 rounded-lg place-content-center">
                    <iconify-icon icon="heroicons:calendar-days-16-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    حلقات الاسبوع
                </button>
                <button type="button" wire:click.prevent="filterbymonth" class="{{ $filterby == 'month' ? 'bg-[#b36700]' : 'bg-[#906842]' }} hover:bg-[#b36700] text-white font-bold py-4 px-4 rounded-lg align-middle mr-0 flex place-content-center">
                    <iconify-icon icon="heroicons:calendar-days-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    حلقات الشهر
                </button>
            </div>
          </div>
          {{-- data table --}}
          <div class="overflow-x-auto -mx-6">
            <div class="inline-block min-w-full align-middle">
              <div class="overflow-hidden">
                <div class="text-lg font-medium text-white">{{ $filterdatestart }}, {{ $filterdateend }}</div>
                @unless ($classes->count() == 0)
                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl">
                  <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                    <tr>
                        @if (auth()->user()->role !== 'student')
                        <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('username')">اسم الطالب</a></th>
                        @endif
                        @if (auth()->user()->role !== 'teacher')
                        <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('name')">اسم المعلم</a></th>
                        @endif
                        <th scope="col" class=" table-th text-xl font-medium">وقت الحصة</th>
                        <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">وقت دخول المعلم</th>
                        <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">وقت دخول المشرف</th>
                        <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">حضور المعلم</th>
                        <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">حضور الطالب</th>
                        @if (auth()->user()->role == 'supervisor' || auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                        <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">الحالة</th>
                        @endif
                        <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">التقييم</th>
                        <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">التقرير</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                    @foreach ($classes as $class)
                    <tr wire:key="class-{{ $class->id }}">
                      @if (auth()->user()->role !== 'student')
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $class->student->name }}</span>
                      </td>
                      @endif
                      @if (auth()->user()->role !== 'teacher')
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $class->teacher->name }}</span>
                      </td>
                      @endif
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $class->date_time }}</span>
                      </td>
                      <td class=" text-center text-lg font-medium text-[#cbd5e1]">
                        <span class=" text-slate-600 dark:text-white capitalize">
                            {{ $class->clssreport->where('loggedin_user_role', 'teacher')->first()->loggedin_time ?? 'لم يتم تسجيل الدخول' }}
                        </span>
                      </td>
                      <td class=" text-center text-lg font-medium text-[#cbd5e1]">
                        <span class=" text-slate-600 dark:text-white capitalize">
                            {{ $class->clssreport->where('loggedin_user_role', 'supervisor')->first()->loggedin_time ?? 'لم يتم تسجيل الخروج' }}
                        </span>
                      </td>
                      <td class=" text-center text-lg font-medium text-[#cbd5e1]">
                        <span class=" text-slate-600 dark:text-white capitalize">
                            {{ $class->clssreport->where('loggedin_user_role', 'teacher')->first()->loggedinduration ?? 'لم يتم تسجيل الدخول' }}
                            @php
                                $teacherloggedinduration += $class->clssreport->where('loggedin_user_role', 'teacher')->first()->loggedinduration ?? 0;
                            @endphp
                        </span>
                      </td>
                      <td class=" text-center text-lg font-medium text-[#cbd5e1]">
                        <span class=" text-slate-600 dark:text-white capitalize">
                            {{ $class->clssreport->where('loggedin_user_role', 'student')->first()->loggedinduration ?? 'لم يتم تسجيل الدخول' }}
                        </span>
                      </td>
                      @if (auth()->user()->role == 'supervisor' || auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                      <td class="table-td text-xl font-normal">
                        @if ($class->is_active === true)
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border border-[#016241] text-white bg-danger-500">
                                غير متاح
                        @elseif ($class->is_active === false)
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border border-[#016241] text-white bg-success-500">
                                متاح
                        @else
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border border-[#016241] text-white bg-success-500">
                                منتهية
                        @endif
                        </div>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $this->totalevaluation($class->supreport) ?? 0 }}%</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">
                            @if (!empty($class->supreport->class_id))
                            <button type="button" wire:click.prevent="suprepview({{ $class }})" class="border border-[#016241] bg-yellow-500 hover:bg-yellow-700 text-slate-900 text-sm font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">عرض التقرير</a>
                            @else
                            <span class=" text-slate-600 dark:text-white text-lg capitalize mx-auto">لا يوجد تقرير</span>
                            @endif
                        </span>
                      </td>
                      @endif
                    </tr>
                    @endforeach
                  </tbody>
                </table>
                @else
                <div class="text-center text-xl font-bold text-[#016241]">لا يوجد تقارير حتى الآن.</div>
                @endunless
              </div>
            </div>
          </div>
        </div>
    </div>
    <div class="flex my-4">
        <div class="flex justify-start w-[50%] items-center">
            <label for="perpage" class="text-lg font-medium text-gray-700 dark:text-[#be9539]">عدد النتائج في كل صفحة:</label>
            <select wire:model.live="perpage" id="role" style="direction:ltr;" class="w-20 shadow-sm rounded-md mx-4 px-5 py-1 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;" value="10">10</option>
                <option style="direction:rtl;" value="20">20</option>
                <option style="direction:rtl;" value="30">30</option>
                <option style="direction:rtl;" value="50">50</option>
                <option style="direction:rtl;" value="2000">الكل</option>
            </select>
        </div>
        <div class="w-[100%] mr-4">{{ $classes->links('vendor.livewire.tailwind') }}</div>
    </div>
</div>
