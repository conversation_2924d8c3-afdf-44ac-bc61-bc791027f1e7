<?php

namespace App\Traits;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Spatie\GoogleCalendar\Event;


trait UseGoogle {

    public $createMeeting = '';
    public $updateMeeting = '';
    public $deleteMeeting = '';
    public $responseData = [];

    public function index()
    {
        // get all future events on a calendar
        $events = Event::get();
        return $events;
    }
    public function createMeeting($user, $student, $start_time)
    {
        //create a new event
        // $event = new Event;

        // $event->name = 'A new event';
        // $event->description = 'Event description';
        // $event->startDateTime = Carbon::now();
        // $event->endDateTime = Carbon::now()->addHour();
        // $event->addAttendee([
        //     'email' => '<EMAIL>',
        //     'name' => '<PERSON>',
        //     'comment' => 'Lorum ipsum',
        //     'responseStatus' => 'needsAction',
        // ]);
        // $event->addAttendee(['email' => '<EMAIL>']);
        // $event->addMeetLink(); // optionally add a google meet link to the event

        // $event->save();

        // if ($response->successful()) {
        //     return [
        //         'success' => $response->getStatusCode() === 201,
        //         'data'    => json_decode($response->getBody(), true),
        //     ];
        // } else {
        //     return [
        //         'success' => $response->getStatusCode() === 201,
        //         'data'    => json_decode($response->getBody(), true),
        //     ];
        //     return response()->json(['error' => 'Failed to create a Zoom meeting'], 500);
        // }

        // create a new event
        if ($user == 'me')
        {
            $calendarId = env('GOOGLE_CALENDAR_ID');
            $event = Event::create([
                'name' => 'حلقة مع الطالب "' . $student . '"',
                'description' => 'Event description',
                'startDateTime' => Carbon::parse($start_time)->format('Y-m-d\TH:i:s'),
                'startTimezone' => 'Africa/Cairo',
                'endDateTime' => Carbon::parse($start_time)->addMinutes(30)->format('Y-m-d\TH:i:s'),
                'endTimezone' => 'Africa/Cairo',
                'conferenceData' => [
                    'createRequest' => [
                        'requestId' => Str::random(3).'-'.Str::random(4).'-'.Str::random(3),
                        'conferenceSolutionKey' => [
                            'type' => 'hangoutsMeet',
                        ],
                    ],
                ],
                'addAttendee' => [
                        'email' => $student->email,
                        'name' => $student->name,
                        'responseStatus' => 'needsAction',
                    ],
            ], $calendarId, ['sendNotifications' => true, 'conferenceDataVersion' => 1]);
            $this->responseData = response()->json($event);
        } else {
            $this->createMeeting = 'invalid user ID';
        }
    }
    public function listMeetings()
    {
        $events = Event::get();
        // return $events;
        echo count($events) . '<br>';
        foreach ($events as $meeting)
        {
            echo $meeting->id . '<br>';
            echo $meeting->name . '<br>';
            echo $meeting->description . '<br>';
            echo $meeting->startDateTime . '<br>';
            echo $meeting->endDateTime . '<br>';
            if(!empty($meeting->attendees))
            {
                foreach ($meeting->attendees as $participant)
                {
                    echo $participant->name . '<br>';
                    echo $participant->email . '<br>';
                    echo $participant->comment . '<br>';
                }
            }
            echo ' ----------------------------- <br>';
        }
    }
    public function meetingInfo($meeting)
    {
        $event = Event::find($meeting);
        return response()->json($event);
    }
    public function updateMeeting($meeting, $start_time)
    {
        $event = Event::find($meeting);

        // update existing event
        // $event->name = 'updated name';
        // $event->save();

        $event->update([
            'startDateTime' => Carbon::parse($start_time)->format('Y-m-d\TH:i:s'),
            'startTimezone' => 'Africa/Cairo',
            'endDateTime' => Carbon::parse($start_time)->addMinutes(30)->format('Y-m-d\TH:i:s'),
            'endTimezone' => 'Africa/Cairo',
        ]);
    }
    public function deleteMeeting($meeting)
    {
        $event = Event::find($meeting);
        // delete an event
        $event->delete();
    }
}
