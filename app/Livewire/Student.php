<?php

namespace App\Livewire;

use App\Models\Achievements;
use App\Models\Banks;
use App\Models\Certificate;
use App\Models\Clss;
use App\Models\Expenses;
use App\Models\ExpensesSubcategory;
use App\Models\Notification;
use App\Models\ReferalCode;
use App\Models\SalesReport;
use App\Models\Settings;
use App\Models\Student as ModelsStudent;
use App\Models\Subscriptions;
use App\Models\User;
use App\Traits\UseDefault;
use App\Traits\UseFilter;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;

class Student extends Component
{
    use WithPagination, UseFilter, UseDefault;

// from user model
    public $studentid = '';
    public $name = '';
    public $username = '';
    public $password = '';
    public $email = '';
    public $phone = '';
    public $country = '';
    public $showpass = false;
    public $status;

// from student model
    public $studentuserid = '';
    public $dateofbirth;
    public $subscription = '';
    public $subscription_id;
    public $subscription_name;
    public $gender_prefer = '';
    public $paid = 0;
    public $bank_id;
    public $trans_code;
    public $convertedpaid = 0;
    public $numofclasses = '';
    public $oldnumofclasses = '';
    public $ref_code = '';
    public $inv_code = '';
    public $balance = 0;
    public $refcode_active_date = null;
    public $startdate = '';
    public $reg_status = '';
    public $bankname = '';

    public $chosensubscription;
    public $tax;

    // fixed in this component
    public $role = 'student';
    public $listallusers = [];
    public $methodType = 'save';
    public $banks = [];
    public $listsubscriptions = [];
    public $listcertifications = [];
    public $listachievements = [];

// selected / select all
    public $selectedstudents = [];
    public $selectall = false;
    public $showbanks = false;
    public $currency = '';

//
    public function save()
    {
        try {
        $salesreport = false;
        $validateduser = $this->validate([
            'name' => 'required|string|min:3|max:100',
            'username' => 'required|regex:/^[a-zA-Z0-9]+$/|min:3|max:100|unique:user,username',
            'email' => 'required|email|min:5|max:100|unique:user,email',
            'password' => 'required|string|min:8|max:25',
            'phone' => 'required|string|min:11|max:20',
            'country' => 'required|string|min:3|max:40',
            'status' => 'required|boolean',
        ]);
        if ($this->subscription == 'fixed')
        {
            $validatedstudent = $this->validate([
            'dateofbirth' => 'required|date',
            'gender_prefer' => 'required',
            'subscription' => 'required|string|max:10',
            'subscription_id' => 'required|numeric|max:100',
            'paid' => 'required|numeric',
            'bank_id' => 'required|numeric',
            'trans_code' => 'string|min:3|max:25',
            'numofclasses' => 'required|numeric|min:1|max:100',
            'ref_code' => 'string|min:7|max:9',
            'balance' => 'required|numeric|min:0',
            'startdate' => 'required|date',
            ]);
            $this->convertedpaid = $validatedstudent['paid'] / $this->convertPaidToSAR($validateduser['country']);
            $salesreport = true;
        } elseif ($this->subscription == 'trial')
        {
            $validatedstudent = $this->validate([
            'dateofbirth' => 'required|date',
            'gender_prefer' => 'required',
            'subscription' => 'required|string|max:10',
            'paid' => 'numeric',
            'numofclasses' => 'required|numeric|min:1|max:3',
            'startdate' => 'required|date',
            ]);
            $validatedstudent['subscription_id'] = null;
            $this->bank_id = null;
        } else {
            $validatedstudent = $this->validate([
            'dateofbirth' => 'required|date',
            'gender_prefer' => 'required',
            'subscription' => 'required|string|max:10',
            'paid' => 'required|numeric',
            ]);
            $validatedstudent['subscription_id'] = null;
            $this->bank_id = null;
        }

        $validateduser['role'] = $this->role;
        $validateduser['password'] = Hash::make($validateduser['password']);
        // dd($validatedstudent, $validateduser);

        // $this->convertedpaid = $validatedstudent['paid'] / $this->convertPaidToSAR($validateduser['country']);
        // dd($validatedstudent['paid'], $this->convertPaidToSAR($validateduser['country']), $this->convertedpaid);

        if($validateduser && $validatedstudent)
        {
                $user = User::create($validateduser);
                ModelsStudent::create([
                    'user_id' => $user->id,
                    'dateofbirth' => $validatedstudent['dateofbirth'],
                    'gender_prefer' => $validatedstudent['gender_prefer'],
                    'subscription' => $validatedstudent['subscription'],
                    'subscription_id' => $validatedstudent['subscription_id'],
                    'paid' => $this->convertedpaid,
                    'bank_id' => $this->bank_id,
                    'numofclasses' => $validatedstudent['numofclasses'],
                    'ref_code' => $validatedstudent['ref_code'] ?? '',
                    'balance' => $this->balance,
                    'refcode_active_date' => Carbon::today('Africa/Cairo')->format('Y-m-d'),
                    'startdate' => $validatedstudent['startdate'],
                    'reg_status' => 'new',
                ]);

                if($salesreport)
                {
                    $paidtax = $this->convertedpaid * ($this->tax / 100);
                    SalesReport::create([
                        'sales_id' => auth()->user()->id,
                        'student_id' => $user->id,
                        'subscription' => $validatedstudent['subscription'],
                        'paid' => $this->convertedpaid - $paidtax,
                        'bank_id' => $this->bank_id,
                        'trans_code' => $validatedstudent['trans_code'],
                        'numofclasses' => $validatedstudent['numofclasses'],
                        'startdate' => $validatedstudent['startdate'],
                        'reg_status' => 'new',
                        'lastupdateuser_id' => auth()->user()->id,
                    ]);

                    $bank = Banks::find($this->bank_id);
                    $bankbalance = $bank->balance;
                    $bank->update([
                        'balance' => $bankbalance + $validatedstudent['paid'],
                    ]);
                    $expsubcate_id = ExpensesSubcategory::where('name', $user->country)->first()->id;
                    Expenses::create([
                        'name' => 'ضرائب الطالب ' . $user->name,
                        'category_id' => 3,
                        'subcategory_id' => $expsubcate_id,
                        'date' => $validatedstudent['startdate'],
                        'amount' => $paidtax,
                        'lastupdateuser_id' => auth()->user()->id,
                    ]);
                }

                if(!empty($this->ref_code))
                {
                    $refcode = ReferalCode::where('short_code', $this->ref_code)->first();
                    $addusedby = json_decode($refcode->used_by);
                    array_push($addusedby, $user->id);
                    // dd($addusedby);

                    $refcode->update([
                        'used_by' => json_encode($addusedby),
                    ]);
                }

                $notifiedusers = User::whereIn('role', ['superadmin', 'admin', 'moderator'])->pluck('id');
                foreach ($notifiedusers as $notifieduser) {
                    Notification::create([
                        'user_id' => $notifieduser,
                        'title' => 'new student',
                        'url' => 'student?q=' . $user->name,
                        'message' => 'تم اضافة طالب جديد "' . $user->name  . '" بواسطة "' . auth()->user()->name . '".',
                    ]);
                }

                $this->closemodal('add');
                $this->dispatch('flashsaved');
        } else {
            dd('something wrong');
        }
        } catch (\Illuminate\Validation\ValidationException $e) {
            dd('Validation failed', $e->errors());
        } catch (\Exception $e) {
            dd('General error:', $e->getMessage());
        }
    }

    public function showview(User $user)
    {
        $this->studentid = $user->id;
        $this->name = $user->name;
        $this->username = $user->username;
        $this->email = $user->email;
        $this->phone = $user->phone;
        $this->country = $user->country;

        $student = ModelsStudent::where('user_id', $user->id)->first();
        // dd($student->bank);
        $this->dateofbirth = carbon::parse($student->dateofbirth)->age;
        $this->gender_prefer = $student->gender_prefer == 'male' ? 'معلم' : 'معلمة';
        $this->subscription = $student->subscription;
        $this->subscription_name = Subscriptions::find($student->subscription_id)->name ?? 'غير مشترك';
        $this->paid = $student->paid;
        $this->bankname = $student->bank->name ?? 'لا يوجد بنك مسجل';
        $this->numofclasses = $student->numofclasses ?? 0;
        $this->balance = $student->balance;
        $this->ref_code = $student->ref_code;
        $this->inv_code = $student->inv_code;
        $this->startdate = $student->startdate;
        $this->reg_status = $student->reg_status;

        $this->listcertifications = Certificate::filterstudent($this->studentid)->get();
        $this->listachievements = Achievements::filterstudent($this->studentid)->get();

        $this->openmodal('view');
    }

    public function showedit(User $user)
    {
        $this->studentid = $user->id;
        $this->name = $user->name;
        $this->username = $user->username;
        $this->email = $user->email;
        $this->phone = $user->phone;
        $this->country = $user->country;

        $student = ModelsStudent::where('user_id', $user->id)->first();
        if($student->subscription == 'fixed')
        {
            $this->showbanks = true;
        } else {
            $this->showbanks = false;
        }
        // dd($student->bank->name ?? '');
        $this->dateofbirth = $student->dateofbirth;
        $this->gender_prefer = $student->gender_prefer;
        $this->subscription = $student->subscription;
        $this->subscription_id = $student->subscription_id;
        $this->paid = $student->paid;
        $this->bank_id = $student->bank_id;
        $this->bankname = $student->bank->name ?? '';
        $this->numofclasses = $student->numofclasses;
        $this->balance = $student->balance;
        $this->ref_code = $student->ref_code;
        $this->inv_code = $student->inv_code;
        $this->startdate = $student->startdate;
        $this->reg_status = $student->reg_status;

        $this->methodType = 'update';
        $this->openmodal('edit');
    }
    public function update()
    {
        $salesreport = false;
        if(!empty($this->password))
        {
            $validateduser = $this->validate([
                'name' => 'required|string|min:3|max:100',
                'username' => 'required|regex:/^[a-zA-Z0-9]+$/|min:3|max:100',
                'email' => 'required|email|min:5|max:100',
                'password' => 'required|string|min:8|max:25',
                'phone' => 'required|string|min:11|max:20',
                'country' => 'required|string|min:3|max:40',
                'role' => '',
            ]);
            $validateduser['password'] = Hash::make($validateduser['password']);

        } else {
            $validateduser = $this->validate([
                'name' => 'required|string|min:3|max:100',
                'username' => 'required|regex:/^[a-zA-Z0-9]+$/|min:3|max:100',
                'email' => 'required|email|min:5|max:100',
                'password' => '',
                'phone' => 'required|string|min:11|max:20',
                'country' => 'required|string|min:3|max:40',
                'role' => '',
            ]);
        }
        if ($this->subscription == 'fixed')
        {
            $salesreport = true;
            $validatedstudent = $this->validate([
            'dateofbirth' => 'required|date',
            'gender_prefer' => 'required|string|min:3|max:10',
            'subscription' => 'required|string|max:10',
            'subscription_id' => 'required|numeric|max:100',
            'paid' => 'required|numeric',
            'bank_id' => 'required|numeric',
            'trans_code' => 'string|min:3|max:25',
            'numofclasses' => 'required|numeric|min:1|max:100',
            'ref_code' => 'string|min:7|max:9',
            'balance' => 'numeric|min:0',
            'startdate' => 'required|date',
            'reg_status' => 'required|string|max:120',
            ]);
            $this->convertedpaid = $validatedstudent['paid'] / $this->convertPaidToSAR($validateduser['country']);

            if (!empty($this->inv_code))
            {
                $invcodestart = '';
                $userid = '';
                for ($i=0;$i<strlen($this->inv_code);$i++) {
                    if (ctype_alpha($this->inv_code[$i]))
                    {
                        $invcodestart .= $this->inv_code[$i];
                    }elseif (ctype_digit($this->inv_code[$i]))
                    {
                        $userid .= $this->inv_code[$i];
                    }
                }
                // dd($invcodestart, $userid);
                $getinvitecode = ReferalCode::where('short_code', 'like', '%' . $invcodestart . '%')->first();
                $getinviteuser = User::find($userid);
                $newbalance = $this->paid * ($getinvitecode->discount/100);
                // dd($invcodestart, $userid, $newbalance, $getinviteuser->student->balance);
                $getinviteuser->student->update([
                    'balance' => $getinviteuser->student->balance + $newbalance,
                ]);
            } else {
                $this->inv_code = '';
            }
        } elseif ($this->subscription == 'trial')
        {
            $validatedstudent = $this->validate([
            'dateofbirth' => 'required|date',
            'gender_prefer' => 'required|string|min:3|max:10',
            'subscription' => 'required|string|max:10',
            'paid' => '',
            'numofclasses' => 'required|numeric|min:1|max:3',
            'startdate' => 'required|date',
            'reg_status' => 'required|string|max:120',
            ]);
            $validatedstudent['subscription_id'] = null;
            $this->bank_id = null;
        } else {
            $validatedstudent = $this->validate([
            'dateofbirth' => 'required|date',
            'gender_prefer' => 'required|string|min:3|max:10',
            'subscription' => 'required|string|max:10',
            'paid' => 'required|numeric',
            'numofclasses' => '',
            'startdate' => '',
            'reg_status' => 'required|string|max:120',
            ]);
            $validatedstudent['subscription_id'] = null;
            $this->bank_id = null;
        }
        // dd($validatedstudent);
        $user = User::find($this->studentid);
        $student = ModelsStudent::where('user_id', $this->studentid)->first();
        // $refcodes = '';
        // dd($validatedstudent['ref_code'], $student->ref_code);

        // if($validatedstudent['ref_code'] != $student->ref_code || !str_contains($student->ref_code, $validatedstudent['ref_code']))
        // {
        //     $refcode = ReferalCode::where('short_code', $this->ref_code)->first();
        //     $addusedby = json_decode($refcode->used_by);
        //     array_push($addusedby, $user->id);

        //     $refcodes = $student->ref_code . ', "' . $validatedstudent['ref_code'] . '"';
        //     // dd($addusedby);

        //     $refcode->update([
        //         'used_by' => json_encode($addusedby),
        //     ]);
        // }elseif ($validatedstudent['ref_code'] === $student->ref_code || str_contains($student->ref_code, $validatedstudent['ref_code']))
        // {
        //     $refcodes = $student->ref_code;
        // }

        if($validateduser && $validatedstudent)
        {
            $user->update($validateduser);
            $student->update([
                'dateofbirth' => $validatedstudent['dateofbirth'],
                'gender_prefer' => $validatedstudent['gender_prefer'],
                'subscription' => $validatedstudent['subscription'],
                'subscription_id' => $validatedstudent['subscription_id'],
                'paid' => $validatedstudent['paid'],
                'bank_id' => $this->bank_id,
                'numofclasses' => $validatedstudent['numofclasses'],
                'ref_code' => $validatedstudent['ref_code'] ?? '',
                'balance' => $this->balance,
                'refcode_active_date' => $this->refcode_active_date,
                'startdate' => $validatedstudent['startdate'],
                'reg_status' => $validatedstudent['reg_status'],
            ]);
            $notifiedusers = User::whereIn('role', ['superadmin', 'admin', 'moderator'])->pluck('id');
            foreach ($notifiedusers as $notifieduser) {
                Notification::create([
                    'user_id' => $notifieduser,
                    'title' => 'student updated',
                    'url' => 'student?q=' . $user->email,
                    'message' => 'تم تعديل الطالب "' . $user->name . '" بواسطة "' . auth()->user()->name . '".',
                ]);
            }
            if($salesreport)
            {
                SalesReport::create([
                    'sales_id' => auth()->user()->id,
                    'student_id' => $this->studentid,
                    'subscription' => $validatedstudent['subscription'],
                    'paid' => $this->convertedpaid,
                    'bank_id' => $this->bank_id,
                    'trans_code' => $validatedstudent['trans_code'],
                    'numofclasses' => $validatedstudent['numofclasses'],
                    'startdate' => $validatedstudent['startdate'],
                    'reg_status' => 'resub',
                    'lastupdateuser_id' => auth()->user()->id,
                ]);

                $bank = Banks::find($this->bank_id);
                $bankbalance = $bank->balance;
                $bank->update([
                    'balance' => $bankbalance + $validatedstudent['paid'],
                ]);

            }
        }
        $this->closemodal('edit');
        $this->dispatch('flashupdated');
    }
    public function showdelete(User $student)
    {
        $this->studentid = $student->id;
        $this->name = $student->name;
        $this->openmodal('delete');
    }
    public function delete()
    {
        $user = User::find($this->studentid);
        $notifiedusers = User::where('role', 'admin')->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'student deleted',
                'url' => 'student',
                'message' => 'تم حذف الطالب "' . $user->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }
        $user->delete();

        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
        // $this->flashmessage('deleteselected');
    }
    public function deleteselected()
    {
        User::query()->whereIn('id', $this->selectedstudents)->delete();

        $this->selectedstudents = [];
        $this->selectall = false;
        $this->closemodal('deleteselected');
        // $this->flashmessage('flashdeleted');
    }
    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedstudents = $this->perpage === 'all' ? User::search($this->search)
            ->filterrole($this->role)
            ->filtercountry($this->filtercountry)
            ->filteractive($this->filteractive)
            ->filtersubscription($this->filtersubscription)
            ->orderBy($this->sortBy, $this->sortDir)
            ->pluck('id') :
            User::search($this->search)
            ->filterrole($this->role)
            ->filtercountry($this->filtercountry)
            ->filteractive($this->filteractive)
            ->filtersubscription($this->filtersubscription)
            ->orderBy($this->sortBy, $this->sortDir)
            ->paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedstudents = [];
        }
    }
    #[Computed]
    public function updatedsubscription()
    {
        if($this->subscription === 'fixed')
        {
            $this->showbanks = true;
        } else {
            $this->showbanks = false;
            $this->bank_id = '';
        }
    }
    #[Computed]
    public function updated($prop)
    {
        if($prop == 'subscription_id')
        {
            $this->chosensubscription = Subscriptions::find($this->subscription_id);
            $this->numofclasses = $this->chosensubscription->numofclasses;
            $this->paid = $this->getpricefromcountry($this->country);
        } elseif ($prop == 'country')
        {
            $this->paid = $this->getpricefromcountry($this->country);
        }
    }
    // #[Computed]
    // public function updatedref_code()
    // {
    //     if(!empty($this->ref_code))
    //     {
    //         $refcode = ReferalCode::getdiscount($this->ref_code)->first();
    //         // dd($refcode->discount);
    //         $this->balance = $this->paid * ($refcode->discount/100);
    //     } else {
    //         $this->balance = 0;
    //     }
    // }
    // #[Computed]
    // public function updatedpaid()
    // {
    //     if(!empty($this->paid))
    //     {
    //         $refcode = ReferalCode::getdiscount($this->ref_code)->first();
    //         // dd($refcode->discount);
    //         $this->balance = $this->paid * ($refcode->discount/100);
    //     }
    // }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'studentid',
            'name',
            'username',
            'email',
            'password',
            'role',
            'phone',
            'country',
            'studentuserid',
            'dateofbirth',
            'subscription',
            'gender_prefer',
            'paid',
            'bank_id',
            'numofclasses',
            'ref_code',
            'balance',
            'startdate',
            'reg_status',
            'showbanks',
            'bankname',
            'subscription_id',
            'chosensubscription',
            'currency',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }

    public function checkifapplied($user)
    {
        if (!empty(Clss::where('student_id', $user->id)->get())) {
            return true;
        } else {
            return false;
        }
    }
    public function getsubscriptiontitle($subscription)
    {
        switch ($subscription) {
            case 'trial':
                return 'تجريبي';
                break;

            case 'fixed':
                return 'مشترك';
                break;

            default:
                return 'منتهي';
                break;
        }
    }
    public function getregstatustitle($reg_status)
    {
        switch ($reg_status) {
            case 'new':
                return 'عضو جديد';
                break;

            case 'resub':
                return 'تجديد';
                break;

            default:
                return 'منتهي';
                break;
        }
    }
    public function getpricefromcountry($country)
    {
        switch ($country) {
            case 'المملكة العربية السعودية':
                $this->currency = 'ريال سعودي';
                return $this->chosensubscription->amount_sar ?? 0;
                break;

            case 'مصر':
                $this->currency = 'جنية مصري';
                return $this->chosensubscription->amount_egp ?? 0;
                break;

            case 'الإمارات العربية المتحدة':
                $this->currency = 'درهم إماراتي';
                return $this->chosensubscription->amount_aed ?? 0;
                break;

            case 'الكويت':
                $this->currency = 'دينار كويتي';
                return $this->chosensubscription->amount_kwd ?? 0;
                break;

            case 'قطر':
                $this->currency = 'ريال قطري';
                return $this->chosensubscription->amount_qar ?? 0;
                break;

            case 'عمان':
                $this->currency = 'ريال عُماني';
                return $this->chosensubscription->amount_omr ?? 0;
                break;

            default:
                $this->currency = 'دولار امريكي';
                return $this->chosensubscription->amount_usd ?? 0;
                break;
        }
    }
    public function mount()
    {
        $this->settings = Settings::find(1);
        $this->perpage = $this->settings->perpage;
        $this->tax = $this->settings->tax;
        $this->listallusers = User::filterrole($this->role)->pluck('name', 'id');
        $this->sortBy = 'created_at';
        $this->sortDir = 'DESC';
        $this->reset([
            'ref_code',
            'balance',
        ]);
        $this->listsubscriptions = Subscriptions::all();
    }

    public function render()
    {
        // dd($this->listallusers);
        $student = ModelsStudent::where('user_id', $this->studentid)->first();
        $refcode = ReferalCode::getdiscount($this->ref_code)->first();
        $this->banks = Banks::filtercountry($this->country)->get();
        if(empty($this->balance))
        {
            $this->balance = 0;
        }
        if(!empty($this->paid) && !empty($this->ref_code))
        {
            if(!empty($refcode))
            {
                if ($this->methodType == 'update') {
                    if (!empty($this->ref_code) || $this->paid > 0)
                    {
                        if($this->ref_code !== $student->ref_code)
                        {
                            if($this->paid > 0 || !empty($this->paid) && ctype_digit($this->paid))
                            {
                                $this->balance += floor(ctype_digit($this->paid) ? $this->paid : 0 * ($refcode->discount/100));
                            } else {
                                $this->balance = 0;
                            }
                        } else {
                            $this->balance = $student->balance;
                        }
                    } else {
                        $this->balance = $student->balance;
                    }
                }elseif ($this->methodType == 'save')
                {
                    if (!empty($this->ref_code) || $this->paid > 0)
                    {
                        $this->balance = floor($this->paid * ($refcode->discount/100));
                        $this->refcode_active_date = Carbon::today('Africa/Cairo')->format('Y-m-d');
                    } else {
                        $this->balance = $student->balance;
                    }
                }
            } else {
                $this->balance = $student->balance ?? 0;
                $this->refcode_active_date = $student->refcode_active_date ?? null;
            }
        }
        $students = $this->perpage === 'all' ? User::search($this->search)
        ->filterrole($this->role)
        ->filtercountry($this->filtercountry)
        ->filteractive($this->filteractive)
        ->filtersubscription($this->filtersubscription)
        ->filterinvcode($this->filterinvcode)
        ->orderBy($this->sortBy, $this->sortDir)
        ->get() :
        User::search($this->search)
        ->filterrole($this->role)
        ->filtercountry($this->filtercountry)
        ->filteractive($this->filteractive)
        ->filtersubscription($this->filtersubscription)
        ->filterinvcode($this->filterinvcode)
        ->orderBy($this->sortBy, $this->sortDir)
        ->paginate($this->perpage);
        return view('livewire.student', [
            'students' => $students,
        ]);
    }
}
