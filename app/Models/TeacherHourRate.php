<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeacherHourRate extends Model
{
    use HasFactory;

    protected $table = 'teacher_hour_rates';
    protected $fillable = [
        'user_id',
        'start_date',
        'rate',
    ];

    public function teacher()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
