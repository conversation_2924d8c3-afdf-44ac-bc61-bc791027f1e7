<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClssReport extends Model
{
    use HasFactory;

    protected $table = 'clss_reports';

    protected $fillable = [
        'class_id',
        'teacher_id',
        'student_id',
        'qualifi_id',
        'loggedin_user',
        'loggedin_user_role',
        'loggedin_time',
        'loggedout_time',
        'loggedinduration',
        'amount_due',
    ];

    public function clss()
    {
        return $this->belongsTo(Clss::class, 'class_id', 'id');
    }
    public function quali()
    {
        return $this->belongsTo(TeacherQualifications::class, 'qualifi_id', 'id');
    }

    public function scopeFilterteacher($query, $value)
    {
        return $query->when($value, function($query, $value) {
            return $query->where('loggedin_user', $value );
        });
    }
    public function scopeFilterdate($query, $startdate, $enddate)
    {
        return $query->when($startdate, function ($query, $startdate){
            return $query->whereDate('loggedin_time', '>=', $startdate);
        })
        ->when($enddate, function ($query, $enddate){
            return $query->whereDate('loggedin_time', '<=', $enddate);
        });
    }
    public function scopeLoggedin($query, $value)
    {
        return $query->where('class_id', $value)
            ->where('loggedin_user', auth()->user()->id)
            ->exists();
    }
}
