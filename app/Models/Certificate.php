<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Certificate extends Model
{
    use HasFactory;

    protected $table = 'certificates';

    protected $fillable = [
        'student_id',
        'teacher_id',
        'title',
        'grade',
        'notes',
    ];


    public function student()
    {
        return $this->belongsTo(User::class, 'student_id', 'id');
    }

    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_id', 'id');
    }


    public function scopeSearch($query, $value)
    {
        return $query->where('title', 'like', '%' . $value . '%');
    }
    public function scopeFilterteacher($query, $value)
    {
        $query->when($value, function($query, $value) {
            return $query->where('teacher_id', $value );
        });
    }
    public function scopeFilterstudent($query, $value)
    {
        $query->when($value, function($query, $value) {
            return $query->where('student_id', $value);
        });
    }
}
