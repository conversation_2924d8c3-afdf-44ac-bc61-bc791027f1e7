<?php

namespace App\Livewire;

use App\Traits\UseZoom;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class ZoomMeeting extends Component
{
    public $accessToken = '';
    public $userInfo = '';
    public $userListMeetingserror = '';
    public $userListMeetings = [
        [
            'id' => '',
            'topic' => '',
            'start_time' => '',
            'end_time' => '',
            'duration' => '',
        ]
    ];
    public $createMeeting = '';
    public $deleteMeeting = '';

    function generateZoomAccessToken()
    {
        $accountID = env('ZOOM_ACCOUNT_ID');
        $clientID = env('ZOOM_CLIENT_ID');
        $clientSecret =  env('ZOOM_CLIENT_SECRET');

        $base64Credentials = base64_encode("$clientID:$clientSecret");

        $url = 'https://zoom.us/oauth/token?grant_type=account_credentials&account_id=' . $accountID;

        $response = Http::withHeaders([
                'Host' => 'zoom.us',
                'Authorization' => "Basic $base64Credentials",
            ])->post($url);

            $responseData = $response->json();

            if (isset($responseData['access_token'])) {
                $this->accessToken =  $responseData['access_token'];
            } else {
                Log::error('Zoom OAuth Token Response: ' . json_encode($responseData));
            }
    }
    public function index()
    {
        return $this->accessToken;
    }
    public function userInfo($user)
    {
        $url = 'https://api.zoom.us/v2/users/' . $user;

        if ($user == 'me') {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer $this->accessToken",
            ])->get($url);

            $this->userInfo = $response->json();

            if (!isset($this->userInfo)) {
                $this->userInfo = 'not getting any info.';
            }
        } else {
            $this->userInfo = 'invalid user ID';
        }
    }
    public function userListMeetings($user)
    {
        $url = 'https://api.zoom.us/v2/users/' . $user . '/meetings';

        if ($user == 'me') {
        $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer $this->accessToken",
            ])->get($url);

            $responseData = $response->json();

            if (isset($responseData)) {
                $i=0;
                foreach ($responseData['meetings'] as $meeting) {
                    $this->userListMeetings[$i]['id'] = $meeting['id'];
                    $this->userListMeetings[$i]['topic'] = $meeting['topic'];
                    $this->userListMeetings[$i]['start_time'] = $meeting['start_time'];
                    $this->userListMeetings[$i]['end_time'] = $meeting['end_time'];
                    $this->userListMeetings[$i]['duration'] = $meeting['duration'];
                    $i++;
                }
            } else {
                $this->userListMeetingserror =  'not getting any info.';
            }
        } else {
            $this->userListMeetingserror = 'invalid user ID';
        }
    }
    public function createMeeting($user)
    {

        $url = 'https://api.zoom.us/v2/users/' . $user . '/meetings';

        if ($user == 'me') {
            $response = Http::withToken($this->accessToken)->post($url, [
                'topic' => 'creating test meeting from api2.',
                'start_time' => Carbon::now('Africa/Cairo')->format('Y-m-d H:i:s'),
                'type' => 2,
                'duration' => 30,
                'timezone' => 'Africa/Cairo',
            ]);

            if ($response->successful()) {
                $this->createMeeting = 'Meeting Created';
            } else {

                $this->createMeeting = 'Failed to create a Zoom meeting';
            }
        } else {
            $this->userInfo = 'invalid user ID';
        }

    }
    public function meetingInfo($meeting)
    {
        $url = 'https://api.zoom.us/v2/past_meetings/' . $meeting;

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => "Bearer $this->accessToken",
        ])->get($url);

        $responseData = $response->json();

        if (isset($responseData)) {
            return $responseData;
        } else {
            return 'not getting any info.';
        }
    }
    public function meetingInfojoin($meeting)
    {
        $url = 'https://api.zoom.us/v2/past_meetings/' . $meeting . '/participants';

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => "Bearer $this->accessToken",
        ])->get($url);

        $responseData = $response->json();

        if (isset($responseData)) {
            return $responseData;
        } else {
            return 'not getting any info.';
        }
    }
    public function meetingDel($meeting)
    {
        $url = 'https://api.zoom.us/v2/meetings/' . $meeting;

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => "Bearer $this->accessToken",
        ])->delete($url);

        if ($response->successful()) {
            $this->deleteMeeting = $meeting . ' meeting is deleted';
        } else {

            $this->deleteMeeting = 'Failed to delete a Zoom meeting';
        }
    }

    public function mount()
    {
        if (empty($this->accessToken))
        {
            $this->generateZoomAccessToken();
        }
    }
    public function render()
    {
        return view('livewire.zoom-meeting');
    }
}
