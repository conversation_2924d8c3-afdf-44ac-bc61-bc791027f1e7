<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Teacher>
 */
class TeacherFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => fake()->numberBetween(1,20),
            'gender' => fake()->randomElement(['male', 'female']),
            'rateperclass' => fake()->randomElement(['40', '50', '60', '45', '55', '65', '70', '75']),
        ];
    }
}
