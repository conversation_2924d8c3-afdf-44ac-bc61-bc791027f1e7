<?php

namespace Tests\Feature;

use App\Models\Student;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Volt\Volt;
use Tests\TestCase;

class ProfileTest extends TestCase
{
    use RefreshDatabase;

    public function test_profile_page_is_displayed(): void
    {
        $user = User::factory()->create();
        if ($user->role == 'student')
        {
            $student = Student::factory()->create(['user_id' => $user->id]);
        }

        $response = $this->actingAs($user)->get('/profile');

        $response
            ->assertOk()
            // ->assertSeeVolt('profile.update-profile-information-form')
            // ->assertSeeVolt('profile.update-password-form')
            // ->assertSeeVolt('profile.delete-user-form');
            ->assertSeeVolt('user-profile');
    }

    public function test_profile_information_can_be_updated(): void
    {
        $user = User::factory()->create();
        if ($user->role == 'student')
        {
            $student = Student::factory()->create(['user_id' => $user->id]);
        }

        $this->actingAs($user);

        $component = Volt::test('UserProfile')
            ->set('name', 'Test User')
            ->set('email', '<EMAIL>')
            ->call('save');

        $component
            ->assertHasNoErrors()
            ->assertNoRedirect();

        $user->refresh();

        $this->assertSame('Test User', $user->name);
        $this->assertSame('<EMAIL>', $user->email);
        if($user->email_verified_at == null)
        {
            $this->assertNull($user->email_verified_at);
        }else{
            $this->assertNotNull($user->email_verified_at);
        }
    }

    public function test_email_verification_status_is_unchanged_when_the_email_address_is_unchanged(): void
    {
        $user = User::factory()->create();
        if ($user->role == 'student')
        {
            $student = Student::factory()->create(['user_id' => $user->id]);
        }

        $this->actingAs($user);

        $component = Volt::test('UserProfile')
            ->set('name', 'Test User')
            ->set('email', $user->email)
            ->call('save');

        $component
            ->assertHasNoErrors()
            ->assertNoRedirect();

        $this->assertNotNull($user->refresh()->email_verified_at);
    }

    // public function test_user_can_delete_their_account(): void
    // {
    //     $user = User::factory()->create();

    //     $this->actingAs($user);

    //     $component = Volt::test('profile.delete-user-form')
    //         ->set('password', 'password')
    //         ->call('deleteUser');

    //     $component
    //         ->assertHasNoErrors()
    //         ->assertRedirect('/');

    //     $this->assertGuest();
    //     $this->assertNull($user->fresh());
    // }

    // public function test_correct_password_must_be_provided_to_delete_account(): void
    // {
    //     $user = User::factory()->create();

    //     $this->actingAs($user);

    //     $component = Volt::test('profile.delete-user-form')
    //         ->set('password', 'wrong-password')
    //         ->call('deleteUser');

    //     $component
    //         ->assertHasErrors('password')
    //         ->assertNoRedirect();

    //     $this->assertNotNull($user->fresh());
    // }
}
