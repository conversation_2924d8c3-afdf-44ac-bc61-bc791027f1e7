<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ZoomApp extends Model
{
    use HasFactory;

    protected $table = 'zoom_apps';
    protected $fillable = [
        'user_id',
        'zoom_user_id',
        'account_id',
        'client_id',
        'client_secret',
        'notes',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    public function scopeSearch($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->whereHas('user', function ($query) use ($value) {
                return $query->where('name', '%' . $value . '%');
            })
            ->orWhere('zoom_user_id', $value)
            ->orWhere('account_id', $value)
            ->orWhere('client_id', $value);
        });
    }
}
