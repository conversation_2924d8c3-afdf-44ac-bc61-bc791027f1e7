<div>
    {{-- add modal --}}
    <x-g-modal name="add">
        <div class="bg-white dark:bg-blue-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">إضافة إنجاز جديد</h1>
                <button type="button" wire:click.prevent="closemodal('add')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="add-expcat"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="mb-4">
                    <label for="student_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الطالب:</label>
                    <select wire:model.live="student_id" id="student_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  selected>اختر الطالب</option>
                        @foreach ( $allstudents as $allstudent)
                            <option style="direction:rtl;" value="{{ $allstudent->id }}">{{ $allstudent->name }}</option>
                        @endforeach
                    </select>
                    @error('student_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="teacher_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المعلم:</label>
                    <select wire:model.live="teacher_id" id="teacher_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  selected>اختر المعلم</option>
                        @foreach ( $allteachers as $allteacher)
                            <option style="direction:rtl;" value="{{ $allteacher->id }}">{{ $allteacher->name }}</option>
                        @endforeach
                    </select>
                    @error('teacher_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم الإنجاز:</label>
                    <input type="text" wire:model.live="name" id="name" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسم البند" required>
                    @error('name')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الوصف:</label>
                    <textarea wire:model.live="description" name="description" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                    @error('description')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات:</label>
                    <textarea wire:model.live="notes" name="notes" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                    @error('notes')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="save" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="save"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('add')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- edit modal --}}
    <x-g-modal name="edit">
        <div class="bg-white dark:bg-yellow-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">تعدبل الإنجاز {{ $name }}</h1>
                <button type="button" wire:click.prevent="closemodal('edit')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="edit-expcat"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
            <div class="mb-4">
                <label for="student_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الطالب:</label>
                <select wire:model.live="student_id" id="student_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                    <option style="direction:rtl;"  selected>اختر الطالب</option>
                    @foreach ( $allstudents as $allstudent)
                        <option style="direction:rtl;" value="{{ $allstudent->id }}">{{ $allstudent->name }}</option>
                    @endforeach
                </select>
                @error('student_id')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="teacher_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المعلم:</label>
                <select disabled wire:model.live="teacher_id" id="teacher_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                    <option style="direction:rtl;">اختر المعلم</option>
                    @foreach ( $allteachers as $allteacher)
                        <option style="direction:rtl;" value="{{ $allteacher->id }}">{{ $allteacher->name }}</option>
                    @endforeach
                </select>
                @error('teacher_id')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم الإنجاز:</label>
                <input type="text" wire:model.live="name" id="name" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسم البند" required>
                @error('name')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الوصف:</label>
                <input type="text" wire:model.live="description" id="description" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسم البند" required>
                @error('description')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات:</label>
                <textarea wire:model.live="notes" name="notes" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                @error('notes')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="update" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="update"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('edit')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- delete modal --}}
    <x-g-modal name="delete">
        <div class="bg-white dark:bg-red-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">حذف الإنجاز  {{ $name }}</h1>
                <button type="button" wire:click.prevent="closemodal('delete')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="delete-expcat">
                <div class="mb-4">
                    <div class="block text-right text-lg font-bold text-white">هل انت متأكد من حذف الإنجاز؟!</div>
                </div>
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="delete" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>

                    <x-loading method="delete"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('delete')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- delete selected modal --}}
    <x-g-modal name="deleteselected">
        <div class="bg-white dark:bg-gray-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">حذف الإنجازات</h1>
                <button type="button" wire:click.prevent="closemodal('deleteselected')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="delete-selectedteachers">
                @if (empty($selectedallachievements))
                    <div class="mb-4">
                        <div class="block text-right text-lg font-bold text-white">لابد من اختيار عناصر حتى يتم حذفهم!</div>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" disabled wire:click.prevent="deleteselected" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 h focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>
                        <button type="button" wire:click.prevent="closemodal('deleteselected')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                    </div>
                @else
                    <div class="mb-4">
                        <div class="block text-right text-lg font-bold text-white">هل انت متأكد من حذف الإنجازات؟!</div>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" wire:loading.remove wire:click.prevent="deleteselected" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>

                    <x-loading method="deleteselected"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('deleteselected')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                    </div>
                @endif
            </form>
        </div>
    </x-g-modal>

    <!-- flash save successful message -->
    <x-flash-message on="flashsaved">
        <div class="flex items-center bg-green-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تسجيل العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    <!-- flash update successful message -->
    <x-flash-message on="flashupdated">
        <div class="flex items-center bg-yellow-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تعديل العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    <!-- flash delete successful message -->
    <x-flash-message on="flashdeleted">
        <div class="flex items-center bg-red-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم حذف العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    {{-- data tables --}}
    <div class="card">
        <header class=" block card-header noborder">
            <h4 class="text-primary-500 font-bold text-3xl">تقارير إنجازات الطالب</h4>
        </header>
        <div x-data="{open: false }" class="card-body px-6 pb-6">
            <div class="sm:grid sm:my-2 md:flex sm:place-content-center md:justify-between w-auto mr-0">
                <div class="grid sm:grid-cols-1 lg:grid-cols-6 gap-3">
                    {{-- add modal button --}}
                    @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                    <button type="button" x-data="" x-on:click.prevent="$dispatch('open-modal', 'add')" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div class="flex place-items-center">
                        <iconify-icon icon="heroicons:plus-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                        اضافة إنجاز جديد
                        </div>
                    </button>
                    {{-- delete selected modal button --}}
                    <button type="button"  wire:click.prevent="showdeleteselected"  class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div class="flex place-items-center">
                        <iconify-icon icon="heroicons:trash-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="حذف"></iconify-icon>
                        حذف المحددين
                        </div>
                    </button>
                    @endif
                    {{-- advanced section modal button --}}
                    <button type="button" x-on:click="open = ! open" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div x-show="! open" class="flex place-items-center">
                            <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                            اظهار البحث الــمتقدم
                        </div>
                        <div x-show="open" class="flex place-items-center">
                            <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                            اخفاء البحث الــمتقدم
                        </div>
                    </button>
                    @if (!empty($filterstudent) || !empty($filterteacher))
                        <button wire:click.prevent="resetallfilter" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                            <div class="flex place-items-center">
                                <iconify-icon icon="heroicons:arrow-uturn-down-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                                إعادة تعيين التصفية
                            </div>
                        </button>
                    @endif
                    {{-- print report button --}}
                    <button type="button" onclick="printReport()" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div class="flex place-items-center">
                            <iconify-icon icon="heroicons:printer-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="طباعة"></iconify-icon>
                            طباعة التقرير
                        </div>
                    </button>
                </div>
                {{-- search field --}}
                <div class="my-3 sm:w-auto sm:mt-0 sm:ml-auto md:ml-0">
                    <div class="relative flex w-80 text-gray-700 mr-auto my-auto place-items-center">
                        @if (!empty($search))
                        <button type="button" wire:click.prevent="resetsearch" class="absolute inset-y-0 left-0 pl-3 flex items-center z-1">
                            <iconify-icon icon="heroicons:x-mark-20-solid" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                        </button>
                        @endif
                        <iconify-icon icon="heroicons:magnifying-glass-solid" style="font-size: 24px;color:#016241;" alt="تعديل" class="ml-2"></iconify-icon>
                        <input wire:model.live.debounce.500="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-lg rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="بحث..." wire:ignore>
                    </div>
                </div>
            </div>
            {{-- advanced date filter --}}
            <div x-show="open" class="grid sm:grid-cols-1 lg:grid-cols-2 place-items-center gap-2 my-2">
                    <div class="flex w-full justify-start place-items-center">
                        <label for="filterstudent" class="block text-wrap text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">الطالب: </label>
                        <select wire:model.live="filterstudent" id="filterstudent" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;" value=""  selected>اختر الطالب</option>
                        @foreach ( $allstudents as $allstudent)
                            <option style="direction:rtl;" value="{{ $allstudent->id }}">{{ $allstudent->name }}</option>
                        @endforeach
                        </select>
                    @if (!empty($filterstudent))
                        <button type="button" wire:click.prevent="resetfilter('filterstudent')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 mr-2 p-1 dark:hover:bg-slate-600 dark:hover:text-white">
                            <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                        </button>
                    @endif
                    </div>
                    <div class="flex w-full justify-start place-items-center">
                        <label for="filterteacher" class="block text-wrap text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">المعلم: </label>
                        <select wire:model.live="filterteacher" id="filterteacher" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;" value="" selected>اختر المعلم</option>
                        @foreach ( $allteachers as $allteacher)
                            <option style="direction:rtl;" value="{{ $allteacher->id }}">{{ $allteacher->name }}</option>
                        @endforeach
                        </select>
                        @if (!empty($filterteacher))
                        <button type="button" wire:click.prevent="resetfilter('filterteacher')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 mr-2 p-1 dark:hover:bg-slate-600 dark:hover:text-white">
                            <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                        </button>
                        @endif
                    </div>
            </div>
            {{-- data table --}}
            <div id="report-content" class="overflow-x-auto -mx-6 dashcode-data-table">
                <div class="inline-block min-w-full align-middle">
                    <div class="overflow-hidden">
                    @unless ($allachievements->count() == 0)
                    <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl">
                        <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                        <tr>
                            <th scope="col" class=" table-th text-xl font-medium"><input type="checkbox" wire:model.live="selectall" class="border-gray-300 rounded bg-gray-50 borded ml-1"> الكل </th>
                            <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('student_id')">اسم الطالب</a></th>
                            <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('teacher_id')">اسم المعلم</a></th>
                            <th scope="col" class=" table-th text-xl font-medium">اسم الإنجاز</th>
                            <th scope="col" class=" table-th text-xl font-medium">الوصف</th>
                            <th scope="col" class=" table-th text-xl font-medium">ملاحظات</th>
                            @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                            <th scope="col" class=" table-th text-xl font-medium ">أجراءات</th>
                            @endif
                        </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                        @foreach ($allachievements as $allachievement)
                        <tr wire:key="expcat-{{ $allachievement->id }}">
                            <td class="table-td text-xl font-normal"><input type="checkbox" wire:model.live="selectedachievements" value="{{ $allachievement->id }}" class="border-gray-300 rounded bg-gray-50 borded"></td>
                            <td class="table-td text-xl font-normal">
                            <span class=" text-slate-600 dark:text-white capitalize">{{ $allachievement->student->name }}</span>
                            </td>
                            <td class="table-td text-xl font-normal">
                            <span class=" text-slate-600 dark:text-white capitalize">{{ $allachievement->teacher->name }}</span>
                            </td>
                            <td class="table-td text-xl font-normal">
                            <span class=" text-slate-600 dark:text-white capitalize">{{ $allachievement->name }}</span>
                            </td>
                            <td class="table-td text-xl font-normal">
                            <span class=" text-slate-600 dark:text-white capitalize">{{ $allachievement->description }}</span>
                            </td>
                            <td class="table-td text-xl font-normal">
                            <span class=" text-slate-600 dark:text-white capitalize">{{ $allachievement->notes }}</span>
                            </td>
                            @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                            <td class="table-td  text-xl font-normal">
                            <div class="flex space-x-3 rtl:space-x-reverse">
                                <button type="button" wire:click.prevent="showedit({{ $allachievement }})">
                                <iconify-icon icon="heroicons:pencil-square" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="تعديل"></iconify-icon>
                                </button>
                                <button type="button" wire:click.prevent="showdelete({{ $allachievement }})">
                                <iconify-icon icon="heroicons:trash" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="حذف"></iconify-icon>
                                </button>
                            </div>
                            </td>
                            @endif
                        </tr>
                        @endforeach
                        </tbody>
                    </table>
                    @else
                    <div class="text-center text-xl font-bold text-[#016241">لا يوجد إنجازات حتى الآن.</div>
                    @endunless
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="flex my-4">
        <div class="flex justify-start w-[50%] items-center">
            <label for="perpage" class="text-lg font-medium text-gray-700 dark:text-[#be9539]">عدد النتائج في كل صفحة:</label>
            <select wire:model.live="perpage" id="role" style="direction:ltr;" class="w-20 shadow-sm rounded-md mx-4 px-5 py-1 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;" value="10">10</option>
                <option style="direction:rtl;" value="20">20</option>
                <option style="direction:rtl;" value="30">30</option>
                <option style="direction:rtl;" value="50">50</option>
                <option style="direction:rtl;" value="2000">الكل</option>
            </select>
        </div>
        <div class="w-[100%] mr-4">{{ $allachievements->links('vendor.livewire.tailwind') }}</div>
    </div>
@push('scripts')
<script>
function printReport() {
    const reportContent = document.getElementById('report-content');
    const printWindow = window.open();
    printWindow.document.write(reportContent.outerHTML);
    printWindow.print();
    printWindow.close();
}
// window.addEventListener('print-cert', function () {
//     console.log('printed');
//     const certContent = document.getElementById('cert-content');
//     const printCertWindow = window.open();
//     printCertWindow.document.write(certContent.outerHTML);
//     printCertWindow.print();
//     printCertWindow.close();
// })

// window.addEventListener('print-cert', function () {
//     console.log('printed');
//     const reportContent = document.getElementById('cert-content');
//     const pdf = new jsPDF({
//         orientation: 'l',
//         unit: 'mm',
//         format: 'a4',
//         putOnlyUsedFonts:true
//     });
//     pdf.fromHTML(reportContent, 15, 15 );
//     pdf.save('achievement.pdf');
// })
    window.addEventListener('open-new-window', function(event){
        // console.log(typeof event.detail[0]);
        const url = event.detail[0];
        window.open(url, '_blank');
    })
</script>
@endpush
</div>
