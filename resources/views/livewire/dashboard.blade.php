<div>
    <header class="flex justify-between card-header noborder">
        <h4 class="text-primary-500 font-bold text-3xl">الصفحة الرئيسية</h4>
        <div class="flex justify-end">
          <a href="/" class="flex place-items-center gap-2 text-[#016241] text-xl hover:text-gray-500">
              تحديث البيانات
              <iconify-icon icon="heroicons:arrow-path-rounded-square-16-solid" style="font-size: 36px;color:#016241;font-weight: bold;" alt="حذف"></iconify-icon>
          </a>
        </div>
      </header>
    <div class="grid grid-cols-12 gap-5">
        <div class="lg:col-span-8 col-span-12 space-y-5">
          <div class="card p-6">
            <div class="flex flex-col gap-3">
              <!-- BEGIN: Group Chart4 -->
                <a href="salesdashboard" class="block bg-[#b99675] hover:bg-[#f1c296] rounded-md border border-[#016241] p-4 bg-opacity-[0.15] dark:bg-opacity-25 relative z-[1]">
                    <div class="absolute text-right overlay right-2 top-2 w-full h-full z-[-1]">
                        <iconify-icon icon="heroicons:currency-dollar-16-solid" style="color: #016241; font-size: 50px;"></iconify-icon>
                    </div>
                    <div class="flex justify-between">
                    <span class="mr-12 block mb-6 text-2xl text-slate-900 dark:text-[#016241] font-bold ">
                        الميزانية الحالية
                    </span>
                    <span class="block text-2xl text-slate-900 dark:text-[#016241] font-bold mb-6">
                        {{ round($currbudget) }}
                    </span>
                    </div>
                    <div class="flex justify-between">
                        <div class="flex space-x-2 rtl:space-x-reverse">
                            <div class="flex-1 text-xl font-bold">
                            <span class="block mb-1 text-slate-600 text-sm dark:text-[#016241]">
                               الشهر السابق
                            </span>
                            <span class="block mb-[2px] text-[#016241] text-center">
                                {{ round($prevmonthbudget) }}
                            </span>
                            </div>
                        </div>
                        <div class="flex space-x-2 rtl:space-x-reverse">
                            <div class="flex-1 text-xl font-bold">
                            <span class="block mb-1 text-slate-600 text-sm dark:text-[#016241]">
                                الشهر الأسبق
                            </span>
                            <span class="block mb-[2px] text-[#016241] text-center">
                                {{ round($monthbeforebudget) }}
                            </span>
                            </div>
                        </div>
                    </div>
                </a>

              <a href="student" class="block bg-[#b99675] hover:bg-[#f1c296] rounded-md p-4 bg-opacity-[0.15] dark:bg-opacity-25 relative z-[1] border border-[#016241]">
                <div class="absolute text-right overlay right-2 top-2 w-full h-full z-[-1]">
                    <iconify-icon icon="heroicons:academic-cap-20-solid" style="color: #016241; font-size: 50px;"></iconify-icon>
                </div>
                <div class="flex justify-between">
                <span class="mr-12 block mb-6 text-xl text-slate-900 dark:text-[#016241] font-medium">
                    الطلاب
                </span>
                <span class="block text-2xl text-slate-900 dark:text-[#016241] font-bold mb-6">
                    {{ $allstudents }}
                </span>
                </div>
                <div class="flex justify-between">
                    <div class="flex space-x-2 rtl:space-x-reverse">
                        <div class="flex-1 text-xl font-bold">
                        <span class="block mb-1 text-slate-600 text-sm dark:text-[#016241]">
                           الطلاب المشتركين
                        </span>
                        <span class="block mb-[2px] text-[#016241] text-center">
                            {{ $fixstudents }}
                        </span>
                        </div>
                    </div>
                    <div class="flex space-x-2 rtl:space-x-reverse">
                        <div class="flex-1 text-xl font-bold">
                        <span class="block mb-1 text-slate-600 text-sm dark:text-[#016241]">
                            الطلاب التجريبيين
                        </span>
                        <span class="block mb-[2px] text-[#016241] text-center">
                            {{ $tristudents }}
                        </span>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between">
                    <div class="flex space-x-2 rtl:space-x-reverse">
                        <div class="flex-1 text-xl font-bold">
                        <span class="block mb-1 text-slate-600 text-sm dark:text-[#016241]">
                           الطلاب المنتهي اشتراكهم
                        </span>
                        <span class="block mb-[2px] text-[#016241] text-center">
                            {{ $endstudents }}
                        </span>
                        </div>
                    </div>
                </div>
              </a>

              <a href="teacher" class="block bg-[#b99675] hover:bg-[#f1c296] rounded-md p-4 bg-opacity-[0.15] dark:bg-opacity-25 relative z-[1] border border-[#016241]">
                <div class="absolute text-right overlay right-2 top-2 w-full h-full z-[-1]">
                    <iconify-icon icon="heroicons:pencil-solid" style="color: #016241; font-size: 50px;"></iconify-icon>
                </div>
                <div class="flex justify-between">
                <span class="mr-12 block mb-6 text-xl text-slate-900 dark:text-[#016241] font-medium">
                    المعلمين
                </span>
                <span class="block text-2xl text-slate-900 dark:text-[#016241] font-bold mb-6">
                    {{ $allteachers }}
                </span>
                </div>
                <div class="flex justify-between">
                    <div class="flex space-x-2 rtl:space-x-reverse">
                        <div class="flex-1 text-xl font-bold">
                        <span class="block mb-1 text-slate-600 text-sm dark:text-[#016241]">
                            المعلمين الذكور
                        </span>
                        <span class="block mb-[2px] text-[#016241] text-center">
                            {{ $maleteachers }}
                        </span>
                        </div>
                    </div>
                    <div class="flex space-x-2 rtl:space-x-reverse">
                        <div class="flex-1 text-xl font-bold">
                        <span class="block mb-1 text-slate-600 text-sm dark:text-[#016241]">
                            المعلمات الإناث
                        </span>
                        <span class="block mb-[2px] text-[#016241] text-center">
                            {{ $allteachers - $maleteachers }}
                        </span>
                        </div>
                    </div>
                </div>
              </a>

              <a href="class" class="block bg-[#b99675] hover:bg-[#f1c296] rounded-md p-4 bg-opacity-[0.15] dark:bg-opacity-25 relative z-[1] border border-[#016241]">
                <div class="absolute text-right overlay right-2 top-2 w-full h-full z-[-1]">
                    <iconify-icon icon="heroicons:tv-20-solid" style="color: #016241; font-size: 50px;"></iconify-icon>
                </div>
                <div class="flex justify-between">
                    <span class="mr-12 block mb-6 text-xl text-slate-900 dark:text-[#016241] font-medium">
                        اجمالي الحصص
                    </span>
                    <span class="block text-2xl text-slate-900 dark:text-[#016241] font-bold mb-6">
                        {{ $allclasses }}
                    </span>
                </div>
                <div class="flex justify-between">
                    <div class="flex space-x-2 rtl:space-x-reverse">
                        <div class="flex-1 text-xl font-bold">
                        <span class="block mb-1 text-slate-600 text-sm dark:text-[#016241]">
                            الحصص المنتهية
                        </span>
                        <span class="block mb-[2px] text-[#016241] text-center">
                            {{ $endedclasses }}
                        </span>
                        </div>
                    </div>
                    <div class="flex space-x-2 rtl:space-x-reverse">
                        <div class="flex-1 text-xl font-bold">
                        <span class="block mb-1 text-slate-600 text-sm dark:text-[#016241]">
                            الحصص المجدولة
                        </span>
                        <span class="block mb-[2px] text-[#016241] text-center">
                            {{ $allclasses - $endedclasses }}
                        </span>
                        </div>
                    </div>
                </div>
              </a>

              <!-- END: Group Chart3 -->
            </div>
          </div>
          <div class="card p-6 font-cairo">
            <header class="card-header">
              <h4 class="font-cairo text-[#016241] text-xl font-bold">
                تقارير طلاب الاشتراك / التجديد خلال فترة 6-شهور
              </h4>
            </header>
            <div class="legend-ring">
                <div>
                    <canvas id="myChart"></canvas>
                </div>
            </div>
          </div>
        </div>
        <div class="lg:col-span-4 col-span-12 space-y-5">
          <div class="lg:col-span-4 col-span-12 space-y-5">
            <div class="card">
              <header class="card-header">
                <h4 class="font-cairo dark:text-[#016241] text-xl font-bold">
                  تقارير الأعضاء
                </h4>
                <a href="user" class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#016241] hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">المزيد</a>
              </header>
              <div class="card-body p-6">
                <ul class="divide-y divide-slate-100 dark:divide-slate-700">

                  <li class="text-lg font-bold text-[#016241] py-2">
                    <div class="flex justify-between">
                      <span>المجموعة</span>
                      <span>عدد الأعضاء</span>
                    </div>
                  </li>
                  @foreach($this->rolesordercalc() as $role => $numuser)
                  <li class="text-xl font-bold text-[#016241] py-2">
                      <div class="flex justify-between">
                        @switch($role)
                            @case('admin')
                                <span>إدارة</span>
                                @break
                            @case('supervisor')
                                <span>إشراف</span>
                                @break
                            @case('moderator')
                                <span>دعم فني</span>
                                @break
                            @case('accountant')
                                <span>مالية</span>
                                @break
                            @case('sales')
                                <span>ايرادات</span>
                                @break
                            @case('teacher')
                                <span>تدريس</span>
                                @break
                            @default
                                <span>طلاب</span>
                                @break
                        @endswitch
                          <span>{{ $numuser }} ({{ round($numuser / $allusers, 2) * 100 }}%)</span>
                        </div>
                    </li>
                    @endforeach
                </ul>
              </div>
            </div>
            <div class="card">
              <header class="card-header">
                <h4 class="font-cairo text-[#016241] text-xl font-bold">
                  اكثر المعلمين شعبية
                </h4>
              </header>
              <div class="card-body p-6">
                <div class="legend-ring3">
                    <div wire:ignore>
                        <canvas id="myChart-donut"></canvas>
                    </div>
                </div>
              </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('livewire:navigated', () => {
    /* chart bar */
    const ctx = document.getElementById('myChart');
    let delayed;
    new Chart(ctx, {
    type: 'bar',
    data: {
        labels: @json($currmonths),
        datasets: [
            {
            label: 'اجمالي طلاب الاشتراك',
            data: @json($lastcyclereportnewstudents),
            backgroundColor: [
            'rgb(1,98,65)'
            ],
            borderWidth: 3
            },
            {
            label: 'اجمالي طلاب التجديد',
            data: @json($lastcyclereportresubstudents),
            backgroundColor: [
            'rgb(190,149,57)'
            ],
            borderWidth: 3
            },
    ]
    },
    options: {
        animation: {
            onComplete: () => {
                delayed = true;
            },
            delay: (context) => {
                let delay = 0;
                if (context.type === 'data' && context.mode === 'default' && !delayed) {
                delay = context.dataIndex * 300 + context.datasetIndex * 100;
                }
                return delay;
            },
        },
        scales: {
        y: {
            beginAtZero: true
        }
        }
    }
    });

    /* donut chart */
    const dtx = document.getElementById('myChart-donut');
    new Chart(dtx, {
    type: 'doughnut',
    data: {
        labels: @json($popteacher),
        datasets: [{
            label: 'اكثر المعلمين شعبية',
            data: @json($numteacherclass),
            backgroundColor: [
            'rgb(255, 180, 79)',
            'rgb(66, 33, 11)',
            'rgb(179, 103, 0)',
            'rgb(144, 104, 66)',
            'rgb(255, 255, 255)'
            ],
            hoverOffset: 4
        }]
    },
    options: {
                responsive: true,
                plugins: {
                legend: {
                    position: 'bottom',
                    display: true,
                    textAlign: 'right',
                    usePointStyle: true,
                    pointStyle: 'circle',
                    padding: 5,
                },
                title: {
                    display: false,
                    text: 'اكثر المعلمين شعبية'
                }
                }
            },
    });
});
</script>
@endpush
</div>
