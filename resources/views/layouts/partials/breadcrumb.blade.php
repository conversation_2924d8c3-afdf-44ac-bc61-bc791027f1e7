<div class="mb-5">
    <ul class="m-0 p-0 list-none flex place-items-center">
      <li class="text-lg text-primary-500 font-bold flex place-items-center">
        <a href="/">
          <iconify-icon icon="heroicons-outline:home" style="font-size:36px;"></iconify-icon>
        </a>
          <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
      </li>
      @switch(request()->route()->getName())
        @case(request()->routeIs('teacher'))
        <li class="text-lg text-primary-500 font-bold flex place-items-center">
        قسم الإدارة
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
        <a href="teacher">
            إدارة المعلمين
        </a>
        </li>
        @break

        @case(request()->routeIs('student'))
        <li class="text-lg text-primary-500 font-bold flex place-items-center">
            قسم الإدارة
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
            <a href="student">
                إدارة الطلاب
            </a>
        </li>
        @break

        @case(request()->routeIs('class'))
        <li class="text-lg text-primary-500 font-bold flex place-items-center">
            @switch(auth()->user()->role)
            @case('admin')
            قسم الإدارة
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
                @break

            @case('admin')
            قسم الإدارة
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
                @break

            @case('accountant')
            قسم الإدارة
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
                @break

            @case('moderator')
            قسم الإدارة
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
                @break

            @case('teacher')
            قسم التدرس
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
                @break

            @case('supervisor')
            قسم التدرس
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
                @break

            @case('sales')
            قسم الايرادات
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
                @break

            @default

            @endswitch
            <a href="class">
                إدارة الحلقات
            </a>
        </li>
        @break

        @case(request()->routeIs('finance'))
        <li class="text-lg text-primary-500 font-bold flex place-items-center">
            قسم المالية
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
            <a href="finance">
                تقارير الحلقات
            </a>
        </li>
        @break

        @case(request()->routeIs('sales'))
        <li class="text-lg text-primary-500 font-bold flex place-items-center">
            قسم المالية
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
            <a href="sales">
                تقارير الايرادات
            </a>
        </li>
        @break

        @case(request()->routeIs('supervisor'))
        <li class="text-lg text-primary-500 font-bold flex place-items-center">
            قسم المالية
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
            <a href="supervisor">
                تقارير الإشراف
            </a>
        </li>
        @break

        @case(request()->routeIs('user'))
        <li class="text-lg text-primary-500 font-bold flex place-items-center">
            قسم الإدارة
            <iconify-icon icon="heroicons-outline:chevron-right" class=" text-slate-500 text-lg font-cairo rtl:rotate-180" style="font-size:24px;"></iconify-icon>
            <a href="user">
                إدارة الأعضاء
            </a>
        </li>
        @break

        @case(request()->routeIs('setting'))
        <li class="text-lg text-primary-500 font-bold flex place-items-center">
        <a href="setting">
            الإعدادات
        </a>
        </li>
        @break

        @case(request()->routeIs('profile'))
        <li class="text-lg text-primary-500 font-bold flex place-items-center">
        <a href="profile">
            الملف الشخصي
        </a>
        </li>
        @break

          @default
          <li class="text-lg text-primary-500 font-bold flex place-items-center">
            <a href="teacher">
              الصفحة الرئيسية
            </a>
          </li>

      @endswitch
    </ul>
  </div>
