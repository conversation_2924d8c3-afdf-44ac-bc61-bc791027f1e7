<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clss_reports', function (Blueprint $table) {
            $table->id();
            $table->integer('class_id')->unsigned();
            $table->integer('teacher_id')->unsigned();
            $table->integer('student_id')->unsigned();
            $table->integer('qualifi_id')->unsigned();
            $table->integer('loggedin_user')->unsigned();
            $table->string('loggedin_user_role');
            $table->dateTime('loggedin_time');
            $table->dateTime('loggedout_time')->nullable();
            $table->string('loggedinduration')->nullable();
            $table->integer('amount_due')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clss_reports');
    }
};
