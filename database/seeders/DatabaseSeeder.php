<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // setting
        \App\Models\Settings::factory()->create([
            'classperiod' => '30',
            'perpage' => '10',
            'safe' => 0,
            'tax' => 14,
            'meeting_app' => 'zoom',
            'saudirate' => 1.0,
            'egyptrate' => 1.0,
            'uaerate' => 1.0,
            'kuwaitrate' => 1.0,
            'qatarrate' => 1.0,
            'omanrate' => 1.0,
            'usdrate' => 1.0,
        ]);

        // bank
        \App\Models\Banks::factory()->create([
            'id' => 1,
            'country' => 'مصر',
            'name' => 'اخرى',
            'accountuser' => 'مالك محمد فوزي خليل',

        ]);

        // expense category
        \App\Models\ExpensesCategory::factory()->create([
            'id' => 1,
            'name' => 'سحب من الرصيد',
        ]);
        \App\Models\ExpensesCategory::factory()->create([
            'id' => 2,
            'name' => 'المرتجع',
        ]);
        \App\Models\ExpensesCategory::factory()->create([
            'id' => 3,
            'name' => 'ضرائب',
        ]);

        // expense subcategory
        \App\Models\ExpensesSubcategory::factory()->create([
            'id' => 1,
            'category_id' => 3,
            'name' => 'المملكة العربية السعودية',
        ]);
        \App\Models\ExpensesSubcategory::factory()->create([
            'id' => 2,
            'category_id' => 3,
            'name' => 'مصر',
        ]);
        \App\Models\ExpensesSubcategory::factory()->create([
            'id' => 3,
            'category_id' => 3,
            'name' => 'الإمارات العربية المتحدة',
        ]);
        \App\Models\ExpensesSubcategory::factory()->create([
            'id' => 4,
            'category_id' => 3,
            'name' => 'الكويت',
        ]);
        \App\Models\ExpensesSubcategory::factory()->create([
            'id' => 5,
            'category_id' => 3,
            'name' => 'قطر',
        ]);
        \App\Models\ExpensesSubcategory::factory()->create([
            'id' => 6,
            'category_id' => 3,
            'name' => 'عمان',
        ]);
        \App\Models\ExpensesSubcategory::factory()->create([
            'id' => 7,
            'category_id' => 3,
            'name' => 'اخرى',
        ]);

        // teacher qualifications
        \App\Models\TeacherQualifications::factory()->create([
            'name' => 'القرآن الكريم',
            'rateperhour' => 50,
        ]);

        //users
        // id = 1;
        \App\Models\User::factory()->create([
            'name' => 'Admin User',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('********'),
            'phone' => '***********',
            'country' => 'مصر',
            'is_active' => false,
            'role' => 'superadmin',
            'status' => true,
        ]);

        // id = 2
        \App\Models\User::factory()->create([
            'name' => 'moderator User',
            'username' => 'moderatorcom',
            'password' => Hash::make('********'),
            'email' => '<EMAIL>',
            'phone' => '***********',
            'country' => 'مصر',
            'is_active' => false,
            'role' => 'moderator',
            'status' => true,
        ]);

        // id = 3
        \App\Models\User::factory()->create([
            'name' => 'supervisor User',
            'username' => 'supervisorcom',
            'password' => Hash::make('********'),
            'email' => '<EMAIL>',
            'phone' => '***********',
            'country' => 'مصر',
            'is_active' => false,
            'role' => 'supervisor',
            'status' => true,
        ]);

        // id = 4
        \App\Models\User::factory()->create([
            'name' => 'accountant User',
            'username' => 'accountantcom',
            'password' => Hash::make('********'),
            'email' => '<EMAIL>',
            'phone' => '***********',
            'country' => 'مصر',
            'is_active' => false,
            'role' => 'accountant',
            'status' => true,
        ]);

        // id = 5
        \App\Models\User::factory()->create([
            'name' => 'sales User',
            'username' => 'salescom',
            'password' => Hash::make('********'),
            'email' => '<EMAIL>',
            'phone' => '***********',
            'country' => 'مصر',
            'is_active' => false,
            'role' => 'sales',
            'status' => true,
        ]);

        // id = 6
        \App\Models\User::factory()->create([
            'name' => 'teacher User',
            'username' => 'teachercom',
            'password' => Hash::make('********'),
            'email' => '<EMAIL>',
            'phone' => '***********',
            'country' => 'مصر',
            'is_active' => 'not',
            'role' => 'teacher',
            'status' => true,
        ]);

        // id = 7
        \App\Models\User::factory()->create([
            'name' => 'student User',
            'username' => 'studentcom',
            'password' => Hash::make('********'),
            'email' => '<EMAIL>',
            'phone' => '***********',
            'country' => 'المملكة العربية السعودية',
            'is_active' => 'not',
            'role' => 'student',
            'status' => true,
        ]);

        // id = 8;
        \App\Models\User::factory()->create([
            'name' => 'Manager',
            'username' => 'manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('********'),
            'phone' => '***********',
            'country' => 'مصر',
            'is_active' => false,
            'role' => 'admin',
            'status' => true,
        ]);

        // student factory
        \App\Models\Student::factory()->create([
            'user_id' => 7,
            'dateofbirth' => '2005-8-14',
            'gender_prefer' => 'male',
            'subscription' => 'fixed',
            'subscription_id' => 1,
            'paid' => '500',
            'bank_id' => 1,
            'numofclasses' => '50',
            'startdate' => Carbon::now('Africa/Cairo')->format('Y-m-d'),
            'reg_status' => 'new',
        ]);

        // teacher factory
        \App\Models\Teacher::factory()->create([
            'user_id' => 6,
            'gender' => 'male',
            'qualifications' => json_encode([1]),
            'rateperclass' => '65',
        ]);


        // \App\Models\User::factory(10)->create();
        // \App\Models\Student::factory(5)->create();
        // \App\Models\Teacher::factory(5)->create();
        }
}
