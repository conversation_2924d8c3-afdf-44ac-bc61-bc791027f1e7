<?php

namespace App\Traits;

use App\Models\ZoomApp;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

trait UseZoom
{
    public $accessToken = '';
    public $userInfo = '';
    public $userListMeetingserror = '';
    public $userListMeetings = [
        [
            'id' => '',
            'topic' => '',
            'start_time' => '',
            'end_time' => '',
            'duration' => '',
        ]
    ];
    public $createMeeting = '';
    public $updateMeeting = '';
    public $deleteMeeting = '';
    public $responseData = [];

    function generateZoomAccessToken($user)
    {
        $accountID = $user->zoominfo->account_id;
        $clientID = $user->zoominfo->client_id;
        $clientSecret = $user->zoominfo->client_secret;

        $base64Credentials = base64_encode("$clientID:$clientSecret");

        $url = 'https://zoom.us/oauth/token?grant_type=account_credentials&account_id=' . $accountID;

        $response = Http::withHeaders([
                'Host' => 'zoom.us',
                'Authorization' => "Basic $base64Credentials",
            ])->post($url);

            $responseData = $response->json();

            // dd($responseData);

            if (isset($responseData['access_token'])) {
                $this->accessToken =  $responseData['access_token'];
            } else {
                Log::error('Zoom OAuth Token Response: ' . json_encode($responseData));
            }
    }
    public function userInfo($user)
    {
        $url = 'https://api.zoom.us/v2/users/' . $user;

        if ($user) {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer $this->accessToken",
            ])->get($url);

            if (!empty($response)) {
            $this->responseData = $response->json();
            }
        } else {
            $this->responseData['error'] = 'invalid user ID';
        }
    }
    public function userListMeetings($user)
    {
        $url = 'https://api.zoom.us/v2/users/' . $user . '/meetings';

        if ($user == 'me') {
        $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer $this->accessToken",
            ])->get($url);

            $responseData = $response->json();

            if (isset($responseData)) {
                $i=0;
                foreach ($responseData['meetings'] as $meeting) {
                    $this->userListMeetings[$i]['id'] = $meeting['id'];
                    $this->userListMeetings[$i]['topic'] = $meeting['topic'];
                    $this->userListMeetings[$i]['start_time'] = $meeting['start_time'];
                    $this->userListMeetings[$i]['end_time'] = $meeting['end_time'];
                    $this->userListMeetings[$i]['duration'] = $meeting['duration'];
                    $i++;
                }
            } else {
                $this->userListMeetingserror =  'not getting any info.';
            }
        } else {
            $this->userListMeetingserror = 'invalid user ID';
        }
    }
    public function createMeeting($user, $student, $start_time)
    {
        $userxist = ZoomApp::search($user)->first() ? true : false;

        if ($userxist) {
            $url = 'https://api.zoom.us/v2/users/me/meetings';
            $response = Http::withToken($this->accessToken)->post($url, [
                'topic' => 'حلقة مع الطالب "' . $student . '"',
                'start_time' => $start_time,
                'type' => 2,
                'duration' => 30,
                'timezone' => 'Africa/Cairo',
            ]);

            if ($response->successful()) {
                $this->responseData = $response->json();
            } else {
                $this->createMeeting = 'Failed to create a Zoom meeting';
            }
        } else {
            $this->createMeeting = 'invalid user ID';
        }
    }
    public function updateMeeting($meeting, $start_time)
    {
        $url = 'https://api.zoom.us/v2/meetings/' . $meeting;

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => "Bearer $this->accessToken",
        ])->patch($url, [
            'start_time' => $start_time,
        ]);

        if ($response->successful()) {
            $this->responseData = $response->json();
        } else {
            $this->updateMeeting = 'Failed to update a Zoom meeting';
        }
    }
    public function deleteMeeting($meeting)
    {
        $url = 'https://api.zoom.us/v2/meetings/' . $meeting;

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => "Bearer $this->accessToken",
        ])->delete($url);

        if ($response->successful()) {
            $this->responseData = $response->json();
        } else {

            $this->deleteMeeting = 'Failed to delete a Zoom meeting';
        }
    }
    public function meetingInfo($meeting)
    {
        $url = 'https://api.zoom.us/v2/past_meetings/' . $meeting;

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => "Bearer $this->accessToken",
        ])->get($url);

        $responseData = $response->json();

        if (isset($responseData)) {
            return $responseData;
        } else {
            return 'not getting any info.';
        }
    }
    public function meetingInfojoin($meeting)
    {
        $url = 'https://api.zoom.us/v2/past_meetings/' . $meeting . '/participants';

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => "Bearer $this->accessToken",
        ])->get($url);

        $responseData = $response->json();

        if (isset($responseData)) {
            return $responseData;
        } else {
            return 'not getting any info.';
        }
    }
    public function meeting($meeting)
    {
        $url = 'https://api.zoom.us/v2/meetings/' . $meeting;

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => "Bearer $this->accessToken",
        ])->delete($url);

        if ($response->successful()) {
            $this->deleteMeeting = $meeting . ' meeting is deleted';
        } else {

            $this->deleteMeeting = 'Failed to delete a Zoom meeting';
        }
    }
}
