<?php

namespace App\Livewire;

use App\Models\User;
use App\Models\Courses as ModelsCourses;
use App\Models\Notification;
use App\Models\Settings;
use App\Traits\UseFilter;
use App\Traits\UseDefault;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;

use Livewire\Component;

class Courses extends Component
{
    use WithPagination, UseFilter, UseDefault;

    public $selectedcourses = [];
    public $selectall = false;

    public $course_id;
    public $name = '';
    public $description = '';
    public $teacher_id = '';
    public $price;
    public $access_code;
    public $notes = '';
    public $lastupdatedat;
    public $lastupdateuser_id;


    public $teachers = [];

    public function save()
    {
        $validated = $this->validate([
            'name' => 'required|string|max:120',
            'description' => 'required|string|max:500',
            'teacher_id' => 'required|numeric|max:999',
            'price' => 'required|integer|min:0|max:10000',
            'access_code' => 'required|string|max:120',
            'notes' => 'string|max:500',
            'lastupdateuser_id' => '',
        ]);
        $validated['lastupdateuser_id'] = auth()->user()->id;

        $course = ModelsCourses::create($validated);

        $notifiedusers = User::whereIn('role', ['superadmin','admin','acountant'])->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'new course',
                'url' => 'courses?q=' . $course->name,
                'message' => 'تم اضافة كورس جديد باسم "' . $course->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }

        $this->closemodal('add');
        $this->dispatch('flashsaved');
    }
    public function showedit(ModelsCourses $course)
    {
        $this->course_id = $course->id;
        $this->name = $course->name;
        $this->description = $course->description;
        $this->teacher_id = $course->teacher_id;
        $this->price = $course->price;
        $this->access_code = $course->access_code;
        $this->notes = $course->notes;
        $this->lastupdateuser_id = $course->user->name;
        $this->lastupdatedat = Carbon::parse($course->updated_at)->format('Y-m-d || A g:i:s');
        $this->openmodal('edit');
    }
    public function update()
    {
        $validated = $this->validate([
            'name' => 'required|string|max:120',
            'description' => 'required|string|max:500',
            'teacher_id' => 'required|numeric|max:999',
            'price' => 'required|integer|min:0|max:10000',
            'access_code' => 'required|string|max:120',
            'notes' => 'string|max:500',
            'lastupdateuser_id' => '',
        ]);
        $validated['lastupdateuser_id'] = auth()->user()->id;

        $course = ModelsCourses::find($this->course_id);
        $course->update($validated);

        $notifiedusers = User::where('role', 'admin')->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'course updated',
                'url' => 'courses?q=' . $course->name,
                'message' => 'تم تعديل كورس باسم "' . $course->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }

        $this->closemodal('edit');
        $this->dispatch('flashupdated');
    }
    public function showdelete(ModelsCourses $course)
    {
        $this->course_id = $course->id;
        $this->name = $course->name;
        $this->notes = $course->notes;
        $this->openmodal('delete');
    }
    public function delete(){

        $course = ModelsCourses::find($this->course_id);
        $course->delete();

        $notifiedusers = User::where('role', 'admin')->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'course updated',
                'url' => '',
                'message' => 'تم حذف كورس باسم "' . $course->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }
        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }
    public function deleteselected()
    {
        ModelsCourses::whereIn('id', $this->selectedcourses)->delete();
        $this->selectedcourses = [];
        $this->closemodal('deleteselected');
    }
    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedcourses = ModelsCourses::paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedcourses = [];
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'course_id',
            'name',
            'description',
            'teacher_id',
            'price',
            'access_code',
            'filterexpsubcate',
            'filtersalesman',
            'notes',
            'lastupdateuser_id',
            'lastupdatedat',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function generateCode()
    {
        $this->access_code = Str::random(16);
    }
    public function mount()
    {
        $this->settings = Settings::find(1);
        $this->teachers = User::filterrole('teacher')->get();

        if (empty($this->perpage)) {
            $this->perpage = $this->settings->perpage;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'DESC';
    }
    public function render()
    {
        return view('livewire.courses', [
            'courses' => ModelsCourses::filterteacher($this->filterteacher)
            ->orderBy($this->sortBy, $this->sortDir)
            ->search($this->search)
            ->paginate($this->perpage),
        ]);
    }
}
