<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('zoom_apps', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->unsigned();
            $table->string('zoom_user_id');
            $table->string('account_id');
            $table->string('client_id');
            $table->string('client_secret');
            $table->longText('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('zoom_apps');
    }
};
