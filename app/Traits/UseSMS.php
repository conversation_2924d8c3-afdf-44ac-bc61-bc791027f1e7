<?php

namespace App\Traits;


trait UseSMS
{
    public function sendSMS($to, $txtmsg)
    {
        $api_key = env('VONAGE_KEY');
        $api_secret = env('VONAGE_SECRET');

        $basic  = new \Vonage\Client\Credentials\Basic($api_key, $api_secret);
        $client = new \Vonage\Client($basic);

        $response = $client->sms()->send(
            new \Vonage\SMS\Message\SMS($to, '201004743833', $txtmsg, 'unicode')
        );

        // $message = $response->current();

        // if ($message->getStatus() == 0) {
        //     echo "The message was sent successfully\n";
        // } else {
        //     echo "The message failed with status: " . $message->getStatus() . "\n";
        // }
    }


}
