<?php

namespace App\Livewire;

use App\Models\Expenses;
use App\Models\ExpensesCategory;
use App\Models\Settings;
use Livewire\Attributes\Computed;
use Livewire\WithPagination;
use App\Traits\UseFilter;

use Livewire\Component;

class ExpCategory extends Component
{
    use WithPagination, UseFilter;

    public $selectedexpcategories = [];
    public $listcountries = [];
    public $selectall = false;

    public $expcate_id;
    public $name = '';
    public $notes = '';

    public function save()
    {
        $validated = $this->validate([
            'name' => 'required|unique:expenses_categories,name|string|max:120',
            'notes' => 'string|max:500',
        ]);

        ExpensesCategory::create($validated);
        $this->closemodal('add');
        $this->dispatch('flashsaved');

    }
    public function showedit(ExpensesCategory $expcate)
    {
        $this->expcate_id = $expcate->id;
        $this->name = $expcate->name;
        $this->notes = $expcate->notes;
        $this->openmodal('edit');
    }
    public function update()
    {
        $validated = $this->validate([
            'name' => 'required|unique:expenses_categories,name,' . $this->expcate_id . '|string|max:120',
            'notes' => 'string|max:500',
        ]);

        ExpensesCategory::find($this->expcate_id)->update($validated);

        $this->closemodal('edit');
        $this->dispatch('flashupdated');

    }
    public function showdelete(ExpensesCategory $expcate)
    {
        $this->expcate_id = $expcate->id;
        $this->name = $expcate->name;
        $this->notes = $expcate->notes;
        $this->openmodal('delete');
    }
    public function delete(){

        ExpensesCategory::find($this->expcate_id)->delete();

        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }
    public function deleteselected()
    {
        ExpensesCategory::whereIn('id', $this->selectedexpcategories)->delete();
        $this->selectedexpcategories = [];
        $this->closemodal('deleteselected');
    }


    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedexpcategories = ExpensesCategory::paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedexpcategories = [];
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'expcate_id',
            'name',
            'notes',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function gettotalexpenses($category)
    {
        $expenses = Expenses::filterdate($this->filterdatestart, $this->filterdateend)
        ->filterexpcate($category->id)->get();
        return $expenses->sum('amount');
    }
    public function mount()
    {
        if(empty($this->perpage))
        {
            $this->perpage = Settings::find(1)->perpage;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'ASC';
    }
    public function render()
    {
        $expcategories = ExpensesCategory::paginate($this->perpage);
        $expcategories_ids = $expcategories->pluck('id');
        $expenses = Expenses::whereIn('category_id', $expcategories_ids);
        $totalexpense = $expenses->sum('amount');
        return view('livewire.exp-category', [
            'expcategories' => $expcategories,
            'totalexpense' => $totalexpense,
        ]);
    }
}
