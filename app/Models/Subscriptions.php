<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscriptions extends Model
{
    use HasFactory;

    protected $table = 'subscriptions';
    protected $fillable = [
        'name',
        'numofclasses',
        'amount_sar',
        'amount_aed',
        'amount_kwd',
        'amount_qar',
        'amount_omr',
        'amount_egp',
        'amount_usd',
        'notes',
    ];

    public function user()
    {
        return $this->hasMany(Student::class, 'subscription_id', 'id');
    }
    public function scopeSearch($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->where('name', 'like', '%' . $value . '%');
        });
    }
}
