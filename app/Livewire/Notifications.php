<?php

namespace App\Livewire;

use App\Models\Notification;
use App\Traits\UseFilter;
use Livewire\Component;
use Livewire\WithPagination;

class Notifications extends Component
{
    use WithPagination;


    public $perpage = 10;

    public function titletoroute($title)
    {
        if(str_contains($title, 'student'))
        {
            return 'student';
        } elseif (str_contains($title, 'teacher'))
        {
            return 'teacher';
        } elseif (str_contains($title, 'user'))
        {
            return 'user';
        } elseif (str_contains($title, 'sales'))
        {
            return 'sales';
        } elseif (str_contains($title, 'class') || str_contains($title, 'report'))
        {
            return 'class';
        } elseif (str_contains($title, 'expense'))
        {
            return 'expenses';
        } elseif (str_contains($title, 'course'))
        {
            return 'courses';
        } elseif (str_contains($title, 'lecture'))
        {
            return 'lectures';
        }
    }
    public function titletoar($title)
    {
        switch ($title) {

            // notifications for save method
            case 'new student':
                return 'طالب جديد';
                break;

            case 'new teacher':
                return 'معلم جديد';
                break;

            case 'new user':
                return 'عضو جديد';
                break;

            case 'new class':
                return 'حلقة جديدة';
                break;

            case 'active class':
                return 'انعقاد حلقة ';
                break;

            case 'new schadule':
                return 'جدول جديد';
                break;

            case 'new expense':
                return 'سند صرف جديد';
                break;

            case 'new sales':
                return 'سند قبض جديد';
                break;

            case 'new report':
                return 'تقرير جديد';
                break;

            case 'new course':
                return 'كورس جديد';
                break;

            case 'new lecture':
                return 'ةمحاضرة جديد';
                break;

            // notifications for update method
            case 'student updated':
                return 'تعديل لطالب';
                break;

            case 'teacher updated':
                return 'تعديل لمعلم';
                break;

            case 'user updated':
                return 'تعديل لعضو';
                break;

            case 'report updated':
                return 'تعديل لتقرير';
                break;

            case 'expense updated':
                return 'تعديل لسند صرف';
                break;


            case 'sales updated':
                return 'تعديل لسند قبض';
                break;

            case 'class updated':
                return 'تعديل لحلقة';
                break;

            case 'course updated':
                return 'تعديل لكورس';
                break;

            case 'lecture updated':
                return 'تعديل لمحاضرة';
                break;

            // notifications for delete method
            case 'student deleted':
                return 'حذف الطالب';
                break;

            case 'expense deleted':
                return 'حذف سند الصرف';
                break;

            case 'teacher deleted':
                return 'حذف المعلم';
                break;

            case 'user deleted':
                return 'حذف العضو';
                break;

            case 'course deleted':
                return 'حذف كورس';
                break;

            case 'lecture deleted':
                return 'حذف محاضرة';
                break;

            default:
                return '/';
                break;
        }
    }
    public function notiseen($noti_id, $route)
    {
        Notification::find($noti_id)->update([
            'is_seen' => true,
        ]);

        $this->redirect($route, true);
    }
    public function notiseenall($userid)
    {
        Notification::where('user_id', $userid)->update([
                'is_seen' => true,
            ]);
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset();
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function render()
    {
        $allnotifications = Notification::filteruser(auth()->user()->id);

        return view('livewire.notifications', [
            'notifications' => $allnotifications->orderBy('created_at', 'DESC')
            ->limit(5)
            ->get(),
            'allnotifications' => $allnotifications->paginate($this->perpage),
            'unseen' => $allnotifications->where('is_seen', false)->count(),
        ]);
    }
}
