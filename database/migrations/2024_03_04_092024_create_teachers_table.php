<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('teachers', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->unsigned();
            $table->string('zoomuser_id')->nullable()->default('لم يتم اضافته بعد');
            $table->string('gender');
            $table->string('rateperclass');
            $table->jsonb('qualifications')->nullable();
            $table->string('transfersalary')->nullable();
            $table->string('transferdetails')->nullable();
            $table->jsonb('upfiles')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('teachers');
    }
};
