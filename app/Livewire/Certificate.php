<?php

namespace App\Livewire;

use App\Models\Certificate as ModelsCertificate;
use App\Models\Settings;
use App\Models\User;
use Livewire\Attributes\Computed;
use Livewire\WithPagination;
use App\Traits\UseFilter;

use Livewire\Component;

class Certificate extends Component
{
    use WithPagination, UseFilter;

    public $selectedcertificates = [];
    public $selectall = false;

    public $cert_id;
    public $student_id;
    public $student_name = '';
    public $teacher_id;
    public $teacher_name = '';
    public $title = '';
    public $grade = '';
    public $notes = '';

    // default data properties
    public $allstudents = [];
    public $allteachers = [];


    public function save()
    {
        $validated = $this->validate([
            'student_id' => 'required|integer',
            'teacher_id' => '',
            'title' => 'required|string|max:500',
            'grade' => 'required|string|max:50',
            'notes' => 'string|max:500',
        ]);

        $validated['teacher_id'] = auth()->user()->id;

        ModelsCertificate::create($validated);

        $this->closemodal('add');
        $this->dispatch('flashsaved');

    }
    public function showedit(ModelsCertificate $cert)
    {
        $this->cert_id = $cert->id;
        $this->student_id = $cert->student_id;
        $this->teacher_id = $cert->teacher_id;
        $this->title = $cert->title;
        $this->grade = $cert->grade;
        $this->notes = $cert->notes;

        $this->openmodal('edit');
    }
    public function update()
    {
        $validated = $this->validate([
            'student_id' => 'required|integer',
            'teacher_id' => '',
            'title' => 'required|string|max:500',
            'grade' => 'required|string|max:50',
            'notes' => 'string|max:500',
        ]);

        ModelsCertificate::find($this->cert_id)->update($validated);

        $this->closemodal('edit');
        $this->dispatch('flashupdated');

    }
    public function showcert($id)
    {
        $url = 'pdf/cert/show/' . $id;
        $this->redirectToUrl($url);
    }
    public function printcert($id)
    {
        $url = 'pdf/cert/download/' . $id;
        $this->redirectToUrl($url);
    }
    public function showdelete(ModelsCertificate $cert)
    {
        $this->cert_id = $cert->id;
        $this->student_id = $cert->student_id;
        $this->teacher_id = $cert->teacher_id;
        $this->title = $cert->title;
        $this->grade = $cert->grade;
        $this->notes = $cert->notes;

        $this->openmodal('delete');
    }
    public function delete(){

        ModelsCertificate::find($this->cert_id)->delete();

        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }
    public function deleteselected()
    {
        ModelsCertificate::whereIn('id', $this->selectedcertificates)->delete();
        $this->selectedcertificates = [];
        $this->closemodal('deleteselected');
    }


    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedcertificates = ModelsCertificate::paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedcertificates = [];
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'student_id',
            'teacher_id',
            'title',
            'grade',
            'student_name',
            'teacher_name',
            'notes',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function redirectToUrl($url)
    {
        $this->dispatch('open-new-window', $url);
    }
    public function mount()
    {
        $this->allstudents = User::filterrole('student')->get();
        $this->allteachers = User::filterrole('teacher')->get();

        if(empty($this->perpage))
        {
            $this->perpage = Settings::find(1)->perpage;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'DESC';
    }
    public function render()
    {
        return view('livewire.certificate', [
            'allcertificates' => ModelsCertificate::filterteacher($this->filterteacher)
            ->filterstudent($this->filterstudent)
            ->search($this->search)
            ->paginate($this->perpage),
        ]);
    }
}
