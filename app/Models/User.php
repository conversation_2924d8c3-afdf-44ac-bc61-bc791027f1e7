<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'username',
        'password',
        'email',
        'phone',
        'country',
        'is_active',
        'role',
        'image',
        'status',
    ];

    protected $table = 'user';

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'status' => 'boolean',
        'is_active' => 'boolean',
    ];

    // setting relations between the models
    public function teacher()
    {
        return $this->hasOne(Teacher::class, 'user_id', 'id');
    }

    public function student()
    {
        return $this->hasOne(Student::class, 'user_id', 'id');
    }

    public function sales()
    {
        return $this->hasOne(SalesReport::class, 'sales_id', 'id');
    }

    public function salestudent()
    {
        return $this->hasOne(SalesReport::class, 'student_id', 'id');
    }

    public function user()
    {
        return $this->hasOne(SalesReport::class, 'lastupdateuser_id', 'id');
    }
    public function zoominfo()
    {
        return $this->hasOne(ZoomApp::class, 'user_id', 'id');
    }
    public function achievement()
    {
        return $this->hasOne(Achievements::class, 'student_id', 'id');
    }

    public function userHistory()
    {
        return $this->hasMany(UserHistory::class, 'user_id', 'id');
    }
    public function teacherHourRate()
    {
        return $this->hasMany(TeacherHourRate::class, 'user_id', 'id');
    }

    public function subscription()
    {
        return $this->belongsTo(Subscriptions::class, 'user_id', 'id');
    }

    public function rater()
    {
        return $this->hasMany(Ratings::class, 'rater_id', 'id');
    }
    public function ratee()
    {
        return $this->hasMany(Ratings::class, 'ratee_id', 'id');
    }

    //scope search and filter methods
    public function scopeSearch($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->where('name', 'like', '%' . $value . '%')
            ->orWhere('username', 'like', '%' . $value . '%')
            ->orWhere('email', 'like', '%' . $value . '%')
            ->orWhere('phone', 'like', '%' . $value . '%');
        });
    }

    public function scopeFiltercountry($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->where('country', $value);
        });
    }

    public function scopeFilterrole($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->where('role', $value);
        });
    }

    public function scopeFiltersubscription($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->whereHas('student', function ($query, $value) {
                return $query->where('subscription', $value);
                });
        });
    }
    public function scopeFilterregstatus($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->whereHas('student', function ($query, $value) {
                return $query->where('reg_status', $value);
                });
        });
    }
    public function scopeFilterstudentdate($query, $startdate, $enddate)
    {
        return $query->whereHas('student', function ($query, $startdate, $enddate)
        {
            return $query->when($startdate, function ($query, $startdate)
            {
                return $query->whereDate('updated_at', '>=', $startdate);
            })
            ->when($enddate, function ($query, $enddate)
            {
                return $query->whereDate('updated_at', '<=', $enddate);
            });
        });
    }
    public function scopeFilterinvcode($query, $value)
    {
        return $query->when($value, function($query, $value){
            return $query->whereHas('student', function ($query, $value) {
                return $query->where('inv_code', $value);
                });
        });
    }
    public function scopeFiltersales($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->whereHas('sales', function ($query, $value) {
                return $query->where('sales_id', $value);
                });
        });
    }
    public function scopeFilterteacher($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->when($value, function($query, $value){
                return $query->where('id', $value);
            });
        });
    }
    public function scopeFiltergender($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->whereHas('teacher', function ($query) use ($value) {
                return $query->when($value, function($query, $value){
                    return $query->where('gender', $value);
                    });
                });
        });
    }

    public function scopeFilterqualifications($query, $value)
    {
        return $query->when($value, function($query, $value){
            return $query->whereHas('teacher', function ($query) use ($value) {
                return $query->when($value, function($query, $value){
                    return $query->whereJsonContains('qualifications', $value);
                });
            });
        });
    }

    public function scopeFilteractive($query, $value)
    {
        return $query->when($value, function($query, $value){
            return $query->where('is_active', $value);
        });
    }

    public function scopeFilterstatus($query, $value)
    {
        return $query->when($value, function($query, $value){
            return $query->where('status', $value);
        });
    }
}
