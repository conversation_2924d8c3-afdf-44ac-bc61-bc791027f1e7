<?php

use App\Http\Controllers\LogoutController;
use App\Http\Controllers\MyFatoorahController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\PdfController;

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/


Route::middleware(['auth', 'verified', 'active'])->group(
    function () {
        //start routes
        Route::view('/', 'dashboard')->middleware('role:admin,superadmin,accountant')->name('dashboard');
        Route::view('class', 'class')->middleware('role:admin,superadmin,accountant,moderator,supervisor,sales,teacher,student')->name('class');
        // قسم الادارة
        Route::view('student', 'student')->middleware('role:admin,superadmin,accountant,moderator,sales')->name('student');
        Route::view('teacher', 'teacher')->middleware('role:admin,superadmin,accountant,moderator')->name('teacher');
        Route::view('user', 'user')->middleware('role:admin,superadmin')->name('user');
        Route::view('subscription', 'subscription')->middleware('role:admin,superadmin')->name('subscription');
        Route::view('qualification', 'qualification')->middleware('role:admin,superadmin')->name('qualification');
        Route::view('courses', 'courses')->middleware('role:admin,superadmin,moderators,sales')->name('courses');
        Route::view('lectures', 'lectures')->middleware('role:admin,superadmin,moderators,sales')->name('lectures');
        Route::view('zoomapps', 'zoomapps')->middleware('role:admin,superadmin')->name('zoomapps');
        //قسم التقارير
        Route::view('finance', 'finance')->middleware('role:admin,superadmin,accountant')->name('finance');
        Route::view('classreport', 'class-report')->middleware('role:admin,superadmin')->name('classreport');
        Route::view('supervisor', 'supervisor')->middleware('role:admin,superadmin')->name('supervisor');
        Route::view('ratings', 'ratings')->middleware('role:admin,superadmin')->name('ratings');
        Route::view('certificates', 'certificates')->middleware('role:admin,superadmin,teacher,moderator')->name('certificates');
        Route::view('achievements', 'achievements')->middleware('role:admin,superadmin,moderator')->name('achievements');
        //قسم المالية
        Route::view('salesdashboard', 'salesdashboard')->middleware('role:admin,superadmin,accountant')->name('salesdashboard');
        Route::view('sales', 'sales')->middleware('role:admin,superadmin,accountant,sales')->name('sales');
        Route::view('expenses', 'expenses')->middleware('role:admin,superadmin,accountant')->name('expenses');
        Route::view('salaries', 'salaries')->middleware('role:admin,superadmin,accountant')->name('salaries');
        Route::view('expcategories', 'expcategories')->middleware('role:admin,superadmin')->name('expcategories');
        Route::view('expsubcategories', 'expsubcategories')->middleware('role:admin,superadmin')->name('expsubcategories');
        Route::view('refcode', 'referalcode')->middleware('role:admin,superadmin')->name('referalcode');
        Route::view('banks', 'banks')->middleware('role:admin,superadmin')->name('banks');
        //other routes
        Route::view('setting', 'setting')->middleware('role:admin,superadmin')->name('setting');
        Route::view('profile', 'profile')->middleware('role:admin,superadmin,accountant,moderator,supervisor,sales,teacher,student')->name('profile');
        //print controllers
        Route::get('pdf/cert/show/{id}', [PdfController::class, 'showCertificate'])->name('showpdf');
        Route::get('pdf/cert/download/{id}', [PdfController::class, 'downloadCertificate'])->name('downloadpdf');
        Route::get('pdf/teacher/show/{id}', [PdfController::class, 'showteacehr'])->name('showteacher');
        Route::get('pdf/teacher/download/{id}', [PdfController::class, 'downloadteacher'])->name('downloadteacher');
        //logout controller
        Route::get('logout', [LogoutController::class, 'logout']);

        // Route::view('callback', [MyFatoorahController::class, 'callback'])->name('myfatoorah.callback');
        // Route::view('callback', [MyFatoorahController::class, 'callback'])->name('myfatoorah.callback');
        // Route::view('payment/course/{id}', [MyFatoorahController::class, 'getPayLoadData'])->name('myfatoorah.checkout');
        // Route::view('order', 'order')->name('order');
        // Route::get('payment/course/{id}', [PaymentController::class, 'checkout'])->name('payment');
        // Route::get('callback', [PaymentController::class, 'callback'])->name('callback');
        // Route::get('error', [PaymentController::class, 'error'])->name('error');
        Route::get('myfatoorah/{product}/{proid}/{userid}', [MyFatoorahController::class, 'index'])->name('myfatoorah.index');
        Route::get('callback', [MyFatoorahController::class, 'callback'])->name('myfatoorah.callback');

    }
);

Route::view('welcome', 'welcome')->name('welcome');
Route::get('notactive', function(){
    return 'Your Account Suspended, You Need To Contact System Administrator';
})->name('welcome');
require __DIR__.'/auth.php';
