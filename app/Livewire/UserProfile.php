<?php

namespace App\Livewire;

use App\Models\Certificate;
use App\Models\ReferalCode;
use App\Models\User;
use App\Models\ZoomApp;
use App\Traits\UseDefault;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\WithFileUploads;

class UserProfile extends Component
{
    use WithFileUploads, UseDefault;

    public $userid = '';
    public $name = '';
    public $username = '';
    public $password = '';
    public $email = '';
    public $role = '';
    public $phone = '';
    public $country = '';
    public $image;
    public $oldimage;
    public $showpass = false;
    // public $irritation = 000;

    // from student model
    public $studentuserid = '';
    public $dateofbirth = '';
    public $subscription = '';
    public $gender_prefer = '';
    public $paid = '';
    public $numofclasses = '';
    public $ref_code = '';
    public $balance;
    public $startdate;
    public $inv_code = '';
    public $allcertificates = [];

    // from teacher zoom app model
    public $zoom_user_id;
    public $account_id;
    public $client_id;
    public $client_secret;


    public function mount()
    {
        $this->name = auth()->user()->name;
        $this->username = auth()->user()->username;
        $this->password = '';
        $this->email = auth()->user()->email;
        $this->phone = auth()->user()->phone;
        $this->country = auth()->user()->country;
        $this->role = auth()->user()->role;
        $this->oldimage = auth()->user()->image;

        if ($this->role == 'student') {
            $refcode = ReferalCode::where('short_code', 'like', '%' . '$id' . '%')->first();
            $this->subscription = auth()->user()->student->subscription;
            $this->paid = auth()->user()->student->paid;
            $this->numofclasses = auth()->user()->student->numofclasses;
            $this->balance = auth()->user()->student->balance;
            $this->inv_code = str_replace('$id', auth()->user()->id, $refcode->short_code);
            $this->allcertificates = Certificate::filterstudent(auth()->user()->id)->get();
        }
        if ($this->role == 'teacher')
        {
            $this->zoom_user_id = auth()->user()->zoominfo->zoom_user_id ?? '';
            $this->account_id = auth()->user()->zoominfo->account_id ?? '';
            $this->client_id = auth()->user()->zoominfo->client_id ?? '';
            $this->client_secret = auth()->user()->zoominfo->client_secret ?? '';
        }
    }
    public function showcert($id)
    {
        $url = 'pdf/show/' . $id;
        $this->redirectToUrl($url);
    }
    public function printcert($id)
    {
        $url = 'pdf/download/' . $id;
        $this->redirectToUrl($url);
    }
    public function redirectToUrl($url)
    {
        $this->dispatch('open-new-window', $url);
    }

    public function updatedimage()
    {
        $this->oldimage = $this->image;
    }

    public function getsubscription($subscription)
    {
        switch ($subscription) {
            case 'trial':
                return 'تجريبي';
                break;

            case 'fixed':
                return 'مشترك';
                break;

            default:
                return 'منتهي';
                break;
        }
    }

    public function removeimage()
    {
        $this->reset([
            'image',
            'oldimage',
        ]);
    }

    public function save()
    {
        $validated = $this->validate([
            'name' => 'required|string|min:3|max:100',
            'username' => 'required|string|min:3|max:100',
            'email' => 'required|email|min:5|max:100',
            'phone' => 'required|string|min:11|max:20',
            'country' => 'required|string|min:3|max:40',
            'image' => 'nullable|sometimes|image|max:1024',
        ]);

        if(!empty($this->password))
        {
            $validated = $this->validate([
                'name' => 'required|string|min:3|max:100',
                'username' => 'required|string|min:3|max:100',
                'email' => 'required|email|min:5|max:100',
                'password' => 'required|string|min:8|max:25',
                'phone' => 'required|string|min:11|max:20',
                'country' => 'required|string|min:3|max:40',
                'image' => 'nullable|sometimes|image|max:1024',
            ]);
            // dd($validated['password']);
            $validated['password'] = Hash::make($validated['password']);
        } else {
            $validated = $this->validate([
                'name' => 'required|string|min:3|max:100',
                'username' => 'required|string|min:3|max:100',
                'email' => 'required|email|min:5|max:100',
                'password' => 'nullable|string|min:8|max:12',
                'phone' => 'required|string|min:11|max:20',
                'country' => 'required|string|min:3|max:40',
                'image' => 'nullable|sometimes|image|max:1024',
            ]);
            $validated['password'] = auth()->user()->password;
        }

        if(!empty($this->image))
        {
            $validated['image'] = $this->image->store('uploads/profile', 'public');
        } else {
            $validated['image'] = $this->oldimage;
        }

        if(auth()->user()->role == 'teacher')
        {
            $validatedzoomapp = $this->validate([
                'zoom_user_id' => 'required|string|max:12',
                'account_id' => 'required|string|max:22',
                'client_id' => 'required|string|max:22',
                'client_secret' => 'required|string|max:32',
            ]);
            $validatedzoomapp['user_id'] = auth()->user()->id;

            $user_zoomapp = ZoomApp::where('user_id', auth()->user()->id)->first();
            auth()->user()->teacher->update([
                'zoomuser_id' => $validatedzoomapp['zoom_user_id'],
            ]);

            if(!empty($user_zoomapp))
            {
                $user_zoomapp->update($validatedzoomapp);
            }else{
                ZoomApp::create($validatedzoomapp);
            }
        }


        User::find(auth()->user()->id)->update($validated);
        // $this->irritation++;

        $this->dispatch('flashupdated');
    }

    public function render()
    {
        return view('livewire.user-profile');
    }
}
