<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class lectures extends Model
{
    use HasFactory;

    protected $table = 'lectures';

    protected $fillable = [
        'name',
        'description',
        'course_id',
        'teacher_id',
        'price',
        'video_url',
        'access_code',
        'notes',
        'lastupdateuser_id',
    ];

    public function course()
    {
        return $this->belongsTo(courses::class, 'course_id', 'id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'lastupdateuser_id', 'id');
    }


    public function scopeSearch($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->where('name', 'like', '%' . $value . '%');
        });
    }
    public function scopeFiltercourse($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->whereHas('course', function($query) use ($value){
                return $query->where('id', $value);
            });
        });
    }
    public function scopeFilterteacher($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->whereHas('course', function($query) use ($value){
                return $query->whereHas('teacher', function($query) use ($value){
                    return $query->where('id', $value);
                });
            });
        });
    }
}
