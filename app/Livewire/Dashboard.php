<?php

namespace App\Livewire;

use App\Models\Banks;
use App\Models\Clss;
use App\Models\Expenses;
use App\Models\SalesReport;
use App\Models\Settings;
use App\Models\User;
use App\Traits\UseDefault;
use Carbon\Carbon;
use Livewire\Component;

class Dashboard extends Component
{
    use UseDefault;

    public $settings;

    public $endedclasses = '';
    public $maleteachers = '';
    public $rolesnames = [];
    public $rolesorder = [];
    public $popteacher = [];
    public $numteacherclass = [];

    //teachers reports
    public $allteachers = 0;
    public $allusers = 0;
    public $allclasses = 0;

    // students reports
    public $allstudents = 0;
    public $fixstudents = 0;
    public $tristudents = 0;
    public $endstudents = 0;

    // budget reports
    public $currbudget = 0;
    public $prevmonthbudget = 0;
    public $monthbeforebudget = 0;

    // student report 6-month chart
    public $currentmonthnewstudentreport;
    public $currentmonthresubstudentreport;
    public $lastmonthnewstudentreport;
    public $lastmonthresubstudentreport;
    public $lastcyclereportnewstudents = [];
    public $lastcyclereportresubstudents = [];
    public $reportcycle = '6-month';
    public $currmonths = [];

    public function startmonth($num = null)
    {
        return Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->shortMonthName;
    }

    public function starttoday()
    {
        return Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'));
    }

    public function submonthtoday($regstatus, $num = null)
    {
        $start = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->startOfMonth();
        $end = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->endOfMonth();
        return SalesReport::filterregstatus($regstatus)->filterdate($start, $end)->count();
    }

    public function calculatetwomonthesstudentreport()
    {
        $currstart = $this->starttoday()->startOfMonth();
        $currend = $this->starttoday()->endOfMonth();
        $this->currentmonthnewstudentreport = SalesReport::filterregstatus('new')->filterdate($currstart, $currend)->count();
        $this->currentmonthresubstudentreport = SalesReport::filterregstatus('resub')->filterdate($currstart, $currend)->count();
        $this->lastmonthnewstudentreport = $this->submonthtoday('new', 1);
        $this->lastmonthresubstudentreport = $this->submonthtoday('resub', 1);
    }

    public function calculatesixmonthesstudentreport()
    {
        $this->currmonths = [
            $this->startmonth(5),
            $this->startmonth(4),
            $this->startmonth(3),
            $this->startmonth(2),
            $this->startmonth(1),
            $this->starttoday()->shortMonthName,
        ];
        $this->lastcyclereportnewstudents = [
            $this->submonthtoday('new', 5),
            $this->submonthtoday('new', 4),
            $this->submonthtoday('new', 3),
            $this->submonthtoday('new', 2),
            $this->submonthtoday('new', 1),
            $this->currentmonthnewstudentreport,
        ];
        $this->lastcyclereportresubstudents = [
            $this->submonthtoday('resub', 5),
            $this->submonthtoday('resub', 4),
            $this->submonthtoday('resub', 3),
            $this->submonthtoday('resub', 2),
            $this->submonthtoday('resub', 1),
            $this->currentmonthresubstudentreport,
        ];
    }

    public function calculateyearstudentreport()
    {
        $this->currmonths = [
            $this->startmonth(11),
            $this->startmonth(10),
            $this->startmonth(9),
            $this->startmonth(8),
            $this->startmonth(7),
            $this->startmonth(6),
            $this->startmonth(5),
            $this->startmonth(4),
            $this->startmonth(3),
            $this->startmonth(2),
            $this->startmonth(1),
            $this->starttoday()->shortMonthName,
        ];
        $this->lastcyclereportnewstudents = [
            $this->submonthtoday('new', 11),
            $this->submonthtoday('new', 10),
            $this->submonthtoday('new', 9),
            $this->submonthtoday('new', 8),
            $this->submonthtoday('new', 7),
            $this->submonthtoday('new', 6),
            $this->submonthtoday('new', 5),
            $this->submonthtoday('new', 4),
            $this->submonthtoday('new', 3),
            $this->submonthtoday('new', 2),
            $this->submonthtoday('new', 1),
            $this->currentmonthnewstudentreport,
        ];
        $this->lastcyclereportresubstudents = [
            $this->submonthtoday('resub', 11),
            $this->submonthtoday('resub', 10),
            $this->submonthtoday('resub', 9),
            $this->submonthtoday('resub', 8),
            $this->submonthtoday('resub', 7),
            $this->submonthtoday('resub', 6),
            $this->submonthtoday('resub', 5),
            $this->submonthtoday('resub', 4),
            $this->submonthtoday('resub', 3),
            $this->submonthtoday('resub', 2),
            $this->submonthtoday('resub', 1),
            $this->currentmonthresubstudentreport,
        ];
    }
    public function getsum($start, $end)
    {
        $totalexpense = Expenses::filternotexpcate(10)
            ->filterdate($start, $end)
            ->sum('amount') / $this->settings->egyptrate; // for converting to SAR
        // exclude bank withdraws in expenses reports
        // $banks = Banks::all();
        // foreach ($banks as $bank) {
        //     $banksexpensesinsar = Expenses::filterexpcate(10)
        //     ->filterexpsubcate($bank->id)
        //     ->filterdate($start, $end)
        //     ->sum('amount') / $this->convertPaidToSAR($bank->country);
        //     $totalexpense += $banksexpensesinsar;
        // }
        // dd($totalexpense);
        return round($totalexpense);
    }

    public function budgetmonthtoday($num = null)
    {
        $start = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->startOfMonth();
        $end = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->endOfMonth();
        $thesales = SalesReport::filterdate($start, $end)->sum('paid');
        $theexpenses = $this->getsum($start, $end);
        return $thesales - $theexpenses;
    }

    public function rolesordercalc()
    {
        $toproles = [];
        $toprolescount = 1;
        $roles = User::pluck('role');
        foreach($roles as $role)
        {
            if(isset($toproles[$role]))
            {
                $toproles[$role] += $toprolescount;
            } else {
                $toproles[$role] = $toprolescount;
            }
        }
        arsort($toproles);
        return $toproles;
    }
    public function teacherclassescalc()
    {
        $topteacherclasses = [];
        $realteacherclasses = [];
        $popteacherclasses = [];
        $topteacherclassescount = 1;
        $teachers = Clss::pluck('teacher_id');
        $teachersexist = User::whereIn('id', $teachers);
        // dd($teachersexist);
        // dd($teachers);
        if(!empty($teachers))
        {
            foreach($teachers as $teacher)
            {
                if(isset($topteacherclasses[$teacher]))
                {
                    $topteacherclasses[$teacher] += $topteacherclassescount;
                } else {
                    $topteacherclasses[$teacher] = $topteacherclassescount;
                }
            }
            arsort($topteacherclasses);
            foreach ($topteacherclasses as $teacher => $numclasses)
            {
                $realteacherclasses[User::find($teacher)->name] = $numclasses;
            }
            $popteacherclasses = array_slice($realteacherclasses, 0, 5);
            foreach ($popteacherclasses as $key => $value)
            {
                $this->popteacher[] = $key;
                $this->numteacherclass[] = $value;
            }
        }
    }
    public function mount()
    {
        $this->calculatetwomonthesstudentreport();
        $this->teacherclassescalc();
        $this->calculatesixmonthesstudentreport();
        $this->settings = Settings::find(1);
        $this->allteachers = User::filterrole('teacher')->count();
        $this->allusers = User::all()->count();
        $this->allclasses = Clss::all()->count();
        $this->endedclasses = Clss::filteractive('ended')->count();
        $this->maleteachers = User::filterrole('teacher')->filtergender('male')->count();
        // $this->fixstudents = User::filterrole('student')->filtersubscription('fixed')->count();
        // $this->tristudents = User::filterrole('student')->filtersubscription('trial')->count();
        // $this->endstudents = User::filterrole('student')->filtersubscription('suspended')->count();
        $this->allstudents = $this->fixstudents + $this->tristudents + $this->endstudents;
        $this->currbudget = $this->budgetmonthtoday();
        $this->prevmonthbudget = $this->budgetmonthtoday(1);
        $this->monthbeforebudget = $this->budgetmonthtoday(2);
        // dd($this->lastcyclereportnewstudents);
    }

    public function render()
    {
        return view('livewire.dashboard');
    }
}
