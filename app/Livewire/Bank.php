<?php

namespace App\Livewire;

use App\Models\Banks;
use App\Models\Settings;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;
use App\Traits\UseDefault;
use App\Traits\UseFilter;

class Bank extends Component
{
    use WithPagination, UseFilter, UseDefault;

    public $selectedbanks = [];
    public $selectall = false;

    public $bank_id;
    public $name = '';
    public $country = '';
    public $accountuser = '';
    public $balance = 0;
    public $notes = '';

    public $getTotalBalanceInSAR = 0;

    public function save()
    {
        $validated = $this->validate([
            'country' => 'required|string|max:100',
            'name' => 'required|unique:banks,name|string|max:250',
            'accountuser' => 'required|string|max:500',
            'balance' => 'required|numeric|min:0',
            'notes' => 'string|max:500',
        ]);
        Banks::create($validated);
        $this->closemodal('add');
        $this->dispatch('flashsaved');
    }
    public function showedit(Banks $bank)
    {
        $this->bank_id = $bank->id;
        $this->name = $bank->name;
        $this->country = $bank->country;
        $this->accountuser = $bank->accountuser;
        $this->balance = $bank->balance;
        $this->notes = $bank->notes;
        $this->openmodal('edit');
    }
    public function update()
    {
        $validated = $this->validate([
            'country' => 'required|string|max:100',
            'name' => 'required|unique:banks,name,' . $this->bank_id . '|string|max:250',
            'accountuser' => 'required|string|max:500',
            'balance' => 'required|numeric',
            'notes' => 'string|max:500',
        ]);

        Banks::find($this->bank_id)->update($validated);

        $this->closemodal('edit');
        $this->dispatch('flashupdated');
    }
    public function showdelete(Banks $bank)
    {
        $this->bank_id = $bank->id;
        $this->name = $bank->name;
        $this->notes = $bank->notes;
        $this->openmodal('delete');
    }
    public function delete()
    {
        Banks::find($this->bank_id)->delete();

        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }
    public function deleteselected()
    {
        Banks::whereIn('id', $this->selectedbanks)->delete();

        $this->selectedbanks = [];
        $this->closemodal('deleteselected');
    }


    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedbanks = Banks::paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedbanks = [];
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'bank_id',
            'country',
            'name',
            'accountuser',
            'balance',
            'notes',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function getTotalBalanceInSAR()
    {
        $banks = Banks::all();
        $totalBalanceInSAR = 0;
        $bankcountrybalance = [];
        foreach ($banks as $bank) {
            if(isset($bankcountrybalance[$bank->country]))
            {
                $bankcountrybalance[$bank->country] += $bank->balance;
            } else {
                $bankcountrybalance[$bank->country] = $bank->balance;
            }
        }
        // dd($bankcountrybalance);
        foreach ($bankcountrybalance as $country => $balance) {
            $totalBalanceInSAR += $balance / $this->convertPaidToSAR($country);
        }
        return $totalBalanceInSAR;
    }
    // public function getBalanceInSAR($country)
    // {
    //     $balance = Banks::filtercountry($country)->sum('balance');
    //     return $balance / $this->convertPaidToSAR($country);
    // }
    public function mount()
    {
        $this->settings = Settings::find(1);
        if(empty($this->perpage))
        {
            $this->perpage = $this->settings->perpage;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'ASC';
        $this->getTotalBalanceInSAR = $this->getTotalBalanceInSAR();
        // dd($this->getTotalBalanceInSAR, $this->getBalanceInSAR('مصر'));
    }
    public function render()
    {
        return view('livewire.bank', [
            'banks' => Banks::filtercountry($this->filtercountry)
            ->search($this->search)
            ->paginate($this->perpage),
        ]);
    }
}
