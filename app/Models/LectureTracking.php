<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LectureTracking extends Model
{
    use HasFactory;

    protected $fillabe = [
        'user_id',
        'lecture_id',
        'finished',
    ];
    protected $table = 'lecture_trackings';

    protected $casts = [
        'finished' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    public function lecture()
    {
        return $this->belongsTo(lectures::class, 'lecture_id', 'id');
    }
}
