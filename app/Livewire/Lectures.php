<?php

namespace App\Livewire;

use App\Models\Courses;
use App\Models\lectures as ModelsLectures;
use App\Models\Notification;
use App\Models\Settings;
use App\Models\User;
use App\Traits\UseDefault;
use App\Traits\UseFilter;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;

use Livewire\Component;
use Livewire\WithPagination;


class Lectures extends Component
{
    use WithPagination, UseFilter, UseDefault;

    public $selectedlectures = [];
    public $selectall = false;

    public $lecture_id;
    public $name = '';
    public $description = '';
    public $course_id;
    public $teacher_id;
    public $price;
    public $video_url = '';
    public $access_code;
    public $notes = '';
    public $lastupdateuser_id;
    public $lastupdatedat;

    public $courses = [];
    public $teachers = [];

    public function save()
    {
        $validated = $this->validate([
            'name' => 'required|string|max:120',
            'description' => 'required|string|min:20|max:500',
            'course_id' => 'required|numeric|min:1|max:999',
            'teacher_id' => 'required|numeric|min:1|max:999',
            'price' => 'required|integer|min:0|max:10000',
            'video_url' => 'required|string|max:250',
            'access_code' => 'required|string|max:120',
            'notes' => 'string|max:500',
            'lastupdateuser_id' => '',
        ]);
        $validated['lastupdateuser_id'] = auth()->user()->id;

        $lecture = ModelsLectures::create($validated);

        $notifiedusers = User::whereIn('role', ['superadmin','admin','acountant'])->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'new lecture',
                'url' => 'lectures?q=' . $lecture->name,
                'message' => 'تم اضافة محاضرة جديدة باسم "' . $lecture->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }

        $this->closemodal('add');
        $this->dispatch('flashsaved');
    }
    public function showedit(ModelsLectures $lecture)
    {
        $this->lecture_id = $lecture->id;
        $this->name = $lecture->name;
        $this->description = $lecture->description;
        $this->course_id = $lecture->course_id;
        $this->teacher_id = $lecture->teacher_id;
        $this->price = $lecture->price;
        $this->video_url = $lecture->video_url;
        $this->access_code = $lecture->access_code;
        $this->notes = $lecture->notes;
        $this->lastupdateuser_id = $lecture->user->name;
        $this->lastupdatedat = Carbon::parse($lecture->updated_at)->format('Y-m-d || A g:i:s');
        $this->openmodal('edit');
    }
    public function update()
    {
        $validated = $this->validate([
            'name' => 'required|string|max:120',
            'description' => 'required|string|max:500',
            'teacher_id' => 'required|numeric|max:999',
            'price' => 'required|integer|min:0|max:10000',
            'access_code' => 'required|string|max:120',
            'notes' => 'string|max:500',
            'lastupdateuser_id' => '',
        ]);
        $validated['lastupdateuser_id'] = auth()->user()->id;

        $lecture = ModelsLectures::find($this->lecture_id);
        $lecture->update($validated);

        $notifiedusers = User::where('role', 'admin')->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'lecture updated',
                'url' => 'lectures?q=' . $lecture->name,
                'message' => 'تم تعديل محاضرة باسم "' . $lecture->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }

        $this->closemodal('edit');
        $this->dispatch('flashupdated');
    }
    public function showdelete(ModelsLectures $lecture)
    {
        $this->lecture_id = $lecture->id;
        $this->name = $lecture->name;
        $this->notes = $lecture->notes;
        $this->openmodal('delete');
    }
    public function delete(){

        $lecture = ModelsLectures::find($this->lecture_id);
        $lecture->delete();

        $notifiedusers = User::where('role', 'admin')->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'lecture deleted',
                'url' => '',
                'message' => 'تم حذف محاضرة باسم "' . $lecture->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }
        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }
    public function deleteselected()
    {
        ModelsLectures::whereIn('id', $this->selectedlectures)->delete();
        $this->selectedlectures = [];
        $this->closemodal('deleteselected');
    }
    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedlectures = ModelsLectures::paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedlectures = [];
        }
    }
    #[Computed]
    public function updated($course_id)
    {
        if(!empty($course_id))
        {
        $this->teacher_id = Courses::find($this->course_id)->teacher->id ?? '';
        // dd($this->teacher_id);
        } else {
            $this->teacher_id = '';
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'lecture_id',
            'name',
            'description',
            'course_id',
            'teacher_id',
            'price',
            'video_url',
            'access_code',
            'notes',
            'lastupdateuser_id',
            'lastupdatedat',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function generateCode()
    {
        $this->access_code = Str::random(16);
    }
    public function mount()
    {
        $this->settings = Settings::find(1);
        $this->teachers = User::filterrole('teacher')->get();
        $this->courses = Courses::all();

        if (empty($this->perpage)) {
            $this->perpage = $this->settings->perpage;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'DESC';
    }
    public function render()
    {
        return view('livewire.lectures', [
            'lectures' => ModelsLectures::filterteacher($this->filterteacher)
            ->orderBy($this->sortBy, $this->sortDir)
            ->search($this->search)
            ->paginate($this->perpage),
        ]);
    }
}
