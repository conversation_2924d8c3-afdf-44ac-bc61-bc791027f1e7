<!DOCTYPE html>
<html lang="ar">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
        <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body style="text-align:center;font-family: 'Tajawal', sans-serif;margin:0;padding:0;" class="antialiased">
        <main style="width:100%;margin: 0 auto;">
            <div style="width:100%;margin: 0 auto;background-image:url('assets/images/teacher-bg.jpeg');background-size: contain;background-attachment:fixed;background-repeat:no-repeat;width:100%;height:100%;" class="max-w-7xl mx-auto p-6 lg:p-8">
                <table dir="rtl" width="90%" cellpadding="4" cellspacing="0" style="padding-top:150px;text-family:'Tajawal'; font-size:20px;font-weight:bold;">
                    <col width="128*"/>

                    <col width="64*"/>

                    <col width="64*"/>

                    <tr>
                        <td align="center" colspan="3" width="100%" valign="top" style=" padding: 0.04in">
                            <h2>بيانات المعلم " {{ $data['name'] }} "</h2>
                        </td>
                    </tr>
                    <tr valign="top">
                        <td align="right" width="25%" style="padding-top: 0in; padding-bottom: 0.04in; padding-left: 0.04in; padding-right: 0.04in">
                            <h3>الاسم:</h3>
                        </td>
                        <td width="25%" align="right" style="padding-top: 0in; padding-bottom: 0.04in; padding-left: 0.04in; padding-right: 0in">
                            {{ $data['name'] }}
                        </td>
                        <td rowspan="3" align="center" width="50%" style="padding-top: 0in; padding-bottom: 0.04in; padding-left: 0.04in; padding-right: 0in">
                            <img src="{{ $data['image'] }}" alt="user" width="208" height="208" />
                        </td>
                    </tr>
                    <tr valign="top">
                        <td align="right" width="25%" style="padding-top: 0in; padding-bottom: 0.04in; padding-left: 0.04in; padding-right: 0.04in">
                            <h3>البلد:</h3>
                        </td>
                        <td align="right" width="25%" style="padding-top: 0in; padding-bottom: 0.04in; padding-left: 0.04in; padding-right: 0in">
                            {{ $data['country'] }}
                        </td>
                    </tr>
                    <tr valign="top">
                        <td align="right" width="25%" style="padding-top: 0in; padding-bottom: 0.04in; padding-left: 0.04in; padding-right: 0.04in">
                            <h3>النوع</h3>
                        </td>
                        <td align="right" width="25%" style="padding-top: 0in; padding-bottom: 0.04in; padding-left: 0.04in; padding-right: 0in">
                            {{ $data['gender'] == 'female' ? 'انثى' : 'ذكر' }}
                        </td>
                    </tr>
                    <tr valign="top">
                        <td align="right" width="25%" style="vertical-align:top;padding-top: 0in; padding-bottom: 0.04in; padding-left: 0.04in; padding-right: 0.04in">
                            <h3>المهارات:</h3>
                        </td>
                        <td align="right" colspan="2" width="75%" style="padding-top: 0in; padding-bottom: 0.04in; padding-left: 0.04in; padding-right: 0in">

                            @foreach ($data['qualifications'] as $quali)
                                ◉ " {{ $quali }} " <br>
                            @endforeach
                        </td>
                    </tr>
                </table>
            </div>
            @foreach ($data['upfiles'] as $file)
            <pagebreak />
                " {{ $file->name }} " <br>
                @if(substr($file->url, -3) == 'jpg' || substr($file->url) == 'jpeg' || substr($file->url) == 'png')
                <img src="storage/{{ $file->url }}" />
                @else
                <iframe
                    src="{{ $file->url }}"
                    width="100%"
                    height="100%"
                    loading="lazy"
                ></iframe>
                @endif
            @endforeach
        </main>
    </body>
</html>
