<?php

namespace App\Livewire;

use App\Models\Clss;
use App\Models\Ratings;
use App\Models\Settings;
use App\Models\Teacher;
use App\Models\User;
use App\Traits\UseFilter;
use Livewire\Attributes\Computed;
use Livewire\WithPagination;
use Livewire\Component;

class TeacherRatings extends Component
{
    use WithPagination, UseFilter;

// selected / select all
    public $teacherloggedinduration = 0;
    public $studentloggedinduration = 0;
    public $teacherrate = 0;

    public $class_id = '';

    // teacher / supervisor reports
    public $internet;
    public $camera;
    public $smartboard;
    public $audio;
    public $teachbackground;
    public $teachenvironment;
    public $teachrelationship;
    public $teacheducational;
    public $notes = 'اكتب ملاحظاتك هنا ...';
    public $totalevaluation;

    // default properties
    public $students = [];
    public $teachers = [];
    public $supervisors = [];

    public function suprepview(Clss $class)
    {
        $this->class_id = $class->id;
        $this->internet = $class->supreport->internet;
        $this->camera = $class->supreport->camera;
        $this->smartboard = $class->supreport->smartboard;
        $this->audio = $class->supreport->audio;
        $this->teachbackground = $class->supreport->teachbackground;
        $this->teachenvironment = $class->supreport->teachenvironment;
        $this->teachrelationship = $class->supreport->teachrelationship;
        $this->teacheducational = $class->supreport->teacheducational;
        $this->notes = $class->supreport->notes;
        $this->openmodal('view-supervisor-report');
    }

    public function totalevaluation($class = null)
    {
        if (isset($class)) {
            return (($class->internet + $class->camera + $class->smartboard + $class->audio + $class->teachbackground + $class->teachenvironment + $class->teachrelationship + $class->teacheducational) / 80 ) * 100;
        }
        return (($this->internet + $this->camera + $this->smartboard + $this->audio + $this->teachbackground + $this->teachenvironment + $this->teachrelationship + $this->teacheducational) / 80 ) * 100;
    }

    // methods for alpinejs modals
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'class_id',
            'internet',
            'camera',
            'smartboard',
            'audio',
            'teachbackground',
            'teachenvironment',
            'teachrelationship',
            'teacheducational',
            'notes',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }

//update teacher rate per class
    #[Computed]
    public function updatedfilterteacher()
    {
        $this->teacherrate = Teacher::where('user_id', $this->filterteacher)->first()->rateperclass ?? 0;
    }
    public function mount()
    {
        $this->perpage = Settings::find(1)->perpage;
        $this->sortBy = 'id';
        $this->sortDir = 'ASC';
        $this->teachers = User::filterrole('teacher')->get();
    }
    public function render()
    {
        return view('livewire.teacher-ratings', [
            'ratings' => Ratings::filterdate($this->filterdatestart, $this->filterdateend)
            ->filterteacher($this->filterteacher)
            ->search($this->search)
            ->orderBy($this->sortBy, $this->sortDir)
            ->paginate($this->perpage),
        ]);
    }
}
