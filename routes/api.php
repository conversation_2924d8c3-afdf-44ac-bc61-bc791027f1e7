<?php

use App\Http\Controllers\ApiController;
use App\Http\Controllers\ApiUserRegisterController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
//     return $request->user();
// });

// Route::get('users', [ApiController::class, 'userIndex']);
// Route::get('user/{id}', [ApiController::class, 'userShow']);

// Route::get('certificates', [ApiController::class, 'certificateIndex']);
// Route::get('certificate/{id}', [ApiController::class, 'certificateShow']);

// Route::get('courses', [ApiController::class, 'courseIndex']);
// Route::get('course/{id}', [ApiController::class, 'courseShow']);

// Route::get('lectures', [ApiController::class, 'lectureIndex']);
// Route::get('lecture/{id}', [ApiController::class, 'lectureShow']);

Route::post('user/register', [ApiUserRegisterController::class, 'register']);
Route::post('user/login', [ApiUserRegisterController::class, 'login']);

Route::group(['middleware' => 'auth:sanctum'], function ()
{
    Route::get('users', [ApiController::class, 'userIndex']);
    Route::get('user/{id}', [ApiController::class, 'userShow']);

    Route::get('certificates', [ApiController::class, 'certificateIndex']);
    Route::get('certificate/{id}', [ApiController::class, 'certificateShow']);

    Route::get('courses', [ApiController::class, 'courseIndex']);
    Route::get('course/{id}', [ApiController::class, 'courseShow']);

    Route::get('lectures', [ApiController::class, 'lectureIndex']);
    Route::get('lecture/{id}', [ApiController::class, 'lectureShow']);


    Route::post('user/logout', [ApiUserRegisterController::class, 'logout']);
});
