<div>
    <!-- flash update successful message -->
    <x-flash-message on="flashupdated">
        <div class="flex items-center bg-yellow-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تعديل العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    <div class="card-body flex flex-col p-6">
        <header class="flex mb-5 items-center border-b border-slate-100 dark:border-slate-700 pb-5 -mx-6 px-6">
            <h4 class="text-primary-500 font-bold text-3xl">الإعدادات</h4>
        </header>
        <form action="update-settings">
            <div class="mb-4">
                <label for="classperiod" class="block text-xl font-medium text-gray-700 dark:text-[#016241] mb-2">مدة الحصة:</label>
                <input type="text" wire:model.live="classperiod" id="classperiod" class="shadow-sm rounded-md lg:w-1/2 w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="مدة الحصة" required>
                @error('classperiod')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="perpage" class="block text-xl font-medium text-gray-700 dark:text-[#016241] mb-2">عدد العناصر في كل صفحة:</label>
                <select wire:model.live="perpage" id="role" style="direction:ltr;" class="w-1/2 shadow-sm rounded-md px-2 py-1 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option style="direction:rtl;" value="10">10</option>
                    <option style="direction:rtl;" value="20">20</option>
                    <option style="direction:rtl;" value="30">30</option>
                    <option style="direction:rtl;" value="50">50</option>
                    <option style="direction:rtl;" value="2000">الكل</option>
                </select>
                @error('perpage')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="safe" class="block text-xl font-medium text-gray-700 dark:text-[#016241] mb-2">رصيد الخزينة:</label>
                <input type="text" wire:model.live="safe" id="safe" class="shadow-sm rounded-md lg:w-1/2 w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="رصيد الخزينة" required>
                @error('safe')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="tax" class="block text-xl font-medium text-gray-700 dark:text-[#016241] mb-2">نسبة الضرائب:</label>
                <input type="text" wire:model.live="tax" id="tax" class="shadow-sm rounded-md lg:w-1/2 w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="نسبة الضرائب" required> <span class="text-xl font-medium text-gray-700 dark:text-[#016241] mb-2"> % </span>
                @error('tax')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="meeting_app" class="block text-xl font-medium text-gray-700 dark:text-[#016241] mb-2">برنامج الاجتماعات:</label>
                <select wire:model.live="meeting_app" id="meeting_app" style="direction:ltr;" class="w-1/2 shadow-sm rounded-md lg:w-1/2 px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                    <option style="direction:rtl;" value="zoom">Zoom Meeting</option>
                    <option style="direction:rtl;" value="google">Google Meet</option>
                </select>
                @error('meeting_app')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="saudirate" class="block text-xl font-medium text-gray-700 dark:text-[#016241] mb-2">معدل الريال السعودي:</label>
                <div class="flex justify-start gap-4">
                    <input type="text" wire:model.live="saudirate" id="saudirate" class="shadow-sm rounded-md lg:w-1/2 w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="معدل الريال السعودي" required>
                    <button type="button" wire:loading.remove wire:click.prevent="refreshrate" class="flex justify-start gap-2 place-items-center py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-lg font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"><iconify-icon icon="heroicons:arrow-path-rounded-square-16-solid" style="font-size: 24px;color:#ffffff;font-weight: bold;" alt="حذف"></iconify-icon> تحديث</button>

                    <x-loading method="refreshrate"></x-loading>

                </div>
                @error('saudirate')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="egyptrate" class="block text-xl font-medium text-gray-700 dark:text-[#016241] mb-2">معدل الجنية المصري:</label>
                <input type="text" wire:model.live="egyptrate" id="egyptrate" class="shadow-sm rounded-md lg:w-1/2 w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="معدل الجنية المصري" required>
                @error('egyptrate')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="uaerate" class="block text-xl font-medium text-gray-700 dark:text-[#016241] mb-2">معدل الدرهم الاماراتي:</label>
                <input type="text" wire:model.live="uaerate" id="uaerate" class="shadow-sm rounded-md lg:w-1/2 w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="معدل الدرهم الاماراتي" required>
                @error('uaerate')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="kuwaitrate" class="block text-xl font-medium text-gray-700 dark:text-[#016241] mb-2">معدل الدينار الكويتي:</label>
                <input type="text" wire:model.live="kuwaitrate" id="kuwaitrate" class="shadow-sm rounded-md lg:w-1/2 w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="معدل الدينار الكويتي" required>
                @error('kuwaitrate')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="qatarrate" class="block text-xl font-medium text-gray-700 dark:text-[#016241] mb-2">معدل الريال القطري:</label>
                <input type="text" wire:model.live="qatarrate" id="qatarrate" class="shadow-sm rounded-md lg:w-1/2 w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="معدل الريال القطري" required>
                @error('qatarrate')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="omanrate" class="block text-xl font-medium text-gray-700 dark:text-[#016241] mb-2">معدل الريال العُماني:</label>
                <input type="text" wire:model.live="omanrate" id="omanrate" class="shadow-sm rounded-md lg:w-1/2 w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="معدل الريال العُماني" required>
                @error('omanrate')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="usdrate" class="block text-xl font-medium text-gray-700 dark:text-[#016241] mb-2">معدل الدولار الامريكي:</label>
                <input type="text" wire:model.live="usdrate" id="usdrate" class="shadow-sm rounded-md lg:w-1/2 w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="معدل الدولار الامريكي" required>
                @error('usdrate')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="flex justify-end">
                <button type="button" wire:loading.remove wire:click.prevent="save" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-lg font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>
                <a href="/" wire:navigate class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm  text-center text-lg font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</a>
            </div>
        </form>
    </div>
</div>
