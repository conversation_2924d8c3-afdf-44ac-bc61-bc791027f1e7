<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;

    protected $table = 'notifications';

    protected $fillable = [
        'user_id',
        'title',
        'message',
        'url',
        'is_seen',
    ];

    public function scopeFilteruser($query, $value)
    {
        return $query->where('user_id', $value);
    }

}
