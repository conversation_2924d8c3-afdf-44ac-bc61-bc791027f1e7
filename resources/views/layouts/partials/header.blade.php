<div class="z-[9]" id="app_header">
    <div class="app-header z-[999] bg-white dark:bg-slate-800 shadow-sm dark:shadow-slate-700">
        <div class="flex justify-between items-center h-full">
            <div class="flex items-center md:space-x-4 space-x-4 rtl:space-x-reverse vertical-box">
                <a href="/" wire:navigate>
                    <x-header-logo class="fill-current text-gray-500" />
                </a>
                <button class="smallDeviceMenuController open-sdiebar-controller hidden xl:hidden md:inline-block">
                    <iconify-icon class="leading-none bg-transparent relative text-xl top-[2px] text-slate-900 dark:text-white" icon="heroicons-outline:menu-alt-3"></iconify-icon>
                </button>
                <button class="sidebarOpenButton text-xl text-slate-900 dark:text-white !ml-0 hidden rtl:rotate-180">
                    <iconify-icon icon="ph:arrow-right-bold"></iconify-icon>
                </button>
            </div>
            <!-- end vertcial -->
            <div class="items-center space-x-4 rtl:space-x-reverse horizental-box">
            <a href="index.html" class="leading-0">
                <span class="xl:inline-block hidden">
                    <a href="/" wire:navigate>
                        <x-header-logo class="fill-current text-gray-500" />
                    </a>
                </span>
                <span class="xl:hidden inline-block">
                    <a href="/" wire:navigate>
                        <x-header-logo class="fill-current text-gray-500" />
                    </a>
                </span>
            </a>
            <button class="smallDeviceMenuController open-sdiebar-controller hidden md:inline-block xl:hidden">
                <iconify-icon class="leading-none bg-transparent relative text-xl top-[2px] text-slate-900 dark:text-white" icon="heroicons-outline:menu-alt-3"></iconify-icon>
            </button>
            </div>
            <!-- end horizental -->

            <!-- end top menu -->
            <div class="nav-tools flex items-center lg:space-x-5 space-x-3 rtl:space-x-reverse leading-0">

            <!-- begin notifications -->
                <livewire:Notifications />
            <!-- end notifications -->

            <!-- BEGIN: Profile Dropdown -->
            <!-- Profile DropDown Area -->
            <div class="md:block hidden w-full">
                <button class="text-slate-800 dark:text-white focus:ring-0 focus:outline-none font-medium rounded-lg text-sm text-center inline-flex items-center" type="button" data-bs-toggle="dropdown" aria-expanded="true">
                <div class="lg:h-12 lg:w-12 h-12 w-12 rounded-full flex-1 ltr:mr-[10px] rtl:ml-[10px] border-2 border-[#016241]">
                    @if (empty(auth()->user()->image))
                        <img src="{{ asset('assets/images/avatar/'). '/' .  auth()->user()->role }}.png" alt="user" class="block w-full h-full object-cover rounded-full bg-gray-900" />
                    @else
                        <image src="storage/{{ auth()->user()->image }}" class="rounded-full block w-full h-full object-fill aspect-square overflow-hidden" />
                    @endif
                </div>
                <span class="flex-none text-slate-600 dark:text-white font-bold text-sm font-cairo items-center lg:flex hidden overflow-hidden text-ellipsis whitespace-nowrap">{{ auth()->user()->name }}</span>
                <svg class="w-[16px] h-[16px] dark:text-white lg:inline-block text-base inline-block ml-[10px] rtl:mr-[10px]" aria-hidden="true" fill="none" stroke="currentColor" viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
                </button>
                <!-- Dropdown menu -->
                <div class="dropdown-menu z-10 hidden bg-[#016241] divide-y divide-slate-100 shadow w-44
                    border dark:border-slate-700 !top-[23px] rounded-md
                    overflow-hidden">
                <ul class="py-1 text-sm text-slate-800 dark:text-slate-200">
                    <li>
                    <a wire:navigate href="profile" class="block px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                        dark:hover:text-white font-inter text-sm text-slate-600
                        dark:text-white font-normal">
                        <iconify-icon icon="heroicons-outline:user" class="relative top-[2px] text-lg ltr:mr-1 rtl:ml-1"></iconify-icon>
                        <span class="font-bold text-sm font-cairo">الملف الشخصي</span>
                    </a>
                    </li>
                    <li>
                        <a wire:navigate href="logout" class="block px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white font-inter text-sm text-slate-600
                            dark:text-white font-normal">
                            <iconify-icon icon="heroicons-outline:login" class="relative top-[2px] text-lg ltr:mr-1 rtl:ml-1"></iconify-icon>
                            <span class="font-bold text-sm font-cairo">تسجيل الخروج</span>
                        </a>
                    </li>
                </ul>
                </div>
            </div>
            <!-- END: Header -->
            <button class="smallDeviceMenuController md:hidden block leading-0">
                <iconify-icon class="cursor-pointer text-slate-900 dark:text-white text-2xl" icon="heroicons-outline:menu-alt-3"></iconify-icon>
            </button>
            <!-- end mobile menu -->
            </div>
            <!-- end nav tools -->
        </div>
    </div>
</div>
    <!-- BEGIN: Search Modal -->
    <div class="modal fade fixed top-0 left-0 hidden w-full h-full outline-none overflow-x-hidden overflow-y-auto inset-0 bg-slate-900/40 backdrop-filter backdrop-blur-sm backdrop-brightness-10" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
        <div class="modal-dialog relative w-auto pointer-events-none top-1/4">
        <div class="modal-content border-none shadow-lg relative flex flex-col w-full pointer-events-auto bg-white dark:bg-slate-900 bg-clip-padding rounded-md outline-none text-current">
            <form>
            <div class="relative">
                <button class="absolute left-0 top-1/2 -translate-y-1/2 w-9 h-full text-xl dark:text-slate-300 flex items-center justify-center">
                <iconify-icon icon="heroicons-solid:search"></iconify-icon>
                </button>
                <input type="text" class="form-control !py-[14px] !pl-10" placeholder="Search" autofocus>
            </div>
            </form>
        </div>
        </div>
    </div>
    <!-- END: Search Modal -->
