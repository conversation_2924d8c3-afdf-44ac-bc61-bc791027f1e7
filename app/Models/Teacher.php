<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Teacher extends Model
{
    use HasFactory;

    protected $table = 'teachers';

    protected $fillable = [
        'zoomuser_id',
        'user_id',
        'gender',
        'rateperclass',
        'qualifications',
        'transfersalary',
        'transferdetails',
        'upfiles',
        'notes',
    ];

    protected $cast = [
        'qualifications' => 'array',
        'upfiles' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function clss()
    {
        return $this->belongsTo(Clss::class, 'teacher_id', 'id');
    }

    public function qualifications()
    {
        return $this->hasMany(TeacherQualifications::class, 'qualifications', 'id');
    }
    public function upfiles()
    {
        return $this->hasMany(TeacherFile::class, 'upfiles', 'id');
    }

}
