<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl" class="dark">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}" dir="rtl" class="dark">
        <meta name="description" content="">
        <link rel="shortcut icon" href={{ asset("assets/images/favicon.ico") }} type="image/x-icon">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- BEGIN: Theme CSS-->
        <link rel="stylesheet" href={{ asset("assets/css/rt-plugins.css") }}>
        <link rel="stylesheet" href={{ asset("assets/css/app.css") }}>
        <!-- End : Theme CSS -->

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap');

            *
            {
                font-family: "Cairo", sans-serif;
            }

        </style>
    <!-- jquery scripts -->
    <script src={{ asset("assets/js/jquery-3.6.0.min.js") }}></script>

    <!-- jspdf cdn for printing certificates -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.4.1/jspdf.debug.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/2.3.4/jspdf.plugin.autotable.min.js"></script>

    {{-- livewire styles --}}
        @livewireStyles

        <!-- Vite Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])



    </head>
    <body class="font-sans antialiased dashcode-app" id="body_class">
  <!-- [if IE]> <p class="browserupgrade"> You are using an <strong>outdated</strong> browser. Please <a href="https://browsehappy.com/">upgrade your browser</a> to improve your experience and security. </p> <![endif] -->
  <main class="app-wrapper">
    <!-- BEGIN: Sidebar -->
    @include('layouts.partials.sidebar')
    <!-- End: Sidebar -->

    <!-- BEGIN: Settings --}} -->

    <!-- {{--  End: Settings  --}} -->

    <div class="flex flex-col justify-between min-h-screen">
      <div>
        <!-- BEGIN: Header -->
        @include('layouts.partials.header')
        <!-- END: Header -->

        <div class="content-wrapper transition-all duration-150 xl:ltr:ml-[248px] xl:rtl:mr-[248px]">
            <div class="page-content">
                <div>

                  <!-- BEGIN: Breadcrumb -->
                  @include('layouts.partials.breadcrumb')
                  <!-- END: BreadCrumb -->
                  <div class=" space-y-5">

                    <!-- BEGIN: Content -->

                    @yield('content')

                    <!-- END: Content -->
                  </div>
                </div>
            </div>
        </div>
      </div>

      <!-- BEGIN: Footer For Desktop and tab -->
      <footer id="footer">
        <div class="site-footer px-6 bg-white dark:bg-slate-800 text-slate-500 dark:text-slate-300 py-4 ltr:ml-[248px] rtl:mr-[248px]">
            <div class="text-left">
                جميع الحقوق محفوظة,
              <span id="thisYear"></span>
               © Shafe3-LMS
            </div>
        </div>
      </footer>
      <!-- END: Footer For Desktop and tab -->

      <!-- BEGIN: notifications and messages For mobiles -->
      @include('layouts.partials.mobile-notimess')
      <!-- END: notifications and messages For mobiles -->

    </div>
  </main>

        <!-- scripts -->
        <script src={{ asset("assets/js/rt-plugins.js") }}></script>
        <script src={{ asset("assets/js/app.js") }}></script>
        {{-- datepicker --}}
        <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/datepicker.min.js"></script>

        <!-- Start : theme-store js -->
        <script src={{ asset("assets/js/store.js") }} sync></script>
        <!-- End : theme-store js -->

        {{-- livewire scripts --}}
        @livewireScripts
        @if(auth()->user()->role !== 'superadmin')
        <script>
            document.addEventListener('contextmenu', event => event.preventDefault());
            document.addEventListener('keydown', event => {
                if (event.ctrlKey && event.shiftKey && event.key === 'I') {
                    event.preventDefault();
                }
                if (event.key === 'F12') {
                    event.preventDefault();
                }
            });
        </script>
        @endif
        @stack('scripts')
    </body>
</html>
