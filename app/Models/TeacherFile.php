<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeacherFile extends Model
{
    use HasFactory;

    protected $table = 'teacher_files';

    protected $fillable = [
        'user_id',
        'name',
        'url',
    ];

    public function teacher()
    {
        return $this->belongsTo(Teacher::class, 'upfiles', 'id');
    }
}
