<div>
    {{-- add modal --}}
    <x-g-modal name="add">
        <div class="bg-white dark:bg-blue-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">إضافة طالب جديد</h1>
                <button type="button" wire:click.prevent="closemodal('add')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="add-student"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم:</label>
                    <input type="text" wire:model.live="name" id="name" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="الاسم" required>
                    @error('name')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البلد:</label>
                    <select wire:model.live="country" id="role" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  selected>اختر البلد</option>
                        @foreach ( $listcountries as $key => $listcountry)
                            <option style="direction:rtl;" value="{{ $listcountry }}">{{ $listcountry }}</option>
                        @endforeach
                    </select>
                    @error('country')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم المستخدم:</label>
                    <input type="text" wire:model.live="username" id="username" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اسم المستخدم" required>
                    @error('username')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الالكتروني</label>
                    <input type="email" wire:model.live="email" id="email" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="البريد الالكتروني"  required>
                    @error('email')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كلمة السر</label>
                    <input type="{{ $showpass ? 'text' : 'password' }}" wire:model.live="password" id="password" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="ادخل كلمة السر" required><br/>
                    <label for="showpass" class="flex place-items-center mt-2">
                        <input wire:model.live="showpass" id="showpass" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="showpass">
                        <span class="mx-2 text-sm text-white">{{ __('اظهار الباسورد') }}</span>
                    </label>
                    @error('password')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم التليفون:</label>
                    <input type="text" wire:model.live="phone" id="phone" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="رقم التليفون" required>
                    @error('phone')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="dateofbirth" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الميلاد:</label>
                    <input type="date" wire:model.live="dateofbirth" id="dateofbirth" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="تاريخ الميلاد" required>
                    @error('dateofbirth')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="gender_prefer" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">معلم / معلمة:</label>
                    <select wire:model.live="gender_prefer" id="gender_prefer" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  selected>اختر ممايلي</option>
                        <option style="direction:rtl;" value="">أيهما متاح</option>
                        <option style="direction:rtl;" value="male">معلم</option>
                        <option style="direction:rtl;" value="female">معلمة</option>
                    </select>
                    @error('gender_prefer')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="subscription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة الاشتراك:</label>
                    <select wire:model.live="subscription" id="subscription" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  selected>اختر ممايلي</option>
                        <option style="direction:rtl;" value="trial">تجريبي</option>
                        <option style="direction:rtl;" value="fixed">مشترك</option>
                        @if (auth()->user()->role === 'admin' || auth()->user()->role === 'moderator')
                            <option style="direction:rtl;" value="suspended">منتهي</option>
                        @endif
                    </select>
                    @error('subscription')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                @if ($subscription == 'fixed')
                    <div class="mb-4">
                        <label for="subscription_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الباقة / الاشتراك:</label>
                        <select wire:model.live="subscription_id" id="role" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                            <option style="direction:rtl;" value="" selected>اختر الباقة / الاشتراك</option>
                            @foreach ( $listsubscriptions as $subscription)
                                <option style="direction:rtl;" value="{{ $subscription->id }}">{{ $subscription->name }}</option>
                            @endforeach
                        </select>
                        @error('subscription_id')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                    <label for="numofclasses" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عدد الحصص:</label>
                    <input type="text" wire:model.live="numofclasses" id="numofclasses" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="عدد الحصص" required>
                    @error('numofclasses')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                    </div>
                    <div class="mb-4">
                        <label for="paid" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المبلغ المدفوع:</label>
                        <div class="flex justify-start place-items-center">
                            <input type="text" wire:model.live="paid" id="paid" class="shadow-sm rounded-md w-3/4 px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="المبلغ المدفوع" required>
                            <span class="mr-3 text-white font-bold text-lg "> {{ $currency }} </span>
                        </div>
                        @error('paid')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label for="startdate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ البدء:</label>
                        <input type="date" wire:model.live="startdate" id="startdate" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="تاريخ البدء" required>
                        @error('startdate')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label for="ref_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كود الخصم:</label>
                        <input type="text" wire:model.live="ref_code" id="ref_code" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="كود الخصم" required>
                        @error('ref_code')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label for="balance" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الرصيد:</label>
                        <input type="text" {{ auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  ? '' : 'disabled' }} wire:model.live="balance" id="balance" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="الرصيد" required>
                        @error('balance')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                @elseif ($subscription == 'trial')
                    <div class="mb-4">
                        <label for="numofclasses" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عدد الحصص:</label>
                        <input type="text" wire:model.live="numofclasses" id="numofclasses" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="عدد الحصص" required>
                        @error('numofclasses')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label for="startdate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ البدء:</label>
                        <input type="date" wire:model.live="startdate" id="startdate" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="تاريخ البدء" required>
                        @error('startdate')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                @endif
                @if ($showbanks)
                <div class="mb-4">
                    <label for="bank_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة التحويل:</label>
                    <select wire:model.live="bank_id" id="bank_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option style="direction:rtl;"  selected>اختر طريقة التحويل</option>
                        @foreach ($banks as $bank)
                        <option style="direction:rtl;" value="{{ $bank->id }}">{{ $bank->name }}</option>
                        @endforeach
                    </select>
                    @error('bank_id')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="trans_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كود التحويل:</label>
                    <input type="text" wire:model.live="trans_code" id="trans_code" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="كود التحويل" required>
                    @error('trans_code')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                @else

                @endif
                <div class="mb-4">
                    <div class="flex items-center mb-4">
                    <label class="relative flex items-center  cursor-pointer">
                    <input type="checkbox" wire:model.live="status" class="sr-only peer">
                    <div class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0  rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600 hover:peer-checked:bg-indigo-700 "></div>
                    </label>
                    <span class="mr-3 text-white font-bold text-lg ">{{ $status ? 'مفعل' : 'غير مفعل' }}</span>
                    </div>
                    @error('status')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="save" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="save"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('add')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- edit modal --}}
    <x-g-modal name="edit">
        <div class="bg-white dark:bg-yellow-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">تعدبل بيانات الطالب  "{{ $name }}" </h1>
                <button type="button" wire:click.prevent="closemodal('edit')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="edit-student"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم:</label>
                    <input type="text" wire:model.live="name" id="name" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" value="{{ $name }}" placeholder="الاسم" required>
                    @error('name')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البلد:</label>
                    <select wire:model.live="country" id="role" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;" value="{{ $country }}" selected>{{ $country }}</option>
                        @foreach ( $listcountries as $key => $listcountry)
                            <option style="direction:rtl;" value="{{ $listcountry }}">{{ $listcountry }}</option>
                        @endforeach
                    </select>
                    @error('country')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم المستخدم:</label>
                    <input type="text" wire:model.live="username" id="username" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" value="{{ $username }}" placeholder="اسم المستخدم" required>
                    @error('username')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الالكتروني</label>
                    <input type="email" wire:model.live="email" id="email" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" value="{{ $email }}" placeholder="البريد الالكتروني"  required>
                    @error('email')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كلمة السر</label>
                    <input type="{{ $showpass ? 'text' : 'password' }}" wire:model.live="password" id="password" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                    <span class="text-sm font-bold text-black-500">* ادخل كلمة سر جديدة اذا كنت تريد تغييرها </span><br/>
                    <label for="showpass" class="flex place-items-center mt-2">
                        <input wire:model.live="showpass" id="showpass" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="showpass">
                        <span class="mx-2 text-sm text-white">{{ __('اظهار الباسورد') }}</span>
                    </label>
                    @error('password')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم التليفون:</label>
                    <input type="text" wire:model.live="phone" value="{{ $phone }}" id="phone" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="رقم التليفون" required>
                    @error('phone')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="dateofbirth" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الميلاد:</label>
                    <input type="date" wire:model.live="dateofbirth" id="dateofbirth" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="تاريخ الميلاد" required>
                    @error('dateofbirth')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="gender_prefer" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">معلم / معلمة:</label>
                    <select wire:model.live="gender_prefer" id="gender_prefer" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;" value="" {{ $gender_prefer == '' ? ' selected' : ''}}>أيهما</option>
                        <option style="direction:rtl;" value="male" {{ $gender_prefer == 'male' ? ' selected' : ''}}>معلم</option>
                        <option style="direction:rtl;" value="female" {{ $gender_prefer == 'female' ? ' selected' : ''}}>معلمة</option>
                    </select>
                    @error('gender_prefer')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="subscription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة الاشتراك:</label>
                    <select wire:model.live="subscription" id="subscription" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;">اختر ممايلي</option>
                        <option style="direction:rtl;" value="trial">تجريبي</option>
                        <option style="direction:rtl;" value="fixed">مشترك</option>
                        <option style="direction:rtl;" value="suspended">منتهي</option>
                    </select>
                    @error('subscription')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                @if ($showbanks)
                    <div class="mb-4">
                        <label for="subscription_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الباقة / الاشتراك:</label>
                        <select wire:model.live="subscription_id" id="role" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                            <option style="direction:rtl;" value="" >اختر الباقة / الاشتراك</option>
                            @foreach ( $listsubscriptions as $subscription)
                                <option style="direction:rtl;" value={{ $subscription->id }}>{{ $subscription->name }}</option>
                            @endforeach
                        </select>
                        @error('subscription_id')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                    <label for="numofclasses" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عدد الحصص:</label>
                    <input type="text" wire:model.live="numofclasses" id="numofclasses" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="عدد الحصص" required>
                    @error('numofclasses')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                    </div>
                    <div class="mb-4">
                        <label for="paid" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المبلغ المدفوع:</label>
                        <input type="text" wire:model.live="paid" id="paid" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="المبلغ المدفوع" required>
                        @error('paid')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label for="startdate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ البدء:</label>
                        <input type="date" wire:model.live="startdate" id="startdate" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="تاريخ البدء" required>
                        @error('startdate')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label for="ref_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كود الخصم:</label>
                        <input type="text" wire:model.live="ref_code" id="ref_code" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="كود الخصم" required>
                        @error('ref_code')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label for="balance" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الرصيد:</label>
                        <input type="text" {{ auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  ? '' : 'disabled' }} wire:model.live="balance" id="balance" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="الرصيد" required>
                        @error('balance')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                @elseif ($subscription == 'trial')
                    <div class="mb-4">
                        <label for="numofclasses" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عدد الحصص:</label>
                        <input type="text" wire:model.live="numofclasses" id="numofclasses" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="عدد الحصص" required>
                        @error('numofclasses')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label for="startdate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ البدء:</label>
                        <input type="date" wire:model.live="startdate" id="startdate" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="تاريخ البدء" required>
                        @error('startdate')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                @endif
                <div class="mb-4">
                    <label for="reg_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تجديد / عضو جديد:</label>
                    <select wire:model.live="reg_status" id="reg_status" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;">اختر ممايلي</option>
                        <option style="direction:rtl;" value="new">عضو جديد</option>
                        <option style="direction:rtl;" value="resub">تجديد</option>
                        <option style="direction:rtl;" value="ended">منتهي</option>
                    </select>
                    @error('reg_status')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                @if ($showbanks)
                <div class="mb-4">
                    <label for="bank_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة التحويل:</label>
                    <select wire:model.live="bank_id" id="role" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option style="direction:rtl;"  selected>اختر طريقة التحويل</option>
                        @foreach ($banks as $bank)
                        <option style="direction:rtl;" value="{{ $bank->id }}">{{ $bank->name }}</option>
                        @endforeach
                    </select>
                    @error('bank_id')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="trans_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كود التحويل:</label>
                    <input type="text" wire:model.live="trans_code" id="trans_code" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="كود التحويل" required>
                    @error('trans_code')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                @else

                @endif
                <div class="mb-4">
                    <div class="flex items-center mb-4">
                    <label class="relative flex items-center  cursor-pointer">
                    <input type="checkbox" wire:model.live="status" class="sr-only peer">
                    <div class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0  rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600 hover:peer-checked:bg-indigo-700 "></div>
                    </label>
                    <span class="mr-3 text-white font-bold text-lg ">{{ $status ? 'مفعل' : 'غير مفعل' }}</span>
                    </div>
                    @error('status')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                {{ $startdate }}
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="update" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="update"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('edit')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- view modal --}}
    <x-g-modal name="view">
        <div class="bg-white dark:bg-gray-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">{{ $name }}</h1>
                <button type="button" wire:click.prevent="closemodal('view')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <div class="">
                <div class="mb-4 flex gap-1">
                    <span class="block text-lg font-bold text-gray-300 px-2">الاسم:</span>
                    <span class="block text-lg font-bold text-gray-300">{{ $name }}</span>
                </div>
                <div class="mb-4 flex gap-1">
                    <span class="block text-lg font-bold text-gray-300 px-2">البريد الالكتروني:</span>
                    <span class="block text-lg font-bold text-gray-300">{{ $email }}</span>
                </div>
                <div class="mb-4 flex gap-1">
                    <span class="block text-lg font-bold text-gray-300 px-2">اسم المستخدم:</span>
                    <span class="block text-lg font-bold text-gray-300">{{ $username }}</span>
                </div>
                <div class="mb-4 flex gap-1">
                    <span class="block text-lg font-bold text-gray-300 px-2">تليفون:</span>
                    <span class="block text-lg font-bold text-gray-300">{{ $phone }}</span>
                </div>
                <div class="mb-4 flex gap-1">
                    <span class="block text-lg font-bold text-gray-300 px-2">البلد:</span>
                    <span class="block text-lg font-bold text-gray-300">{{ $country }}</span>
                </div>
                <div class="mb-4 flex gap-1">
                    <span class="block text-lg font-bold text-gray-300 px-2">اسم البنك المحوَّل عليه:</span>
                    <span class="block text-lg font-bold text-gray-300">{{ $bankname }}</span>
                </div>
                <div class="mb-4 flex gap-1">
                    <span class="block text-lg font-bold text-gray-300 px-2">الباقة المشترك عليها:</span>
                    <span class="block text-lg font-bold text-gray-300">{{ $subscription_name }}</span>
                </div>

            </div>
            <div class="flex justify-between gap-4">
                <div class="">
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">عمر الطالب:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $dateofbirth }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">معلم / معلمة:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $gender_prefer }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">المبلغ المدفوع:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $paid }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">كود الخصم:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ '["' . $ref_code . '"]' ?? 'لا يوجد كود مُفعّل' }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">كود الدعوة:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ '["' . $inv_code . '"]' ?? 'لا يوجد كود مُفعّل' }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">الحالة:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $status == true ? 'مفعل' : 'غير مفعل' }}</span>
                    </div>
                </div>
                <div class="">
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">عدد الحصص:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $numofclasses }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">تاريخ البدء:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $startdate }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">الرصيد:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $balance }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">تجديد / عضو جديد:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $this->getregstatustitle($reg_status) }}</span>
                    </div>
                    <div class="mb-4 flex gap-1">
                        <span class="block text-lg font-bold text-gray-300 px-2">الإشتراك:</span>
                        <span class="block text-lg font-bold text-gray-300">{{ $this->getsubscriptiontitle($subscription) }}</span>
                    </div>
                </div>
            </div>
            <div class="mt-4">
                <h1 class="text-2xl font-bold text-right mb-4 dark:text-gray-200">الشهادات:</h1>
                <div class="overflow-x-auto -mx-6 dashcode-data-table px-8">
                    <div class="inline-block min-w-full align-middle">
                    <div class="overflow-hidden">
                        @unless (count($listcertifications) == 0)
                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl">
                        <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                            <tr>
                            <th scope="col" class=" text-right text-xl font-medium text-[#cbd5e1] pr-2 py-2">الطالب</th>
                            <th scope="col" class=" text-right text-xl font-medium text-[#cbd5e1] pr-2 py-2">المعلم</th>
                            <th scope="col" class=" text-right text-xl font-medium text-[#cbd5e1] pr-2 py-2">اسم الشهادة</th>
                            <th scope="col" class=" text-right text-xl font-medium text-[#cbd5e1] pr-2 py-2">الدرجة</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                            @foreach ($listcertifications as $certificate)
                            <tr>
                                <td class="table-td text-xl font-normal">
                                    <span class=" text-slate-600 dark:text-white capitalize">{{ $certificate->student->name }}</span>
                                </td>
                                <td class="table-td text-xl font-normal">
                                    <span class=" text-slate-600 dark:text-white capitalize">{{ $certificate->teacher->name }}</span>
                                </td>
                                <td class="table-td text-xl font-normal">
                                    <span class=" text-slate-600 dark:text-white capitalize">{{ $certificate->title }}</span>
                                </td>
                                <td class="table-td text-xl font-normal">
                                    <span class=" text-slate-600 dark:text-white capitalize">{{ $certificate->grade }}</span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                        </table>
                        @else
                        <div class="text-center text-xl font-bold text-white">لا يوجد شهادات للطالب حتى الآن.</div>
                        @endunless
                    </div>
                    </div>
                </div>
            </div>
            <div class="mt-4">
                <h1 class="text-2xl font-bold text-right mb-4 dark:text-gray-200">إنجازات الطالب:</h1>
                <div class="overflow-x-auto -mx-6 dashcode-data-table px-8">
                    <div class="inline-block min-w-full align-middle">
                    <div class="overflow-hidden">
                        @unless (count($listachievements) == 0)
                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl">
                        <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                            <tr>
                            <th scope="col" class=" text-right text-xl font-medium text-[#cbd5e1] pr-2 py-2">الطالب</th>
                            <th scope="col" class=" text-right text-xl font-medium text-[#cbd5e1] pr-2 py-2">المعلم</th>
                            <th scope="col" class=" text-right text-xl font-medium text-[#cbd5e1] pr-2 py-2">العنوان</th>
                            <th scope="col" class=" text-right text-xl font-medium text-[#cbd5e1] pr-2 py-2">الوصف</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                            @foreach ($listachievements as $achievement)
                            <tr>
                                <td class="table-td text-xl font-normal">
                                    <span class=" text-slate-600 dark:text-white capitalize">{{ $achievement->student->name }}</span>
                                </td>
                                <td class="table-td text-xl font-normal">
                                    <span class=" text-slate-600 dark:text-white capitalize">{{ $achievement->teacher->name }}</span>
                                </td>
                                <td class="table-td text-xl font-normal">
                                    <span class=" text-slate-600 dark:text-white capitalize">{{ $achievement->name }}</span>
                                </td>
                                <td class="table-td text-xl font-normal">
                                    <span class=" text-slate-600 dark:text-white capitalize">{{ $achievement->description }}</span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                        </table>
                        @else
                        <div class="text-center text-xl font-bold text-white">لا يوجد إنجازات للطالب حتى الآن.</div>
                        @endunless
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </x-g-modal>
    {{-- delete modal --}}
    <x-g-modal name="delete">
        <div class="bg-white dark:bg-red-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">حذف الطالب  "{{ $name }}"</h1>
                <button type="button" wire:click.prevent="closemodal('delete')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="delete-student">
                <div class="mb-4">
                    <div class="block text-right text-lg font-bold text-white">هل انت متأكد من حذف الطالب  "{{ $name }}"؟!</div>
                </div>
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="delete" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>

                    <x-loading method="delete"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('delete')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- delete selected modal --}}
    <x-g-modal name="deleteselected">
        <div class="bg-white dark:bg-gray-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">حذف المستخدمين</h1>
                <button type="button" wire:click.prevent="closemodal('deleteselected')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="delete-selectedusers">
                @if (empty($selectedstudents))
                    <div class="mb-4">
                        <div class="block text-right text-lg font-bold text-white">لابد من اختيار عناصر حتى يتم حذفهم!</div>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" disabled wire:click.prevent="deleteselected" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 h focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>

                        <x-loading method="deleteselected"></x-loading>

                        <button type="button" wire:click.prevent="closemodal('deleteselected')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                    </div>
                @else
                    <div class="mb-4">
                        <div class="block text-right text-lg font-bold text-white">هل انت متأكد من حذف المستخدمين التاليين؟!</div>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" wire:loading.remove wire:click.prevent="deleteselected" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>

                        <x-loading method="deleteselected"></x-loading>

                        <button type="button" wire:click.prevent="closemodal('deleteselected')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                    </div>
                @endif
            </form>
        </div>
    </x-g-modal>


    <!-- flash save successful message -->
    <x-flash-message on="flashsaved">
        <div class="flex items-center bg-green-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تسجيل العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    <!-- flash update successful message -->
    <x-flash-message on="flashupdated">
        <div class="flex items-center bg-yellow-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تعديل العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    <!-- flash delete successful message -->
    <x-flash-message on="flashdeleted">
        <div class="flex items-center bg-red-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم حذف العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    {{-- data tables --}}
    <div class="card">
        <header class=" block card-header noborder">
          <h4 class="text-primary-500 font-bold text-3xl">إدارة الطلاب</h4>
        </header>
        <div x-data="{open: false }" class="card-body px-6 pb-6">
          <div class="sm:grid sm:my-2 md:flex sm:place-content-center md:justify-between w-auto place-items-center">
            <div class="grid sm:grid-cols-1 lg:grid-cols-6 gap-3">
                {{-- add modal button --}}
                @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  || auth()->user()->role == 'sales')
                <button type="button" x-data="" x-on:click.prevent="$dispatch('open-modal', 'add')" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div class="flex place-items-center">
                    <iconify-icon icon="heroicons:plus-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    اضافة عضو جديد
                    </div>
                </button>
                @endif
                {{-- delete selected modal button --}}
                @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                <button type="button"  wire:click.prevent="showdeleteselected"  class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div class="flex place-items-center">
                    <iconify-icon icon="heroicons:trash-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="حذف"></iconify-icon>
                    حذف المحددين
                    </div>
                </button>
                @endif
                {{-- advanced section modal button --}}
                <button type="button" x-on:click="open = ! open" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div x-show="! open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اظهار البحث الــمتقدم
                    </div>
                    <div x-show="open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اخفاء البحث الــمتقدم
                    </div>
                </button>
                @if (!empty($filtercountry) || !empty($filtersubscription) || !empty($filteractive) || !empty($filterinvcode))
                    <button wire:click.prevent="resetallfilter" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div class="flex place-items-center">
                            <iconify-icon icon="heroicons:arrow-uturn-down-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                            إعادة تعيين التصفية
                        </div>
                    </button>
                @endif
            </div>
            <div class="my-3 sm:w-auto sm:mt-0 sm:ml-auto md:ml-0">
                <div class="relative flex w-80 text-gray-700 mr-auto my-auto place-items-center">
                    @if (!empty($search))
                    <button type="button" wire:click.prevent="resetsearch" class="absolute inset-y-0 left-0 pl-3 flex items-center z-1">
                        <iconify-icon icon="heroicons:x-mark-20-solid" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                    </button>
                    @endif
                    <iconify-icon icon="heroicons:magnifying-glass-solid" style="font-size: 24px;color:#016241;" alt="تعديل" class="ml-2"></iconify-icon>
                    <input wire:model.live.debounce.500="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-lg rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="بحث..." wire:ignore>
                </div>
            </div>
          </div>
          <div x-show="open" class="grid sm:grid-cols-1 lg:grid-cols-5 gap-2 w-full my-2">
            <div class="flex place-items-center">
            <label for="filtercountry" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">البلد: </label>
            <select name="filtercountry" wire:model.live="filtercountry" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;" value="" selected>اختر البلد</option>
                    @foreach ( $listcountries as $key => $listcountry)
                        <option style="direction:rtl;" value="{{ $listcountry }}">{{ $listcountry }}</option>
                    @endforeach
            </select>
            @if (!empty($filtercountry))
            <button type="button" wire:click.prevent="resetfilter('filtercountry')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
            <div class="flex place-items-center">
            <label for="filterinvcode" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 mx-2">كود الدعوة: </label>
            <select name="filterinvcode" wire:model.live="filterinvcode" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;" value="" selected>كود الدعوة</option>
                    @foreach ( $listallusers as $key => $username)
                        <option style="direction:rtl;" value="INVNEW{{ $key < 10 ? '0'.$key : $key }}">{{ $username }}</option>
                    @endforeach
            </select>
            @if (!empty($filterinvcode))
            <button type="button" wire:click.prevent="resetfilter('filterinvcode')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
            <div class="flex place-items-center"></div>
            <div class="flex place-items-center">
            <label for="filtersubscription" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 mx-2">نوع الاشتراك:</label>
            <select name="filtersubscription" wire:model.live="filtersubscription" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;"  value="" selected>اختر ممايلي</option>
                <option style="direction:rtl;" value="trial">تجريبي</option>
                <option style="direction:rtl;" value="fixed">مشترك</option>
                <option style="direction:rtl;" value="suspended">منتهي</option>
            </select>
            @if (!empty($filtersubscription))
            <button type="button" wire:click.prevent="resetfilter('filtersubscription')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
            <div class="flex place-items-center">
            <label for="filteractive" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 mx-2">الحالة:</label>
            <select name="filteractive" wire:model.live="filteractive" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:ltr;" value="" selected>اختر الحالة</option>
                <option style="direction:ltr;" value="no">متاح</option>
                <option style="direction:ltr;" value="yes">غير متاح</option>
            </select>
            @if ($filteractive >= 0)
            <button type="button" wire:click.prevent="resetfilter('filteractive')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
          </div>
          <div class="overflow-x-auto -mx-6 dashcode-data-table">
            <div class="inline-block min-w-full align-middle">
              <div class="overflow-hidden">
                @unless ($students->count() == 0)
                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl">
                  <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                    <tr>
                      <th scope="col" class=" table-th text-xl font-medium"><input type="checkbox" wire:model.live="selectall" class="border-gray-300 rounded bg-gray-50 borded ml-1"> الكل </th>
                      <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('name')">الإسم</a></th>
                      <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('username')">اسم المستخدم</a></th>
                      <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('email')">البريد الالكتروني</a></th>
                      <th scope="col" class=" table-th text-xl font-medium">رقم التليفون</th>
                      <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('country')">البلد</a></th>
                      <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">جديد / تجديد</th>
                      <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">الاشتراك</th>
                      <th scope="col" class=" text-center text-xl font-medium text-[#cbd5e1]">الحالة</th>
                      <th scope="col" class=" table-th text-xl font-medium ">أجراءات</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                    @foreach ($students as $student)
                    <tr wire:key="student-{{ $student->id }}" class=" {{ $this->checkifapplied($student) ? '' : 'bg-green-900' }} {{ $student->student->numofclasses == 1 ? 'bg-red-900' : '' }} {{ $student->student->numofclasses <= 3 ? 'bg-yellow-900' : '' }} {{ $student->is_active === true ? 'bg-orange-900' : '' }}">
                      <td class="table-td text-xl font-normal"><input type="checkbox" wire:model.live="selectedstudents" value="{{ $student->id }}" class="border-gray-300 rounded bg-gray-50 borded"></td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $student->name }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $student->username }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $student->email }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $student->phone }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $student->country }}</span>
                      </td>
                      <td class="table-td text-center text-xl font-normal">
                        @if ($student->student->reg_status === 'new')
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border-[#016241] border text-yellow-500 bg-warning-500">
                                عضو جديد
                        @elseif ($student->student->reg_status === 'resub')
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border-[#016241] border text-green-500 bg-success-500">
                                تجديد
                        @elseif ($student->student->reg_status === 'ended')
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border-[#016241] border text-red-500 bg-danger-500">
                                منتهي
                        @endif
                        </div>
                      </td>
                      <td class="table-td text-center text-xl font-normal">
                        @if ($student->student->subscription === 'trial')
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border-[#016241] border text-yellow-500 bg-warning-500">
                                تجريبي
                        @elseif ($student->student->subscription === 'fixed')
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border-[#016241] border text-green-500 bg-success-500">
                                مشترك
                        @elseif ($student->student->subscription === 'suspended')
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border-[#016241] border text-red-500 bg-danger-500">
                                منتهي
                        @endif
                        </div>
                      </td>
                      <td class="table-td text-center text-xl font-normal">
                        @if ($student->is_active === true)
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border-[#016241] border text-red-500 bg-danger-500">
                                غير متاح
                        @else
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border-[#016241] border text-green-500 bg-success-500">
                                متاح
                        @endif
                        </div>
                      </td>
                      <td class="table-td  text-xl font-normal">
                        <div class="flex space-x-3 rtl:space-x-reverse">
                            <button type="button" wire:click.prevent="showview({{ $student }})">
                              <iconify-icon icon="heroicons:eye-solid" style="font-size: 24px;color:#016241;font-weight:bold;" alt="مشاهدة"></iconify-icon>
                            </button>
                            @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                            <button type="button" wire:click.prevent="showedit({{ $student }})">
                              <iconify-icon icon="heroicons:pencil-square" style="font-size: 24px;color:#016241;font-weight:bold;" alt="تعديل"></iconify-icon>
                            </button>
                            <button type="button" wire:click.prevent="showdelete({{ $student }})">
                                <iconify-icon icon="heroicons:trash" style="font-size: 24px;color:#016241;font-weight:bold;" alt="حذف"></iconify-icon>
                            </button>
                            @endif
                        </div>
                      </td>
                    </tr>
                    @endforeach
                  </tbody>
                </table>
                @else
                <div class="text-center text-xl font-bold text-[#016241]">لا يوجد تلاميذ حتى الآن.</div>
                @endunless
              </div>
            </div>
          </div>
        </div>
    </div>
    <div class="flex my-4">
        <div class="flex justify-start w-[50%] items-center">
            <label for="perpage" class="text-lg font-medium text-gray-700 dark:text-[#be9539]">عدد النتائج في كل صفحة:</label>
            <select wire:model.live="perpage" id="perpage" style="direction:ltr;" class="w-20 shadow-sm rounded-md mx-4 px-5 py-1 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                @foreach ($perpageoptions as $option)
                    <option style="direction:rtl;" value="{{ $option }}">{{ $option === 'all' ? 'الكل' : $option }}</option>
                @endforeach
            </select>
        </div>
        @if ($perpage === 'all')

        @else
        <div class="w-[100%] mr-4">{{ $students->links('vendor.livewire.tailwind') }}</div>
        @endif
    </div>
</div>
