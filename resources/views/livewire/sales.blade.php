<div>
    {{-- add modal --}}
    <x-g-modal name="add">
        <div class="bg-white dark:bg-blue-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">إضافة سند قبض جديد</h1>
                <button type="button" wire:click.prevent="closemodal('add')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="add-expenses"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="mb-4">
                    <label for="saletype" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع سند الايراد:</label>
                    <select wire:model.live="saletype" name="saletype" id="saletype" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="تجديد للطالب">تجديد للطالب</option>
                        <option value="ايرادات اخرى">ايرادات اخرى</option>
                    </select>
                    @error('saletype')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                @if ( $saletype == 'تجديد للطالب' )
                    <div class="mb-4">
                        <label for="student_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"> الطالب:</label>
                        <select wire:model.live="student_id" name="student_id" id="student_id" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">اختر الطالب</option>
                            @foreach ($allstudents as $allstudent)
                                <option value="{{ $allstudent->id }}">{{ $allstudent->name }}</option>
                            @endforeach
                        </select>
                        @error('student_id')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label for="sales_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"> البائع:</label>
                        <select wire:model.live="sales_id" name="sales_id" id="sales_id" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">اختر البائع</option>
                            @foreach ($sales as $sale)
                                <option value="{{ $sale->id }}">{{ $sale->name }}</option>
                            @endforeach
                        </select>
                        @error('sales_id')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                @else
                    <div class="mb-4">
                        <label for="saletitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم السند: </label>
                        <input type="text" wire:model.live="saletitle" id="saletitle" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسم السند" required>
                        @error('saletitle')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                @endif
                <div class="mb-4">
                    <div
                        x-data
                        x-init="flatpickr($refs.datetimewidget, {wrap: true, enableTime: false, dateFormat: 'Y-m-d'});"
                        x-ref="datetimewidget"
                        class="flatpick mx-auto mt-5"
                    >
                        <label for="startdate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ السند:</label>
                        <div class="flex align-middle align-content-center">
                            <input
                                x-ref="startdate"
                                type="text"
                                id="startdate"
                                wire:model.live="startdate"
                                data-input
                                placeholder="اختر التاريخ"
                                class="block w-full p-2 border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-r-md shadow-sm"
                            >
                            <a
                                class="h-12 w-10 input-button cursor-pointer rounded-l-md bg-slate-300 hover:bg-slate-600 text-slate-600 hover:text-slate-100 border-gray-300 border-t border-b border-l"
                                title="clear" data-clear
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mt-2 ml-1" viewBox="0 0 20 20" fill="#4b4b4b">
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                    @error('startdate')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="paid" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المبلغ: </label>
                    <input type="text" wire:model.live="paid" id="paid" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب المبلغ" required>
                    @error('paid')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                @if ( $saletype == 'تجديد للطالب' )
                    <div class="mb-4">
                        <label for="numofclasses" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عدد الحصص: </label>
                        <input type="text" wire:model.live="numofclasses" id="numofclasses" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="عدد الحصص" required>
                        @error('numofclasses')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                @else

                @endif
                <div class="mb-4">
                    <label for="bank_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة التحويل:</label>
                    <select wire:model.live="bank_id" id="role" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option style="direction:rtl;"  selected>اختر طريقة التحويل</option>
                        @foreach ($banks as $bank)
                        <option style="direction:rtl;" value="{{ $bank->id }}">{{ $bank->country }} : {{ $bank->name }} [ {{ $bank->balance }} ]</option>
                        @endforeach
                        <option style="direction:rtl;" value="0">مصر : الخزينة [ {{ $safebalance }} ]</option>
                    </select>
                    @error('bank_id')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                @if ( $saletype == 'تجديد للطالب' )
                    <div class="mb-4">
                        <label for="trans_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كود التحويل: </label>
                        <input type="text" wire:model.live="trans_code" id="trans_code" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="كود التحويل" required>
                        @error('trans_code')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                @else

                @endif
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="save" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="save"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('add')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- edit modal --}}
    <x-g-modal name="edit">
        <div class="bg-white dark:bg-yellow-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">تعدبل البند الرئيسي </h1>
                <button type="button" wire:click.prevent="closemodal('edit')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="edit-expenses"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
            <div class="mb-4">
                <label for="saletype" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع سند الايراد:</label>
                <select wire:model.live="saletype" name="saletype" id="saletype" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="تجديد للطالب">تجديد للطالب</option>
                    <option value="ايرادات اخرى">ايرادات اخرى</option>
                </select>
                @error('saletype')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            @if ( $saletype == 'تجديد للطالب' )
                <div class="mb-4">
                    <label for="student_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"> الطالب:</label>
                    <select wire:model.live="student_id" name="student_id" id="student_id" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">اختر الطالب</option>
                        @foreach ($allstudents as $allstudent)
                            <option value="{{ $allstudent->id }}">{{ $allstudent->name }}</option>
                        @endforeach
                    </select>
                    @error('student_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="sales_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"> البائع:</label>
                    <select wire:model.live="sales_id" name="sales_id" id="sales_id" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">اختر البائع</option>
                        @foreach ($sales as $sale)
                            <option value="{{ $sale->id }}">{{ $sale->name }}</option>
                        @endforeach
                    </select>
                    @error('sales_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
            @else
                <div class="mb-4">
                    <label for="saletitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم السند: </label>
                    <input type="text" wire:model.live="saletitle" id="saletitle" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسم السند" required>
                    @error('saletitle')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
            @endif
            <div class="mb-4">
                <div
                    x-data
                    x-init="flatpickr($refs.datetimewidget, {wrap: true, enableTime: false, dateFormat: 'Y-m-d'});"
                    x-ref="datetimewidget"
                    class="flatpick mx-auto mt-5"
                >
                    <label for="startdate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ السند:</label>
                    <div class="flex align-middle align-content-center">
                        <input
                            x-ref="startdate"
                            type="text"
                            id="startdate"
                            wire:model.live="startdate"
                            data-input
                            placeholder="اختر التاريخ"
                            class="block w-full p-2 border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-r-md shadow-sm"
                        >
                        <a
                            class="h-12 w-10 input-button cursor-pointer rounded-l-md bg-slate-300 hover:bg-slate-600 text-slate-600 hover:text-slate-100 border-gray-300 border-t border-b border-l"
                            title="clear" data-clear
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mt-2 ml-1" viewBox="0 0 20 20" fill="#4b4b4b">
                                <path fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd"/>
                            </svg>
                        </a>
                    </div>
                </div>
                @error('startdate')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="paid" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المبلغ: </label>
                <input type="text" wire:model.live="paid" id="paid" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب المبلغ" required>
                @error('paid')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            @if ( $saletype == 'تجديد للطالب' )
                <div class="mb-4">
                    <label for="numofclasses" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عدد الحصص: </label>
                    <input type="text" wire:model.live="numofclasses" id="numofclasses" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="عدد الحصص" required>
                    @error('numofclasses')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
            @else

            @endif
            <div class="mb-4">
                <label for="bank_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة التحويل:</label>
                <select wire:model.live="bank_id" id="role" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option style="direction:rtl;"  selected>اختر طريقة التحويل</option>
                    @foreach ($banks as $bank)
                    <option style="direction:rtl;" value="{{ $bank->id }}">{{ $bank->country }} : {{ $bank->name }} [ {{ $bank->balance }} ]</option>
                    @endforeach
                </select>
                @error('bank_id')
                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            @if ( $saletype == 'تجديد للطالب' )
                <div class="mb-4">
                    <label for="trans_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كود التحويل: </label>
                    <input type="text" wire:model.live="trans_code" id="trans_code" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="كود التحويل" required>
                    @error('trans_code')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
            @else

            @endif
            <div class="flex justify-end">
                <button type="button" wire:loading.remove wire:click.prevent="update" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                <x-loading method="update"></x-loading>

                <button type="button" wire:click.prevent="closemodal('edit')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
            </div>
            </form>
            @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                text-center<div class="mb-4 text-right flex justify-center">
                    <div dir="ltr" class="text-xl font-bold text-gray-300">
                        {{ $lastupdatedat }} : {{ $lastupdateuser_id }}
                    </div>
                </div>
            @else

            @endif
        </div>
    </x-g-modal>

    <!-- flash save successful message -->
    <x-flash-message on="flashsaved">
        <div class="flex items-center bg-green-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تسجيل العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    <!-- flash update successful message -->
    <x-flash-message on="flashupdated">
        <div class="flex items-center bg-yellow-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تعديل العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    {{-- data tables --}}
    <div class="card">
        <header class=" block card-header noborder">
          <h4 class="text-primary-500 font-bold text-3xl">التقارير المالية للايرادات</h4>
        </header>
        <div x-data="{open: false, shown: true }" x-defer class="card-body px-6 pb-6">
          <div class="sm:grid sm:my-2 md:flex sm:place-content-center md:justify-between w-auto mr-0">
            {{-- control buttons --}}
            <div class="grid sm:grid-cols-1 lg:grid-cols-6 gap-3">
                {{-- add modal button --}}
                @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                <button type="button" x-data="" x-on:click.prevent="$dispatch('open-modal', 'add')" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div class="flex place-items-center">
                    <iconify-icon icon="heroicons:plus-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    اضافة سند جديد
                    </div>
                </button>
                @endif
                {{-- advanced section modal button --}}
                <button type="button" x-on:click="open = ! open" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div x-show="! open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اظهار البحث الــمتقدم
                    </div>
                    <div x-show="open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اخفاء البحث الــمتقدم
                    </div>
                </button>
                @if (!empty($filterdatestart) || !empty($filterdateend) || !empty($filtersalesman) || !empty($filteractive) || !empty($filtercountry))
                    @if (auth()->user()->role === 'superadmin' || auth()->user()->role === 'accountant')
                    <button wire:click.prevent="resetallfilter" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div class="flex place-items-center">
                            <iconify-icon icon="heroicons:arrow-uturn-down-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                            إعادة تعيين التصفية
                        </div>
                    </button>
                    @endif
                @endif
                {{-- print report button --}}
                <button type="button" onclick="printReport()" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div class="flex place-items-center">
                        <iconify-icon icon="heroicons:printer-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="طباعة"></iconify-icon>
                        طباعة التقرير
                    </div>
                </button>
            </div>
            {{-- search field --}}
            <div class="my-3 sm:w-auto sm:mt-0 sm:ml-auto md:ml-0">
                <div class="relative flex w-80 text-gray-700 mr-auto my-auto place-items-center">
                    @if (!empty($search))
                        <button type="button" wire:click.prevent="resetsearch" class="absolute inset-y-0 left-0 pl-3 flex items-center z-1">
                            <iconify-icon icon="heroicons:x-mark-20-solid" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                        </button>
                    @endif
                    <iconify-icon icon="heroicons:magnifying-glass-solid" style="font-size: 24px;color:#016241;" alt="تعديل" class="ml-2"></iconify-icon>
                    <input wire:model.live.debounce.500="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-lg rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="بحث..." wire:ignore>
                </div>
            </div>
          </div>
          {{-- advanced filter by status and teacher --}}
          <div x-show="open" class="grid sm:grid-cols-1 lg:grid-cols-3 place-items-center gap-2 my-2">
            @if (auth()->user()->role === 'superadmin' || auth()->user()->role === 'accountant')
                <div class="flex w-full place-items-center">
                <label for="filtersalesman" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">البائع: </label>
                <select name="filtersalesman" wire:model.live="filtersalesman" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option style="direction:rtl;" value="" selected>اختر البائع</option>
                    @foreach ( $sales as $sale)
                        <option style="direction:rtl;" value="{{ $sale->id }}">{{ $sale->name }}</option>
                    @endforeach
                </select>
                @if (!empty($filtersalesman))
                <button type="button" wire:click.prevent="resetfilter('filtersalesman')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                    <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                </button>
                @endif
                </div>
            @endif
            @if (auth()->user()->role === 'superadmin' || auth()->user()->role === 'accountant')
                <div class="flex w-full place-items-center">
                <label for="filterregstatus" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">تسجيل / تجديد: </label>
                <select name="filterregstatus" wire:model.live="filterregstatus" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option style="direction:rtl;" value="" selected>اختر مما يلي</option>
                    <option style="direction:rtl;" value="new" > اشتراك </option>
                    <option style="direction:rtl;" value="resub" > تجديد </option>
                    <option style="direction:rtl;" value="other" > اخرى </option>
                </select>
                @if (!empty($filterregstatus))
                <button type="button" wire:click.prevent="resetfilter('filterregstatus')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                    <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                </button>
                @endif
                </div>
            @endif
            <div class="flex w-full place-items-center">
            <label for="filtercountry" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">البلد: </label>
            <select name="filtercountry" wire:model.live="filtercountry" style="direction:ltr;" class="shadow-sm rounded-md px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;" value="" selected>اختر البلد</option>
                    @foreach ( $listcountries as $key => $listcountry)
                        <option style="direction:rtl;" value="{{ $listcountry }}">{{ $listcountry }}</option>
                    @endforeach
            </select>
            @if (!empty($filtercountry))
            <button type="button" wire:click.prevent="resetfilter('filtercountry')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
          </div>
          {{-- advanced date filter --}}
          <div x-show="open" class="grid sm:grid-cols-1 lg:grid-cols-3 place-items-center gap-2 my-2">
                <div class="flex w-full place-items-center">
                    <label for="filterdatestart" class="block text-wrap text-lg font-medium text-gray-700 dark:text-[#016241] ml-2">من: </label>
                    <input type="date" id="filterdatestart" wire:model.live="filterdatestart" value="{{ $filterdatestart }}" name="filterdatestart" min="2023-01-01" max="2050-12-31" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
                    @if (!empty($filterdatestart))
                    <button type="button" wire:click.prevent="resetfilter('filterdatestart')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 mr-2 p-1 dark:hover:bg-slate-600 dark:hover:text-white">
                        <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                    </button>
                    @endif
                </div>
                <div class="flex w-full place-items-center">
                    <label for="filterdateend" class="block text-lg font-medium text-gray-700 dark:text-[#016241] ml-2 lg:mx-2">حتى: </label>
                    <input type="date" id="filterdateend" wire:model.live="filterdateend" value="{{ $filterdateend }}" name="filterdateend" min="2023-01-01" max="2050-12-31" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
                    @if (!empty($filterdateend))
                    <button type="button" wire:click.prevent="resetfilter('filterdateend')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 mr-2 p-1 dark:hover:bg-slate-600 dark:hover:text-white">
                        <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                    </button>
                    @endif
                </div>
            <div class="grid sm:grid-cols-1 lg:grid-cols-3 gap-3 w-full my-2">
                {{-- filter day/week/month button --}}
                <button type="button" wire:click.prevent="filterbyday" class="{{ $filterby == 'today' ? 'bg-[#b36700]' : 'bg-[#906842]' }} hover:bg-[#b36700] text-white font-bold py-4 px-4 rounded-lg place-content-center">
                    <iconify-icon icon="heroicons:calendar-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    ايرادات اليوم
                </button>
                <button type="button" wire:click.prevent="filterbyweek" class="{{ $filterby == 'week' ? 'bg-[#b36700]' : 'bg-[#906842]' }} hover:bg-[#b36700] text-white font-bold py-4 px-4 rounded-lg place-content-center">
                    <iconify-icon icon="heroicons:calendar-days-16-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    ايرادات الاسبوع
                </button>
                <button type="button" wire:click.prevent="filterbymonth" class="{{ $filterby == 'month' ? 'bg-[#b36700]' : 'bg-[#906842]' }} hover:bg-[#b36700] text-white font-bold py-4 px-4 rounded-lg align-middle mr-0 flex place-content-center">
                    <iconify-icon icon="heroicons:calendar-days-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    ايرادات الشهر
                </button>
            </div>
          </div>
          {{-- data table --}}
          <div id="report-content" class="overflow-x-auto -mx-6">
            <div class="inline-block min-w-full align-middle">
              <div class="overflow-hidden">
                <div class="text-lg font-medium text-white">{{ $filterdatestart }}, {{ $filterdateend }}</div>
                @unless ($salesreports->count() == 0)
                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl">
                  <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                    <tr>
                        <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('username')">اسم الطالب</a></th>
                        <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('name')">اسم البائع</a></th>
                        <th scope="col" class=" table-th text-xl font-medium">بداية الاشتراك</th>
                        <th scope="col" class=" table-th text-xl font-medium">مدفوع</th>
                        <th scope="col" class=" table-th text-xl font-medium">عدد الحصص</th>
                        <th scope="col" class=" table-th text-xl font-medium ">اشتراك / تجديد</th>
                        @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                        <th scope="col" class=" table-th text-xl font-medium ">أجراءات</th>
                        @endif
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                    @foreach ($salesreports as $salesreport)
                    <tr wire:key="sales-{{ $salesreport->id }}">
                        <td class="table-td text-xl font-normal">
                          <span class=" text-slate-600 dark:text-white capitalize">{{ $salesreport->student->name }}</span>
                        </td>
                        <td class="table-td text-xl font-normal">
                          <span class=" text-slate-600 dark:text-white capitalize">{{ $salesreport->sales->name }}</span>
                        </td>
                        <td class="table-td text-xl font-normal">
                          <span class=" text-slate-600 dark:text-white capitalize">{{ $salesreport->created_at }}</span>
                        </td>
                        <td class="table-td text-xl font-normal">
                          <span class=" text-slate-600 dark:text-white capitalize">{{ round($salesreport->paid) }}</span>
                          @php
                              $salesmantotalsales += $salesreport->paid;
                          @endphp
                        </td>
                        <td class="table-td text-xl font-normal">
                          <span class=" text-slate-600 dark:text-white capitalize">{{ $salesreport->numofclasses }}</span>
                        </td>
                        <td class="table-td text-xl font-normal">
                            @if ($salesreport->reg_status == 'new')
                                <span class=" inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border border-[#016241] text-white bg-success-500">تسجيل</span>
                            @elseif ($salesreport->reg_status == 'resub')
                                <span class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border border-[#016241] text-white bg-danger-500">تجديد</span>
                            @else
                                <span class=" inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border border-[#202020] text-white bg-success-500">اخرى</span>
                            @endif
                        </td>
                        @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                        <td class="table-td text-xl font-normal">
                            <div class="flex space-x-3 rtl:space-x-reverse">
                                <button type="button" wire:click.prevent="showedit({{ $salesreport }})">
                                  <iconify-icon icon="heroicons:pencil-square" style="font-size: 24px;color:#016241;font-weight:bold;" alt="تعديل"></iconify-icon>
                                </button>
                            </div>
                        </td>
                        @endif
                    </tr>
                    @endforeach
                  </tbody>
                </table>
                <div class="flex my-4 justify-between font-cairo text-xl text-[#016241]">
                    <div class="w-full">
                        <div class="flex justify-center">
                            مجموع الايرادات: {{ round($salesmantotalsales) }} ريال سعودي
                        </div>
                    </div>
                </div>
                @else
                <div class="text-center text-xl font-bold text-[#016241]">لا يوجد تقارير حتى الآن.</div>
                @endunless
              </div>
            </div>
          </div>
        </div>
    </div>
    <div class="flex my-4">
        <div class="flex justify-start w-[50%] items-center">
            <label for="perpage" class="text-lg font-medium text-gray-700 dark:text-[#be9539]">عدد النتائج في كل صفحة:</label>
            <select wire:model.live="perpage" id="role" style="direction:ltr;" class="w-20 shadow-sm rounded-md mx-4 px-5 py-1 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;" value="10">10</option>
                <option style="direction:rtl;" value="20">20</option>
                <option style="direction:rtl;" value="30">30</option>
                <option style="direction:rtl;" value="50">50</option>
                <option style="direction:rtl;" value="2000">الكل</option>
            </select>
        </div>
        <div class="w-[100%] mr-4">{{ $salesreports->links('vendor.livewire.tailwind') }}</div>
    </div>
@push('scripts')
<script>
function printReport() {
  const reportContent = document.getElementById('report-content');
  const printWindow = window.open();
  printWindow.document.write(reportContent.outerHTML);
  printWindow.print();
  printWindow.close();
}
</script>
@endpush
</div>
