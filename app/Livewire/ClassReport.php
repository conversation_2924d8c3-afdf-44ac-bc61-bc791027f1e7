<?php

namespace App\Livewire;

use App\Models\Clss;
use App\Models\Ratings;
use App\Models\Settings;
use App\Models\SuperVisorReport;
use App\Models\Teacher;
use App\Models\TeacherReport;
use App\Models\User;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;
use App\Traits\UseFilter;

class ClassReport extends Component
{
    use WithPagination, UseFilter;

    public $students = [];
    public $teachers = [];
    public $supervisors = [];

    public function getTeacherReport($classID)
    {
        $classreport = TeacherReport::where('class_id', $classID)->first();
        if(!empty($classreport))
        {
            return 'التسميع: ' . $classreport->recite_eval .' المراجعة: ' . $classreport->revision_eval;
        }
        return '------------';
    }
    public function getSupervisorReport($classID)
    {
        $classreport = SuperVisorReport::where('class_id', $classID)->first();
        if(!empty($classreport))
        {
            return (($classreport->internet + $classreport->camera + $classreport->smartboard + $classreport->audio + $classreport->teachbackground + $classreport->teachenvironment + $classreport->teachrelationship + $classreport->teacheducational) / 80 ) * 100 . ' %';
        }
        return '------------';
    }
    public function getStudentReport($classID)
    {
        $classreport = Ratings::where('class_id', $classID)->first();
        $rate = '';
        if(!empty($classreport))
        {
            for ($i=0; $i < $classreport->rate; $i++) {
                $rate .= '⭐';
            }
            return $rate;
        }
        return '------------';
    }

    public function mount()
    {
        $this->perpage = Settings::find(1)->perpage;
        $this->sortBy = 'date_time';
        $this->sortDir = 'ASC';

        $this->filterbymonth();

        $this->students = User::filterrole('student')->get();
        $this->teachers = User::filterrole('teacher')->get();
        $this->supervisors = User::filterrole('supervisor')->get();
    }
    public function render()
    {
        return view('livewire.class-report', [
            'classes' => Clss::filterdate($this->filterdatestart, $this->filterdateend)
            ->with('clssreport')
            ->search($this->search)
            ->filterteacher($this->filterteacher)
            ->filterstudent($this->filterstudent)
            ->orderBy($this->sortBy, $this->sortDir)
            ->paginate($this->perpage),
        ]);
    }
}
