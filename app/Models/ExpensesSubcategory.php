<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExpensesSubcategory extends Model
{
    use HasFactory;

    protected $table = 'expenses_subcategories';

    protected $fillable = [
        'name',
        'category_id',
        'notes',
    ];

    public function category()
    {
        return $this->belongsTo(ExpensesCategory::class, 'category_id', 'id');
    }
    public function expenses()
    {
        return $this->hasOne(Expenses::class, 'subcategory_id', 'id');
    }

    public function scopeFilterexpecate($query, $value)
    {
        if(empty($value))
        {
            return $query;
        }
        return $query->where('category_id', $value);
    }
}
