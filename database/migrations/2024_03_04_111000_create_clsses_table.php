<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clsses', function (Blueprint $table) {
            $table->id();
            $table->integer('teacher_id')->unsigned();
            $table->integer('student_id')->unsigned();
            $table->integer('qualifi_id')->unsigned();
            $table->string('zmeeting_id');
            $table->dateTime('date_time');
            $table->longText('start_meeting')->nullable();
            $table->longText('join_meeting')->nullable();
            $table->string('is_active')->default('not');
            $table->integer('lastupdateuser_id')->unsigned();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clsses');
    }
};
