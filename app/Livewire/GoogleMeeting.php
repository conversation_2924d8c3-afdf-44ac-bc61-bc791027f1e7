<?php

namespace App\Livewire;

use Livewire\Component;
use Carbon\Carbon;
use Spatie\GoogleCalendar\Event;

class GoogleMeeting extends Component
{
    public function index()
    {
        // get all future events on a calendar
        $events = Event::get();
        return $events;
    }
    public function createMeeting()
    {
        //create a new event
        // $event = new Event;

        // $event->name = 'A new event';
        // $event->description = 'Event description';
        // $event->startDateTime = Carbon::now();
        // $event->endDateTime = Carbon::now()->addHour();
        // $event->addAttendee([
        //     'email' => '<EMAIL>',
        //     'name' => '<PERSON>',
        //     'comment' => 'Lorum ipsum',
        //     'responseStatus' => 'needsAction',
        // ]);
        // $event->addAttendee(['email' => '<EMAIL>']);
        // $event->addMeetLink(); // optionally add a google meet link to the event

        // $event->save();

        // if ($response->successful()) {
        //     return [
        //         'success' => $response->getStatusCode() === 201,
        //         'data'    => json_decode($response->getBody(), true),
        //     ];
        // } else {
        //     return [
        //         'success' => $response->getStatusCode() === 201,
        //         'data'    => json_decode($response->getBody(), true),
        //     ];
        //     return response()->json(['error' => 'Failed to create a Zoom meeting'], 500);
        // }

        // create a new event
        $event  = Event::create([
            'name' => 'A new event',
            'description' => 'Event description',
            'startDateTime' => Carbon::now(),
            'startTimezone' => 'Africa/Cairo',
            'endTimezone' => 'Africa/Cairo',
            'endDateTime' => Carbon::now()->addMinutes(30),
            'addAttendee' => [
                    'email' => '<EMAIL>',
                    'name' => 'John Doe',
                    'comment' => 'Lorum ipsum',
                    'responseStatus' => 'needsAction',
                ],
        ]);
        return 'meeting created.';
    }
    public function listMeetings()
    {
        $events = Event::get();
        // return $events;
        echo count($events) . '<br>';
        foreach ($events as $meeting)
        {
            echo $meeting->id . '<br>';
            echo $meeting->name . '<br>';
            echo $meeting->description . '<br>';
            echo $meeting->startDateTime . '<br>';
            echo $meeting->endDateTime . '<br>';
            if(!empty($meeting->attendees))
            {
                foreach ($meeting->attendees as $participant)
                {
                    echo $participant->name . '<br>';
                    echo $participant->email . '<br>';
                    echo $participant->comment . '<br>';
                }
            }
            echo ' ----------------------------- <br>';
        }
    }
    public function meetingInfo($meeting)
    {
        $event = Event::find($meeting);
        return response()->json($event);
    }
    public function updateMeeting($meeting)
    {
        $event = Event::find($meeting);

        // update existing event
        // $event->name = 'updated name';
        // $event->save();

        $event->update(['name' => 'updated again']);
    }
    public function deleteMeeting($meeting)
    {
        $event = Event::find($meeting);
        // delete an event
        $event->delete();
        return '"' . $meeting . '" Meeting is deleted.';
    }
    public function render()
    {
        return view('livewire.google-meeting');
    }
}
