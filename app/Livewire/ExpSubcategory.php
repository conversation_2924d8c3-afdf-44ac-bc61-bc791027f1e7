<?php

namespace App\Livewire;

use App\Models\Expenses;
use App\Models\ExpensesCategory;
use App\Models\ExpensesSubcategory;
use App\Models\Settings;
use Livewire\Attributes\Computed;
use Livewire\WithPagination;
use App\Traits\UseFilter;

use Livewire\Component;

class ExpSubcategory extends Component
{
    use WithPagination, UseFilter;

    public $selectedexpsubcategories = [];
    public $listcountries = [];
    public $selectall = false;

    public $expcate_id;
    public $name = '';
    public $category_id;
    public $notes = '';

    public function save()
    {
        $validated = $this->validate([
            'name' => 'required|unique:expenses_categories,name|string|max:120',
            'category_id' => 'required|numeric|max:99',
            'notes' => 'string|max:500',
        ]);

        ExpensesSubcategory::create($validated);
        $this->closemodal('add');
        $this->dispatch('flashsaved');

    }
    public function showedit(ExpensesSubcategory $expcate)
    {
        $this->expcate_id = $expcate->id;
        $this->name = $expcate->name;
        $this->category_id = $expcate->category_id;
        $this->notes = $expcate->notes;
        $this->openmodal('edit');
    }
    public function update()
    {
        $validated = $this->validate([
            'name' => 'required|unique:expenses_categories,name,' . $this->expcate_id . '|string|max:120',
            'category_id' => 'required|numeric|max:99',
            'notes' => 'string|max:500',
        ]);

        ExpensesSubcategory::find($this->expcate_id)->update($validated);

        $this->closemodal('edit');
        $this->dispatch('flashupdated');

    }
    public function showdelete(ExpensesSubcategory $expcate)
    {
        $this->expcate_id = $expcate->id;
        $this->name = $expcate->name;
        $this->category_id = $expcate->category_id;
        $this->notes = $expcate->notes;
        $this->openmodal('delete');
    }
    public function delete(){

        ExpensesSubcategory::find($this->expcate_id)->delete();

        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }
    public function deleteselected()
    {
        ExpensesSubcategory::whereIn('id', $this->selectedexpsubcategories)->delete();
        $this->selectedexpsubcategories = [];
        $this->closemodal('deleteselected');
    }


    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedexpsubcategories = ExpensesSubcategory::paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedexpsubcategories = [];
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'expcate_id',
            'name',
            'category_id',
            'notes',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function gettotalexpenses($category)
    {
        $expenses = Expenses::filterdate($this->filterdatestart, $this->filterdateend)
        ->filterexpsubcate($category->id)->get();
        return $expenses->sum('amount');
    }
    public function mount()
    {
        if (empty($this->perpage)) {
            $this->perpage = Settings::find(1)->perpage;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'ASC';
    }
    public function render()
    {
        $expsubcategories = ExpensesSubcategory::filterexpecate($this->filterexpcate)
        ->paginate($this->perpage);
        $expsubcategories_ids = $expsubcategories->pluck('id');
        $expenses = Expenses::whereIn('subcategory_id', $expsubcategories_ids);
        $totalexpense = $expenses->sum('amount');
        $expcategories = ExpensesCategory::all();
        return view('livewire.exp-subcategory', [
            'expsubcategories' => $expsubcategories,
            'totalexpense' => $totalexpense,
            'expcategories' => $expcategories,
        ]);
    }
}
