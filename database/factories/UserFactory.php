<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $unikey = fake()->unique()->numberBetween(1, 30);
        $uniword = fake()->randomElement(['admin', 'moderator', 'supervisor', 'accountant', 'sales']);
        $testfake = fake('ar_EG')->country();
        return [
            'name' => fake('ar_EG')->name(),
            'username' => substr($uniword, 0, 4) . $unikey . 'com',
            'password' => Hash::make('********'),
            'email' => ucwords(substr($uniword, 0, 2)) . $unikey . '@ex.com',
            'email_verified_at' => fake()->date('Y-m-d', 'now'),
            'remember_token' => Str::random(20),
            'phone' => fake()->phoneNumber(),
            'country' => $testfake,
            'is_active' => false,
            'role' => $uniword,
            'status' => true,
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
