<?php

namespace App\Livewire;

use App\Models\Notification;
use App\Models\Settings;
use App\Models\Teacher as ModelsTeacher;
use App\Models\TeacherFile;
use App\Models\UserHistory;
use App\Models\TeacherHourRate;
use App\Models\TeacherQualifications;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;
use App\Traits\UseFilter;
use App\Traits\UseDefault;
use Carbon\Carbon;
use Livewire\Features\SupportFileUploads\WithFileUploads;

class Teacher extends Component
{
    use WithPagination, WithFileUploads, UseDefault, UseFilter;

// from user model
    public $teacherid = '';
    public $name = '';
    public $username = '';
    public $password = '';
    public $email = '';
    public $phone = '';
    public $country = '';
    public $image = '';
    public $showpass = false;
    public $status;

    // from teacher model
    public $zoomuser_id = '';
    public $gender = '';
    public $rateperclass = '';
    public $transfersalary = '';
    public $transferdetails = '';
    public $qualifications = [];
    public $upfiles = [];
    public $oldupfiles = [];
    public $teacherfiles = [];
    public $upfilesnum = 0;
    public $notes = '';

    // fixed in this component
    public $role = 'teacher';
    public $curr = -1;

// selected / select all
    public $listqualifications = [];
    public $selectedteachers = [];
    public $selectall = false;
    public $showdetails = false;

    public function save()
    {
        $validateduser = $this->validate([
            'name' => 'required|string|min:3|max:100',
            'username' => 'required|regex:/^[a-zA-Z]+$/|min:3|max:100|unique:user,username',
            'email' => 'required|email|min:5|max:100|unique:user,email',
            'password' => 'required|string|min:8|max:25',
            'phone' => 'required|string|min:11|max:20',
            'country' => 'required|string|min:3|max:40',
            'role' => '',
            'status' => 'required|boolean',
        ]);

        $validateduser['password'] = Hash::make($validateduser['password']);

        $validatedteacher = $this->validate([
            'zoomuser_id' => 'nullable|sometimes|string|max:25',
            'gender' => 'required|string|min:3|max:20',
            'rateperclass' => 'required|numeric|min:0|max:9999',
            'transfersalary' => 'required|string|min:3|max:20',
            'transferdetails' => 'required|string|min:3|max:30',
            'qualifications' => 'nullable|array',
            'upfiles' => 'nullable|sometimes|array',
            'upfiles.*.filename' => 'required_with:upfiles.*.uploadedfile|string|min:3|max:150',
            'upfiles.*.uploadedfile' => 'nullable|sometimes|file|image|mimes:jpeg,png,jpg,gif,pdf,doc,docx,bmp,xls,xlsx|max:2048',
            'notes' => 'string|max:500',
            ]);

        if($validateduser && $validatedteacher)
        {
            $user = User::create($validateduser);
            $teacherfilesid = [];
            if(!empty($validatedteacher['upfiles']))
            {
                foreach ($validatedteacher['upfiles'] as $index => $value) {

                    $teacherfile = TeacherFile::create([
                        'user_id' => $user->id,
                        'name' => $value['filename'],
                        'url' => $value['uploadedfile']->store('uploads/teachersfiles', 'public'),
                    ]);
                    $teacherfilesid[$index] = $teacherfile->id;
                }
            } else {
                $validatedteacher['upfiles'] = [];
            }
            // dd($validatedteacher);
            ModelsTeacher::create([
                'zoomuser_id' => $validatedteacher['zoomuser_id'],
                'user_id' => $user->id,
                'gender' => $validatedteacher['gender'],
                'rateperclass' => $validatedteacher['rateperclass'],
                'transfersalary' => $validatedteacher['transfersalary'],
                'transferdetails' => $validatedteacher['transferdetails'],
                'qualifications' => json_encode($validatedteacher['qualifications']),
                'upfiles' => json_encode($teacherfilesid),
                'notes' => $validatedteacher['notes'],
            ]);
            UserHistory::create([
                'user_id' => $user->id,
                'start_date' => Carbon::today()->format('Y-m-d'),
                'stop_date' => null,
            ]);
            TeacherHourRate::create([
                'user_id' => $user->id,
                'start_date' => Carbon::today()->format('Y-m-d'),
                'rate' => $validatedteacher['rateperclass'],
            ]);
            $notifiedusers = User::whereIn('role', ['superadmin', 'admin', 'moderator'])->pluck('id');
            foreach ($notifiedusers as $notifieduser) {
                Notification::create([
                    'user_id' => $notifieduser,
                    'title' => 'new teacher',
                    'url' => 'teacher?q=' . $user->name ,
                    'message' => 'تم اضافة معلم جديد "' . $user->name . '" بواسطة "' . auth()->user()->name . '".',
                ]);
            }
        }
        $this->closemodal('add');
        $this->dispatch('flashsaved');
    }
    public function showview(User $user)
    {
        $this->teacherid = $user->id;
        $this->name = $user->name;
        $this->username = $user->username;
        $this->email = $user->email;
        $this->phone = $user->phone;
        $this->image = $user->image;
        $this->country = $user->country;
        $this->status = $user->status;

        $teacher = ModelsTeacher::where('user_id', $user->id)->first();
        $this->zoomuser_id = $teacher->zoomuser_id;
        $this->gender = $teacher->gender;
        $this->rateperclass = $teacher->rateperclass;
        $this->qualifications = json_decode($teacher->qualifications) ?? [];
        $this->transfersalary = $teacher->transfersalary;
        $this->transferdetails = $teacher->transferdetails;
        $this->oldupfiles = TeacherFile::where('user_id', $this->teacherid)->get();
        // dd($this->upfiles);
        $this->notes = $user->teacher->notes;

        $this->openmodal('view');
    }
    public function showedit(User $user)
    {
        $this->teacherid = $user->id;
        $this->name = $user->name;
        $this->username = $user->username;
        $this->email = $user->email;
        $this->phone = $user->phone;
        $this->country = $user->country;
        $this->status = $user->status;

        $this->zoomuser_id = $user->teacher->zoomuser_id;
        $this->gender = $user->teacher->gender;
        $this->rateperclass = $user->teacher->rateperclass;
        $this->transfersalary = $user->teacher->transfersalary;
        $this->showdetails = $this->transfersalary == '' ? false : true;
        $this->transferdetails = $user->teacher->transferdetails;
        // dd($user->teacher->qualifications);
        $this->qualifications = json_decode($user->teacher->qualifications) ?? [];
        $this->oldupfiles = TeacherFile::where('user_id', $this->teacherid)->get();
        // dd($this->oldupfiles);
        $this->curr = count($this->oldupfiles)-1;
        $this->upfilesnum = count($this->oldupfiles);

        // dd($this->upfiles,$this->upfilesnum, $this->curr);

        $this->notes = $user->teacher->notes;
        // dd($this->qualifications);
        $this->openmodal('edit');
    }
    public function update()
    {
        if(!empty($this->password))
        {
            $validateduser = $this->validate([
                'name' => 'required|string|min:3|max:100',
                'username' => 'required|regex:/^[a-zA-Z]+$/|min:3|max:100',
                'email' => 'required|email|min:5|max:100',
                'password' => 'required|string|min:8|max:25',
                'phone' => 'required|string|min:11|max:20',
                'country' => 'required|string|min:3|max:40',
                'role' => '',
                'status' => 'required|boolean',
            ]);
            $validateduser['password'] = Hash::make($validateduser['password']);
        } else {
            $validateduser = $this->validate([
                'name' => 'required|string|min:3|max:100',
                'username' => 'required|regex:/^[a-zA-Z]+$/|min:3|max:100',
                'email' => 'required|email|min:5|max:100',
                'password' => '',
                'phone' => 'required|string|min:11|max:20',
                'country' => 'required|string|min:3|max:40',
                'role' => '',
                'status' => 'required|boolean',
            ]);
        }
        $validatedteacher = $this->validate([
            'zoomuser_id' => 'nullable|sometimes|string|max:25',
            'gender' => 'required|string|min:3|max:20',
            'rateperclass' => 'required|numeric|min:0|max:9999',
            'transfersalary' => 'required|string|min:3|max:20',
            'transferdetails' => 'required|string|min:3|max:30',
            'qualifications' => 'nullable|array',
            'upfiles' => 'nullable|sometimes|array',
            'upfiles.*.filename' => 'required_with:upfiles.*.uploadedfile|string|min:3|max:150',
            'upfiles.*.uploadedfile' => 'nullable|sometimes|file|image|mimes:jpeg,png,jpg,gif,pdf,doc,docx,bmp,xls,xlsx|max:2048',
            'notes' => 'nullable|string|max:500',
        ]);

        // dd($this->upfiles,$this->upfilesnum, $this->curr,$validatedteacher);

        if($validateduser && $validatedteacher)
        {
            $user = User::find($this->teacherid);

            if(!empty($validatedteacher['upfiles']))
            {
                // dd($validatedteacher['upfiles'][0]['uploadedfile']);
                // foreach ($validatedteacher['upfiles'] as $index => $value) {
                //     $validatedteacher['upfiles'][$index]['uploadedfile'] = $value['uploadedfile']->store('uploads/teachersfiles', 'public');
                // }
                foreach ($validatedteacher['upfiles'] as $index => $value) {

                    $teacherfile = TeacherFile::create([
                        'user_id' => $user->id,
                        'name' => $value['filename'],
                        'url' => $value['uploadedfile']->store('uploads/teachersfiles', 'public'),
                    ]);
                    $teacherfilesid[$index] = $teacherfile->id;
                }
            } else {
                $validatedteacher['upfiles'] = $this->upfiles;
            }

            // dd($validatedteacher['upfiles']);

            if($this->status != $user->status)
            {
                if($this->status == false)
                {
                    UserHistory::where('user_id', $user->id)->update([
                        'stop_date' => Carbon::today()->format('Y-m-d'),
                    ]);
                } else {
                    UserHistory::create([
                        'user_id' => $user->id,
                        'start_date' => Carbon::today()->format('Y-m-d'),
                        'stop_date' => null,
                    ]);
                }
            }
            if($validatedteacher['rateperclass'] != $user->teacher->rateperclass)
            {
                TeacherHourRate::create([
                    'user_id' => $user->id,
                    'start_date' => Carbon::today()->format('Y-m-d'),
                    'rate' => $validatedteacher['rateperclass'],
                ]);
            }

            ModelsTeacher::where('user_id', $user->id)->update([
                'zoomuser_id' => $validatedteacher['zoomuser_id'],
                'gender' => $validatedteacher['gender'],
                'rateperclass' => $validatedteacher['rateperclass'],
                'transfersalary' => $validatedteacher['transfersalary'],
                'transferdetails' => $validatedteacher['transferdetails'],
                'qualifications' => json_encode($validatedteacher['qualifications']),
                'upfiles' => json_encode($validatedteacher['upfiles']),
                'notes' => $validatedteacher['notes'],
            ]);

            $notifiedusers = User::whereIn('role', ['superadmin', 'admin', 'moderator'])->pluck('id');
            foreach ($notifiedusers as $notifieduser) {
                Notification::create([
                    'user_id' => $notifieduser,
                    'title' => 'teacher updated',
                    'url' => 'teacher?q=' . $user->name ,
                    'message' => 'تم تعديل المعلم "' . $user->name . '" بواسطة "' . auth()->user()->name . '".',
                ]);
            }
            $user->update($validateduser);
        }
        $this->closemodal('edit');
        $this->dispatch('flashupdated');
    }
    public function showdelete(User $teacher)
    {
        $this->teacherid = $teacher->id;
        $this->name = $teacher->name;
        $this->openmodal('delete');
    }
    public function delete()
    {
        $user = User::find($this->teacherid);
        $notifiedusers = User::where('role', 'admin')->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'teacher deleted',
                'url' => 'teacher',
                'message' => 'تم حذف المعلم "' . $user->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }
        $user->delete();

        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
        // $this->flashmessage('deleteselected');
    }
    public function deleteselected()
    {
        User::query()->whereIn('id', $this->selectedteachers)->delete();

        $this->selectedteachers = [];
        $this->selectall = false;
        $this->closemodal('deleteselected');
        // $this->flashmessage('flashdeleted');
    }
    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedteachers = $this->perpage === 'all' ? User::search($this->search)
            ->filterrole($this->role)
            ->filtercountry($this->filtercountry)
            ->filteractive($this->filteractive)
            ->filtergender($this->filtergender)
            ->filterqualifications($this->filterqualifications)
            ->orderBy($this->sortBy, $this->sortDir)
            ->pluck('id') :
            User::search($this->search)
            ->filterrole($this->role)
            ->filtercountry($this->filtercountry)
            ->filteractive($this->filteractive)
            ->filtergender($this->filtergender)
            ->filterqualifications($this->filterqualifications)
            ->orderBy($this->sortBy, $this->sortDir)
            ->paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedteachers = [];
        }
    }

    #[Computed]
    public function updatedtransfersalary()
    {
        if(!empty($this->transfersalary) || $this->transfersalary != '')
        {
            $this->showdetails = true;
        } else {
            $this->showdetails = false;
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'teacherid',
            'name',
            'username',
            'email',
            'password',
            'role',
            'phone',
            'country',
            'status',
            'gender',
            'rateperclass',
            'qualifications',
            'transfersalary',
            'transferdetails',
            'upfiles',
            'oldupfiles',
            'upfilesnum',
            'notes',
            'showdetails',
            'curr',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function redirectToUrl($url)
    {
        $this->dispatch('open-new-window', $url);
    }
    public function showteacher($id)
    {
        $url = 'pdf/teacher/show/' . $id;
        $this->redirectToUrl($url);
    }
    public function printteacher($id)
    {
        $url = 'pdf/teacher/download/' . $id;
        $this->redirectToUrl($url);
    }
    public function qualiname($quali)
    {
        return TeacherQualifications::find($quali)->name;
    }
    public function removeoldupfile($fileid)
    {
        $this->upfilesnum--;
        TeacherFile::find($fileid)->delete();
    }
    public function transferetitle($name)
    {
        switch ($name) {
            case 'e-wallet':
                return 'المحفظة الالكترونية';
                break;

            case 'instapay':
                return 'انستاباي';
                break;



            case 'bankaccount':
                return 'الحساب البنكي';
                break;



            case 'others':
                return 'اخرى';
                break;


            default:
                return '';
                break;
        }
    }
    public function addupfiles($index)
    {
        $this->upfiles[$index] = ['filename' => '', 'uploadedfile' => ''];
        $this->upfilesnum++;
    }
    public function removeupfiles($index)
    {
        $this->upfilesnum--;
        unset($this->upfiles[$index]);
    }
    public function mount()
    {
        if (empty($this->perpage)) {
            $this->perpage = Settings::find(1)->perpage;
        }
        $this->listqualifications = TeacherQualifications::all();
        $this->sortBy = 'created_at';
        $this->sortDir = 'DESC';
        $this->listcountries;
    }
    public function render()
    {
    $teachers = $this->perpage === 'all' ? User::search($this->search)
    ->filterrole($this->role)
    ->filtercountry($this->filtercountry)
    ->filteractive($this->filteractive)
    ->filtergender($this->filtergender)
    ->filterqualifications($this->filterqualifications)
    ->orderBy($this->sortBy, $this->sortDir)
    ->get() :
    User::search($this->search)
    ->filterrole($this->role)
    ->filtercountry($this->filtercountry)
    ->filteractive($this->filteractive)
    ->filtergender($this->filtergender)
    ->filterqualifications($this->filterqualifications)
    ->orderBy($this->sortBy, $this->sortDir)
    ->paginate($this->perpage);
        return view('livewire.teacher', [
            'teachers' => $teachers,
        ]);
    }
}
