<?php

use App\Models\User;
use App\Models\Student;
use App\Models\ReferalCode;
use App\Models\Notification;
use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;
use App\Traits\UseDefault;
use Carbon\Carbon;

new #[Layout('layouts.guest')] class extends Component
{
    use UseDefault;

    public string $name = '';
    public string $username = '';
    public string $email = '';
    public string $password = '';
    public string $password_confirmation = '';
    public $phone = '';
    public $country = '';

    // from student model
    public $studentid;
    public $dateofbirth = '';
    public $subscription = '';
    public $gender_prefer = '';
    public $paid = 0;
    public $numofclasses = '';
    public $ref_code = '';
    public $balance = 0;
    public $refcode_active_date = null;
    public $startdate = '';

    public $role = 'student';
    public $inv_code = '';
    public $getinvitecode = '';
    public $getinviteuser = '';
    public $getdiscountcode;

    public $code = '';
    public $ref_code_stat = false;
    public $retmessage = '';

    /**
     * Handle an incoming registration request.
     */
    public function register(): void
    {
        $addusedby = [];
        $validated = $this->validate([
            'name' => ['required', 'string', 'max:255'],
            'username' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
            'phone' => 'required|string|min:11|max:20',
            'country' => 'required|string|min:3|max:40',
        ]);
        $validatedstudent = $this->validate([
            'dateofbirth' => 'required|date',
            'gender_prefer' => 'required',
            'ref_code' => 'string|nullable|sometimes|min:7|max:9',
        ]);

        $validated['password'] = Hash::make($validated['password']);
        $validated['role'] = $this->role;


        event(new Registered($user = User::create($validated)));

        $this->studentid = $user->id;

        $student = Student::create([
            'user_id' => $user->id,
            'dateofbirth' => $validatedstudent['dateofbirth'],
            'gender_prefer' => $validatedstudent['gender_prefer'],
            'subscription' => 'trial',
            'paid' => $this->paid,
            'numofclasses' => 3,
            'ref_code' => $validatedstudent['ref_code'],
            'balance' => $this->balance,
            'inv_code' => $this->inv_code,
            'refcode_active_date' => $this->refcode_active_date,
            'startdate' => Carbon::now(),
        ]);

        $token = $user->createToken('myAppToken')->plainTextToken;

        if(!empty($this->ref_code))
        {
            $this->ref_code_stat = true;
            $refcode = ReferalCode::where('short_code', $this->ref_code)->first();
            $addusedby = json_decode($refcode->used_by);
            if (is_array($addusedby)) {
                array_push($addusedby, $user->id);
            } else {
                $addusedby[] += $user->id;
            }
            // dd($addusedby);

            $refcode->update([
                'used_by' => json_encode($addusedby),
            ]);

            $refcodediscount = ReferalCode::getdiscount($this->ref_code)->first();
            if(!empty($refcodediscount))
            {
                $this->balance = floor($this->paid * ($refcodediscount->discount/100));
                $this->refcode_active_date = Carbon::today('Africa/Cairo')->format('Y-m-d');
            } else {
                $this->balance = 0;
                $this->refcode_active_date = null;
            }
        }
        if (!empty($this->inv_code)) {
            $addusedby = json_decode($this->getinvitecode->used_by);
            if (is_array($addusedby)) {
                array_push($addusedby, $this->getinviteuser->id);
            } else {
                $addusedby[] += $this->getinviteuser->id;
            }
            // dd($addusedby);

            $this->getinvitecode->update([
                'used_by' => json_encode($addusedby),
            ]);
        }
        $notifiedusers = User::whereIn('role', ['superadmin', 'admin', 'moderator'])->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'new student',
                'url' => 'student?q=' . $user->name,
                'message' => 'تم تسجيل طالب جديد "' . $user->name  . '".',
            ]);
        }

        Mail::raw( 'تم تسجيل طالب جديد "' . $user->name  . '".', function ($message) {
            $message->from('<EMAIL>', 'Abdullah Anees');
            $message->sender('<EMAIL>', 'Abdullah Anees');
            $message->to('<EMAIL>', 'Abdullah Anees');
            $message->subject('Test Subject');
            $message->priority(3);
        });

        Auth::login($user);

        $this->redirect(RouteServiceProvider::CLSS, navigate: true);
    }
    public function mount(){
        if(!empty($this->code))
        {
            $invregex = "/^[a-z]{6}\d{2,3}$/i";
            $disregex = "/^d[a-z]{5}f\d{2,3}$/i";
            if (preg_match($invregex, $this->code)) {
                $invcodestart = '';
                $userid = '';
                for ($i=0;$i<strlen($this->code);$i++) {
                    if (ctype_alpha($this->code[$i]))
                    {
                        $invcodestart .= $this->code[$i];
                    }elseif (ctype_digit($this->code[$i]))
                    {
                        $userid .= $this->code[$i];
                    }
                }
                // dd($invcodestart, $userid);
                $this->getinvitecode = ReferalCode::where('short_code', 'like', '%' . $invcodestart . '%')->first();
                $this->getinviteuser = User::find($userid);
                // dd($getinvitecode, $getinviteuser);
                if(!empty($this->getinvitecode) && !empty($this->getinviteuser))
                {
                    if($this->getinviteuser->role === 'student')
                    {
                        $this->inv_code = $this->code;
                        $this->retmessage = 'Valid Invitaition Code, Thank You.';
                    } else {
                        $this->inv_code = '';
                    }
                } else {
                    $this->inv_code = '';
                }
            }elseif(preg_match($disregex, $this->code))
            {
                $this->ref_code = $this->code;
                $this->getdiscountcode = ReferalCode::where('short_code', $this->ref_code)->first();
                $this->retmessage = 'Valid Referal Code, Thank You.';
            } else {
                $this->inv_code = '';
                $this->retmessage = 'Invalid Invitaition Code, Please Check and Try Again.';
            }
        }
    }
};
?>

<div>
    {{ $this->inv_code }}<br/>
    {{ $this->ref_code }}<br/>
    {{ $this->getdiscountcode->discount ?? '' }}%<br/>
    {{ $this->retmessage }}
    <form wire:submit="register">
        <div class="flex gap-5 justify-between">
            <div>
                <!-- Name -->
                <div>
                    <x-input-label for="name" :value="__('الإسم')" />
                    <x-text-input wire:model="name" id="name" class="block mt-1 w-full" type="text" name="name" required />
                    <x-input-error :messages="$errors->get('name')" class="mt-2" />
                </div>

                <!-- Username -->
                <div class="mt-4">
                    <x-input-label for="username" :value="__('اسم المستخدم')" />
                    <x-text-input wire:model="username" id="username" class="block mt-1 w-full" type="text" name="username" required />
                    <x-input-error :messages="$errors->get('username')" class="mt-2" />
                </div>

                <!-- Email Address -->
                <div class="mt-4">
                    <x-input-label for="email" :value="__('البريد الإلكتروني')" />
                    <x-text-input wire:model="email" id="email" class="block mt-1 w-full" type="email" name="email" required />
                    <x-input-error :messages="$errors->get('email')" class="mt-2" />
                </div>

                <!-- Password -->
                <div class="mt-4">
                    <x-input-label for="password" :value="__('الرمز السري')" />

                    <x-text-input wire:model="password" id="password" class="block mt-1 w-full"
                                    type="password"
                                    name="password"
                                    required autocomplete="new-password" />

                    <x-input-error :messages="$errors->get('password')" class="mt-2" />
                </div>

                <!-- Confirm Password -->
                <div class="mt-4">
                    <x-input-label for="password_confirmation" :value="__('تأكيد الرمز السري')" />

                    <x-text-input wire:model="password_confirmation" id="password_confirmation" class="block mt-1 w-full"
                                    type="password"
                                    name="password_confirmation" required />

                    <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
                </div>

            </div>
            <div>

                <!-- phone -->
                <div class="mt-4">
                    <x-input-label for="phone" :value="__('رقم الهاتف')" />
                    <x-text-input wire:model="phone" id="phone" class="block mt-1 w-full" type="text" name="phone" required />
                    <x-input-error :messages="$errors->get('phone')" class="mt-2" />
                </div>

                <!-- dateofbirth -->
                <div>
                    <x-input-label for="dateofbirth" :value="__('العمر')" />
                    <x-text-input wire:model="dateofbirth" id="dateofbirth" class="block mt-1 w-full" type="text" name="dateofbirth" required />
                    <x-input-error :messages="$errors->get('dateofbirth')" class="mt-2" />
                </div>

                <!-- dateofbirth -->
                <div class="mt-4">
                    <x-input-label for="gender_prefer" :value="__('ممعلم / معلمة')" />
                    <select wire:model.live="gender_prefer" id="gender_prefer" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 mt-1 h-12 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  selected>اختر ممايلي</option>
                        <option style="direction:rtl;" value="">أيهما متاح</option>
                        <option style="direction:rtl;" value="male">معلم</option>
                        <option style="direction:rtl;" value="female">معلمة</option>
                    </select>
                    <x-input-error :messages="$errors->get('dateofbirth')" class="mt-2" />
                </div>

                <!-- Referal Code -->
                <div class="mt-4">
                    <x-input-label for="ref_code" :value="__('كود الخصم')" />
                    @if (!empty($this->ref_code))
                        <x-text-input wire:model.live="ref_code" :disabled=true id="ref_code" class="block mt-1 w-full" type="text" name="ref_code" />

                    @else
                        <x-text-input wire:model.live="ref_code" :disabled=false id="ref_code" class="block mt-1 w-full" type="text" name="ref_code" />

                    @endif
                    <x-input-error :messages="$errors->get('ref_code')" class="mt-2" />
                </div>

            </div>
        </div>
        <div>

            <!-- country -->
            <div class="mt-4">
                <x-input-label for="country" :value="__('البلد')" />
                <select wire:model.live="country" id="country" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                    <option style="direction:rtl;"  selected>اختر البلد</option>
                    @foreach ( $this->listcountries as $key => $listcountry)
                        <option style="direction:rtl;" value="{{ $listcountry }}">{{ $listcountry }}</option>
                    @endforeach
                </select>
                <x-input-error :messages="$errors->get('country')" class="mt-2" />
            </div>

        </div>
        <div class="flex items-center justify-end mt-4">
            <a class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" href="{{ route('login') }}" wire:navigate>
                {{ __('مسجل بالفعل؟!') }}
            </a>

            <x-primary-button class="ms-4">
                {{ __('حساب جديد') }}
            </x-primary-button>
        </div>
    </form>
</div>
