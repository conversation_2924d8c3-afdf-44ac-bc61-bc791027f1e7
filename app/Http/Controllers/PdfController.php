<?php

// $html = view('print.certificate', ['data' => $data])->render();

// $pdf = Pdf::loadView('print.certificate', ['data' => $data]);
// $pdf->render();

// Option 1: Display PDF in browser
// return $pdf->stream();

// Option 2: Download PDF as a file

// $pdf = App::make('dompdf.wrapper');
// $pdf = PDF::loadView('print.certificate', ['data' => $data]);
// $pdf->loadHTML($html);
// $pdf->loadHTML('<h1>السلام عليكم</h1>');
// $pdf->loadView('print.certificate', ['data' => $data]);

// return $pdf->download('report.pdf');
// return $pdf->stream('certifi.pdf');

namespace App\Http\Controllers;

use App\Models\Certificate;
use App\Models\TeacherFile;
use App\Models\TeacherQualifications;
use App\Models\User;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\App;
use PDF;

class PdfController extends Controller
{
    public function downloadCertificate($id)
    {
        $cert = Certificate::find($id);
        $data = [
            'name' => $cert->student->name,
            'title' => $cert->title,
            'teacher' => $cert->teacher->name,
        ];

        $pdf = PDF::loadView('print-cert', ['data' => $data]);
        return $pdf->download('شهادة '. $cert->title . ' للطالب '. $cert->student->name . '.pdf');
    }
    public function showCertificate($id)
    {
        $cert = Certificate::find($id);
        $data = [
            'name' => $cert->student->name,
            'title' => $cert->title,
            'teacher' => $cert->teacher->name,
        ];

        $pdf = PDF::loadView('print-cert', ['data' => $data]);
        return $pdf->stream($cert->title . ' للطالب '. $cert->student->name . '.pdf');
    }

    public function downloadteacher($id)
    {
        $teacher = User::find($id);
        $qualis = [];
        $imagepath = $teacher->image;
        $teacherfiles = TeacherFile::where('user_id', $id)->get();
        foreach (json_decode($teacher->teacher->qualifications) as $quali) {
            $qualis[] = $this->qualiname($quali);
        }
        if (empty($imagepath))
        {
            $imagepath = 'assets/images/avatar/teacher.png';

        } else {
            $imagepath = 'storage/' . $imagepath;
        }
        $data = [
            'name' => $teacher->name,
            'country' => $teacher->country,
            'gender' => $teacher->teacher->gender,
            'qualifications' => $qualis,
            'upfiles' => TeacherFile::find($id),
            'image' => $imagepath,
            'upfiles' => $teacherfiles,
        ];


        $pdf = PDF::loadView('print-teacher', ['data' => $data]);
        return $pdf->download('شهادة '. $teacher->id . ' للطالب '. $teacher->name . '.pdf');
    }
    public function qualiname($quali)
    {
        return TeacherQualifications::find($quali)->name;
    }
    public function showteacehr($id)
    {
        $teacher = User::find($id);
        $qualis = [];
        $imagepath = $teacher->image;
        $teacherfiles = TeacherFile::where('user_id', $id)->get();
        foreach (json_decode($teacher->teacher->qualifications) as $quali) {
            $qualis[] = $this->qualiname($quali);
        }
        if (empty($imagepath))
        {
            $imagepath = 'assets/images/avatar/teacher.png';

        } else {
            $imagepath = 'storage/' . $imagepath;
        }
        $data = [
            'name' => $teacher->name,
            'country' => $teacher->country,
            'gender' => $teacher->teacher->gender,
            'qualifications' => $qualis,
            'upfiles' => TeacherFile::find($id),
            'image' => $imagepath,
            'upfiles' => $teacherfiles,
        ];

        $pdf = PDF::loadView('print-teacher', ['data' => $data]);
        return $pdf->stream($teacher->id . ' للطالب '. $teacher->name . '.pdf');
    }
}
