<?php

namespace App\Traits;

use Illuminate\Support\Facades\Http;

trait UseCurrency
{
    public function generateConversionRate($country)
    {
        $apikey = env('EXCHANGERATE_API_KEY');

        $url = 'https://v6.exchangerate-api.com/v6/' . $apikey . '/pair/SAR/' . $country;

        $response = Http::get($url);
        $responseData = $response->json();

        return $responseData;
    }
}
