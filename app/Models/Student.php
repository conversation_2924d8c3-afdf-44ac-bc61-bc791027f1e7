<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    use HasFactory;

    protected $table = 'students';

    protected $fillable = [
        'user_id',
        'dateofbirth',
        'gender_prefer',
        'subscription',
        'subscription_id',
        'paid',
        'bank_id',
        'numofclasses',
        'ref_code',
        'inv_code',
        'balance',
        'refcode_active_date',
        'startdate',
        'reg_status',
        'notes',
    ];
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function bank()
    {
        return $this->hasOne(Banks::class, 'id', 'bank_id', 'id');
    }

    public function clss()
    {
        return $this->belongsTo(Clss::class, 'teacher_id', 'id');
    }
    public function subscription()
    {
        return $this->belongsTo(Subscriptions::class, 'subscription_id', 'id');
    }
}
