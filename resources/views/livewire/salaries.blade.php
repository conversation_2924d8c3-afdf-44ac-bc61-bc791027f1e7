<div>
    {{-- data tables --}}
    <div class="card">
        <header class=" block card-header noborder">
          <h4 class="text-primary-500 font-bold text-3xl">التقارير الشهرية للرواتب</h4>
        </header>
        <div x-data="{open: false, shown: true }" x-defer class="card-body px-6 pb-6">
          <div class="sm:grid sm:my-2 md:flex sm:place-content-center md:justify-between w-auto mr-0">
            {{-- control buttons --}}
            <div class="grid sm:grid-cols-1 lg:grid-cols-6 gap-3">
                {{-- advanced section modal button --}}
                <button type="button" x-on:click="open = ! open" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                    <div x-show="! open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اظهار البحث الــمتقدم
                    </div>
                    <div x-show="open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اخفاء البحث الــمتقدم
                    </div>
                </button>
                @if (!empty($filterdatestart) || !empty($filterdateend) || !empty($filterteacher) || !empty($filteractive) || !empty($filtercountry))
                    @if (auth()->user()->role === 'superadmin' || auth()->user()->role === 'accountant')
                    <button wire:click.prevent="resetallfilter" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div class="flex place-items-center">
                            <iconify-icon icon="heroicons:arrow-uturn-down-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                            إعادة تعيين التصفية
                        </div>
                    </button>
                    @endif
                @endif
            </div>
            {{-- search field --}}
            <div class="my-3 sm:w-auto sm:mt-0 sm:ml-auto md:ml-0">
                <div class="relative flex w-80 text-gray-700 mr-auto my-auto place-items-center">
                    @if (!empty($search))
                        <button type="button" wire:click.prevent="resetsearch" class="absolute inset-y-0 left-0 pl-3 flex items-center z-1">
                            <iconify-icon icon="heroicons:x-mark-20-solid" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                        </button>
                    @endif
                    <iconify-icon icon="heroicons:magnifying-glass-solid" style="font-size: 24px;color:#016241;" alt="تعديل" class="ml-2"></iconify-icon>
                    <input wire:model.live.debounce.500="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-lg rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="بحث..." wire:ignore>
                </div>
            </div>
          </div>
          {{-- advanced filter by status and teacher --}}
          <div x-show="open" class="grid sm:grid-cols-1 lg:grid-cols-3 place-items-center gap-2 my-2">
            @if (auth()->user()->role === 'superadmin' || auth()->user()->role === 'accountant')
                <div class="flex w-full place-items-center">
                <label for="filterteacher" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">البائع: </label>
                <select name="filterteacher" wire:model.live="filterteacher" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option style="direction:rtl;" value="" selected>اختر المعلم</option>
                    @foreach ( $theallteachers as $teacher)
                        <option style="direction:rtl;" value="{{ $teacher->id }}">{{ $teacher->name }}</option>
                    @endforeach
                </select>
                @if (!empty($filterteacher))
                <button type="button" wire:click.prevent="resetfilter('filterteacher')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                    <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                </button>
                @endif
                </div>
            @endif
            <div class="flex w-full place-items-center">
            <label for="filtermonth" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">شهر: </label>
            <select name="filtermonth" wire:model.live="filtermonth" style="direction:ltr;" class=" w-full shadow-sm rounded-md px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;" value="0" selected>اختر الشهر</option>
                    @foreach ( $listmonthes as $key => $month)
                        <option style="direction:rtl;" value="{{ $key }}">{{ $month }}</option>
                    @endforeach
            </select>
            @if (!empty($filtermonth))
            <button type="button" wire:click.prevent="resetfilter('filtermonth')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
            </button>
            @endif
            </div>
          </div>
          {{-- data table --}}
          <div class="overflow-x-auto -mx-6">
            <div class="inline-block min-w-full align-middle">
              <div class="overflow-hidden">
                <div class="text-lg font-medium text-white">{{ $filterdatestart }}, {{ $filterdateend }}</div>
                @unless ($allteachers->count() == 0)
                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl">
                  <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                    <tr>
                        <th scope="col" class=" table-th text-xl font-medium">اسم المعلم</th>
                        <th scope="col" class=" table-th text-xl font-medium">شهر</th>
                        <th scope="col" class=" table-th text-xl font-medium">عدد الساعات</th>
                        <th scope="col" class=" table-th text-xl font-medium">حساب الساعة</th>
                        <th scope="col" class=" table-th text-xl font-medium">راتب شهر</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                    @foreach ($allteachers as $teacher)
                    <tr wire:key="salary-{{ $teacher->id }}">
                      @if (auth()->user()->role !== 'student')
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $teacher->name }}</span>
                      </td>
                      @endif
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $filterdatestart->monthName ?? now()->monthName }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $this->gettotalhours($teacher) }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $teacher->teacher->rateperclass ?? 0 }}</span>
                      </td>
                      @if (auth()->user()->role !== 'teacher')
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $this->gettotalsalary($teacher) }}</span>
                      </td>
                      @endif
                    </tr>
                    @endforeach
                  </tbody>
                </table>
                @else
                <div class="text-center text-xl font-bold text-[#016241]">لا يوجد تقارير حتى الآن.</div>
                @endunless
              </div>
            </div>
          </div>
        </div>
    </div>
    <div class="flex my-4">
        <div class="flex justify-start w-[50%] items-center">
            <label for="perpage" class="text-lg font-medium text-gray-700 dark:text-[#be9539]">عدد النتائج في كل صفحة:</label>
            <select wire:model.live="perpage" id="role" style="direction:ltr;" class="w-20 shadow-sm rounded-md mx-4 px-5 py-1 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option style="direction:rtl;" value="10">10</option>
                <option style="direction:rtl;" value="20">20</option>
                <option style="direction:rtl;" value="30">30</option>
                <option style="direction:rtl;" value="50">50</option>
                <option style="direction:rtl;" value="2000">الكل</option>
            </select>
        </div>
        <div class="w-[100%] mr-4">{{ $allteachers->links('vendor.livewire.tailwind') }}</div>
    </div>
</div>
