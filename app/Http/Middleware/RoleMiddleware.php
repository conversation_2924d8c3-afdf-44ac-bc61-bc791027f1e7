<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (! in_array(Auth::user()->role, $roles)) {
            if (Auth::User()->role == 'admin' || Auth::User()->role == 'accountant' ) {
                return redirect(RouteServiceProvider::HOME);
            } else {
                return redirect(RouteServiceProvider::CLSS);
            }
        }
        return $next($request);
    }
}
