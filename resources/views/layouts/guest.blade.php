<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl" class="dark">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}" dir="rtl" class="dark">
        <meta name="description" content="">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- BEGIN: Theme CSS-->
        <link rel="stylesheet" href={{ asset("assets/css/rt-plugins.css") }}>
        <link rel="stylesheet" href={{ asset("assets/css/app.css") }}>
        <!-- End : Theme CSS -->

        <!-- Start : theme-store js -->
        <script src={{ asset("assets/js/store.js") }} sync></script>
        <!-- End : theme-store js -->

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap');

            *{
                font-family: "Cairo", sans-serif;
            }
            .loginbg{
                background-image: url({{ asset('assets/images/shafe3-bg.jpg') }});
                background-position: center;
                background-repeat: no-repeat;
                background-size: cover;
            }
        </style>

        <!-- daisyUI CDN -->
        <link href="https://cdn.jsdelivr.net/npm/daisyui@4.7.2/dist/full.min.css" rel="stylesheet" type="text/css" />
        <script src="https://cdn.tailwindcss.com"></script>

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans text-gray-900 antialiased">
        <div class="min-h-screen flex flex-col sm:justify-center items-start pr-[20%] pt-6 sm:pt-0 bg-gray-100 loginbg">
            <div class="pr-20">
                <a href="/" wire:navigate>
                    <x-application-logo class="w-20 h-20 fill-current text-gray-500" />
                </a>
            </div>

            <div class="w-full sm:max-w-md mt-6 px-6 py-4 bg-white shadow-md overflow-hidden sm:rounded-lg">
                {{ $slot }}
            </div>
        </div>
    </body>
</html>
