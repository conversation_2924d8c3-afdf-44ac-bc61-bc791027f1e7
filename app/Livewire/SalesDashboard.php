<?php

namespace App\Livewire;

use App\Models\Banks;
use App\Models\Clss;
use App\Models\Expenses;
use App\Models\ExpensesCategory;
use App\Models\SalesReport;
use App\Models\Settings;
use App\Models\User;
use App\Traits\UseDefault;
use Carbon\Carbon;
use Livewire\Component;

class SalesDashboard extends Component
{
    use UseDefault;

    public $settings;

    public $endedclasses = '';
    public $maleteachers = '';
    public $rolesnames = [];
    public $rolesorder = [];
    public $popteacher = [];
    public $numteacherclass = [];
    public $popcountries = [];
    public $numcountriescount = [];

    //teachers reports
    public $allteachers = 0;
    public $allusers = 0;
    public $allclasses = 0;

    // students reports
    public $allstudents = 0;
    public $fixstudents = 0;
    public $tristudents = 0;
    public $endstudents = 0;

    // budget reports
    public $currbudget = 0;
    public $prevmonthbudget = 0;
    public $monthbeforebudget = 0;

    // sales reports
    public $currsales = 0;
    public $prevmonthsales = 0;
    public $monthbeforesales = 0;
    public $currsalesnewstudents = 0;
    public $currsalesresubstudents = 0;

    // expenses reports
    public $expcats = [];
    public $currexpenses = 0;
    public $prevmonthexpenses = 0;
    public $monthbeforeexpenses = 0;
    public $safe;

    // marketing reports
    public $currmarketing = 0;
    public $prevmonthmarketing = 0;
    public $monthbeforemarketing = 0;

    // slary reports
    public $currsalary = 0;
    public $prevmonthsalary = 0;
    public $monthbeforesalary = 0;

    // sales report 6-month chart
    public $currentmonthsales;
    public $currentmonthexpenses;
    public $lastmonthsales;
    public $lastmonthexpenses;
    public $lastcyclesales = [];
    public $lastcycleexpenses = [];
    public $reportcycle = '6-month';
    public $currmonths = [];

    public function startmonth($num = null)
    {
        return Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->shortMonthName;
    }

    public function starttoday()
    {
        return Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'));
    }

    public function submonthtodaysales($num = null)
    {
        $start = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->startOfMonth();
        $end = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->endOfMonth();
        return SalesReport::filterdate($start, $end)->sum('paid');
    }
    public function submonthtodayexpenses($num = null)
    {
        $start = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->startOfMonth();
        $end = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->endOfMonth();
        return $this->getsum($start, $end);
    }
    public function submonthtodaysalesnew($num = null)
    {
        $start = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->startOfMonth();
        $end = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->endOfMonth();
        return SalesReport::filterdate($start, $end)->filterregstatus('new')->sum('paid');
    }
    public function submonthtodaysalesresub($num = null)
    {
        $start = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->startOfMonth();
        $end = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->endOfMonth();
        return SalesReport::filterdate($start, $end)->filterregstatus('resub')->sum('paid');
    }
    public function gettotalhours($teacher, $start, $end)
    {
        $totalclasses = 0;
        $classes = Clss::filterdate($start, $end)
        ->with('clssreport')
        ->filterteacher($teacher->id)->get();
        foreach ($classes as $class) {
            $totalclasses += $class->clssreport->where('loggedin_user_role', 'teacher')->first()->loggedinduration ?? 0;
        }
        // dd($totalclasses);
        return $totalclasses;
    }
    public function submonthtodaysalary($num = null)
    {
        $thewholetotalclasses = 0;
        $start = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->startOfMonth();
        $end = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->endOfMonth();
        $teachers = User::filterrole('teacher')->get();
        foreach ($teachers as $teacher) {
            $rateperclass = $teacher->teacher->rateperclass ?? 0;
            $thewholetotalclasses += ($this->gettotalhours($teacher,$start, $end)/60) * $rateperclass;
        }
        // dd($thewholetotalclasses);
        return $thewholetotalclasses;
    }

    public function submonthtodaymarketing($num = null)
    {
        $start = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->startOfMonth();
        $end = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->endOfMonth();
        return Expenses::filterdate($start, $end)->filterexpcate(3)->sum('amount');
    }
    public function submonthtodayexpcat($expcat, $num = null)
    {
        $start = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->startOfMonth();
        $end = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->endOfMonth();
        $totalbankwithdraw = 0;
        if($expcat == 10)
        {
            // $expenses = [];
            $banks = Banks::all();
            foreach ($banks as $bank) {
                $totalbankwithdraw += (Expenses::filterdate($start, $end)->filterexpcate(10)->filterexpsubcate($bank->id)->sum('amount') / $this->convertPaidToSAR($bank->country)) * $this->settings->egyptrate;
            }

            return $totalbankwithdraw;
        }
        return Expenses::filterdate($start, $end)->filterexpcate($expcat)->sum('amount');
    }

    public function submonthtodaynobankexpcat($expcat, $num = null)
    {
        $start = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->startOfMonth();
        $end = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->endOfMonth();
        return Expenses::filterdate($start, $end)->filterexpcate($expcat)->filternotexpcate(10)->sum('amount');
    }
    public function calculatetwomonthessales()
    {
        $currstart = $this->starttoday()->startOfMonth();
        $currend = $this->starttoday()->endOfMonth();
        $this->currentmonthsales = SalesReport::filterdate($currstart, $currend)->sum('paid');
        $this->lastmonthsales = $this->submonthtodaysales(1);
    }
    public function calculatetwomonthesexpenses()
    {
        $currstart = $this->starttoday()->startOfMonth();
        $currend = $this->starttoday()->endOfMonth();
        $this->currentmonthexpenses = $this->getsum($currstart, $currend);
        $this->lastmonthexpenses = $this->submonthtodayexpenses(1);
    }

    public function calculatesixmonthesreport()
    {
        $this->currmonths = [
            $this->startmonth(5),
            $this->startmonth(4),
            $this->startmonth(3),
            $this->startmonth(2),
            $this->startmonth(1),
            $this->starttoday()->shortMonthName,
        ];
        $this->lastcyclesales = [
            $this->submonthtodaysales(5),
            $this->submonthtodaysales(4),
            $this->submonthtodaysales(3),
            $this->submonthtodaysales(2),
            $this->submonthtodaysales(1),
            $this->currentmonthsales,
        ];
        $this->lastcycleexpenses = [
            $this->submonthtodayexpenses(5),
            $this->submonthtodayexpenses(4),
            $this->submonthtodayexpenses(3),
            $this->submonthtodayexpenses(2),
            $this->submonthtodayexpenses(1),
            $this->currentmonthexpenses,
        ];
    }

    public function calculateyearreport()
    {
        $this->currmonths = [
            $this->startmonth(11),
            $this->startmonth(10),
            $this->startmonth(9),
            $this->startmonth(8),
            $this->startmonth(7),
            $this->startmonth(6),
            $this->startmonth(5),
            $this->startmonth(4),
            $this->startmonth(3),
            $this->startmonth(2),
            $this->startmonth(1),
            $this->starttoday()->shortMonthName,
        ];
        $this->lastcyclesales = [
            $this->submonthtodaysales(11),
            $this->submonthtodaysales(10),
            $this->submonthtodaysales(9),
            $this->submonthtodaysales(8),
            $this->submonthtodaysales(7),
            $this->submonthtodaysales(6),
            $this->submonthtodaysales(5),
            $this->submonthtodaysales(4),
            $this->submonthtodaysales(3),
            $this->submonthtodaysales(2),
            $this->submonthtodaysales(1),
            $this->currentmonthsales,
        ];
        $this->lastcycleexpenses = [
            $this->submonthtodayexpenses(11),
            $this->submonthtodayexpenses(10),
            $this->submonthtodayexpenses(9),
            $this->submonthtodayexpenses(8),
            $this->submonthtodayexpenses(7),
            $this->submonthtodayexpenses(6),
            $this->submonthtodayexpenses(5),
            $this->submonthtodayexpenses(4),
            $this->submonthtodayexpenses(3),
            $this->submonthtodayexpenses(2),
            $this->submonthtodayexpenses(1),
            $this->currentmonthexpenses,
        ];
    }

    public function budgetmonthtoday($num = null)
    {
        $start = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->startOfMonth();
        $end = Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->subMonth($num)->endOfMonth();
        $thesales = SalesReport::filterdate($start, $end)->sum('paid');
        $theexpenses = Expenses::filterdate($start, $end)->sum('amount');
        return ($thesales * $this->settings->egyptrate) - $theexpenses;
    }

    public function rolesordercalc()
    {
        $toproles = [];
        $toprolescount = 1;
        $roles = User::pluck('role');
        foreach($roles as $role)
        {
            if(isset($toproles[$role]))
            {
                $toproles[$role] += $toprolescount;
            } else {
                $toproles[$role] = $toprolescount;
            }
        }
        arsort($toproles);
        return $toproles;
    }
    public function teacherclassescalc()
    {
        $topteacherclasses = []; //teachers id
        $realteacherclasses = []; //teachers names
        $popteacherclasses = []; //filter top teachers to 5
        $topteacherclassescount = 1;
        $teachers = Clss::pluck('teacher_id');
        foreach($teachers as $teacher)
        {
            if(isset($topteacherclasses[$teacher]))
            {
                $topteacherclasses[$teacher] += $topteacherclassescount;
            } else {
                $topteacherclasses[$teacher] = $topteacherclassescount;
            }
        }
        arsort($topteacherclasses);
        foreach ($topteacherclasses as $teacher => $numclasses) {
            $realteacherclasses[User::find($teacher)->name] = $numclasses;
        }
        $popteacherclasses = array_slice($realteacherclasses, 0, 5);
        foreach ($popteacherclasses as $key => $value) {
            $this->popteacher[] = $key;
            $this->numteacherclass[] = $value;
        }
    }
    public function studentcountriescalc()
    {
        $topstudentcountries = []; //countries id
        $realstudentcountries = []; //countries names
        $popstudentcountries = []; //filter top countries to 5
        $topstudentcountriescount = 1;
        $countries = User::filterrole('student')->pluck('country');
        foreach($countries as $country)
        {
            if(isset($topstudentcountries[$country]))
            {
                $topstudentcountries[$country] += $topstudentcountriescount;
            } else {
                $topstudentcountries[$country] = $topstudentcountriescount;
            }
        }
        arsort($topstudentcountries);
        foreach ($topstudentcountries as $country => $numclasses) {
            $realstudentcountries[$country] = $numclasses;
        }
        $popstudentcountries = array_slice($realstudentcountries, 0, 5);
        foreach ($popstudentcountries as $key => $value) {
            $this->popcountries[] = $key;
            $this->numcountriescount[] = $value;
        }
    }
    public function getsum($start, $end)
    {
        $totalexpense = Expenses::filternotexpcate(1)
            ->filterdate($start, $end)
            ->sum('amount'); // $this->settings->egyptrate; // for converting to SAR
            // exclude bank withdraws in expenses reports
        // $banks = Banks::all();
        // foreach ($banks as $bank) {
        //     $banksexpensesinsar = Expenses::filterexpcate(10)
        //     ->filterexpsubcate($bank->id)
        //     ->filterdate($start, $end)
        //     ->sum('amount') / $this->convertPaidToSAR($bank->country);
        //     $totalexpense += $banksexpensesinsar;
        // }
        // dd($totalexpense);
        return round($totalexpense);
    }
    public function mount()
    {
        $this->settings = Settings::find(1);
        $this->safe = $this->settings->safe;
        $this->allteachers = User::filterrole('teacher')->count();
        $this->allusers = User::all()->count();
        $this->allclasses = Clss::all()->count();
        $this->expcats = ExpensesCategory::all();
        $this->endedclasses = Clss::filteractive('ended')->count();
        // $this->maleteachers = User::filterrole('teacher')->filtergender('male')->count() ?? 0;
        // $this->fixstudents = User::filterrole('student')->filtersubscription('fixed')->count() ?? 0;
        // $this->tristudents = User::filterrole('student')->filtersubscription('trial')->count() ?? 0;
        // $this->endstudents = User::filterrole('student')->filtersubscription('suspended')->count() ?? 0;
        $this->allstudents = $this->fixstudents + $this->tristudents + $this->endstudents;
        $this->currbudget = $this->budgetmonthtoday();
        $this->prevmonthbudget = $this->budgetmonthtoday(1);
        $this->monthbeforebudget = $this->budgetmonthtoday(2);
        $this->currsales = $this->submonthtodaysales();
        $this->currsalesnewstudents = $this->submonthtodaysalesnew();
        $this->currsalesresubstudents = $this->submonthtodaysalesresub();
        $this->prevmonthsales = $this->submonthtodaysales(1);
        $this->monthbeforesales = $this->submonthtodaysales(2);
        $this->currexpenses = $this->submonthtodayexpenses();
        $this->prevmonthexpenses = $this->submonthtodayexpenses(1);
        $this->monthbeforeexpenses = $this->submonthtodayexpenses(2);
        $this->currmarketing = $this->submonthtodaymarketing();
        $this->prevmonthmarketing = $this->submonthtodaymarketing(1);
        $this->monthbeforemarketing = $this->submonthtodaymarketing(2);
        $this->currsalary = $this->submonthtodaysalary();
        $this->prevmonthsalary = $this->submonthtodaysalary(1);
        $this->monthbeforesalary = $this->submonthtodaysalary(2);
    }

    public function render()
    {
        $this->calculatetwomonthessales();
        $this->calculatetwomonthesexpenses();
        $this->studentcountriescalc();
        if($this->reportcycle == 'year')
        {
            $this->calculateyearreport();
        } else {
            $this->calculatesixmonthesreport();
        }

        return view('livewire.sales-dashboard');
    }
}
