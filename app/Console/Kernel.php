<?php

namespace App\Console;

use App\Mail\ClassNotification;
use App\Models\Clss;
use Carbon\Carbon;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Mail;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        $schedule->call(function(){
            $classes = Clss::where('date_time', '=', Carbon::now('Africa/Cairo')->addMinutes(30)->format('Y-m-d H:i:00'))->get();

            foreach ($classes as $class) {
                $teacher = $class->teacher;
                $student = $class->student;

                Mail::to($teacher->email, $teacher->name)->send(new ClassNotification($class));
                Mail::to($student->email, $student->name)->send(new ClassNotification($class));
            }
        })->everyMinute();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
