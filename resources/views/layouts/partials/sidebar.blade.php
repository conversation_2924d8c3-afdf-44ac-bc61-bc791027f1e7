<div class="sidebar-wrapper group hidden xl:w-[248px] xl:block">
    <div wire:ignore id="bodyOverlay" class="w-screen h-screen fixed top-0 bg-slate-800 bg-opacity-50 backdrop-blur-sm hidden"></div>
    <div class="logo-segment">
        <div>
            <a href="/" wire:navigate>
                <x-application-logo class="w-20 h-20 fill-current text-gray-500" />
            </a>
        </div>
    <!-- Sidebar Type Button -->
    <div wire:ignore id="sidebar_type" class="cursor-pointer text-slate-900 dark:text-white text-lg">
        <iconify-icon class="sidebarDotIcon extend-icon text-slate-900 dark:text-slate-200" icon="fa-regular:dot-circle"></iconify-icon>
        <iconify-icon class="sidebarDotIcon collapsed-icon text-slate-900 dark:text-slate-200" icon="material-symbols:circle-outline"></iconify-icon>
    </div>
    <button class="sidebarCloseIcon text-2xl inline-block md:hidden">
        <iconify-icon class="text-slate-900 dark:text-slate-200" icon="clarity:window-close-line"></iconify-icon>
    </button>
    </div>
    <div wire:ignore id="nav_shadow" class="nav_shadow h-[60px] absolute top-[80px] nav-shadow z-[1] w-full transition-all duration-200 pointer-events-none opacity-0"></div>
    <div wire:ignore class="sidebar-menus bg-white dark:bg-slate-800 py-2 px-4 h-[calc(100%-80px)] z-50" id="sidebar_menus">
    <ul class="sidebar-menu">

        @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin' || auth()->user()->role == 'account')
        <li class="">
            <a href="/" wire:navigate target="_self" class="navItem {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                <span class="flex items-center text-xl">
                    <iconify-icon class=" nav-icon" icon="heroicons-outline:home" style="font-size:36px;"></iconify-icon>
                    <span>الصفحة الرئيسية</span>
                </span>
            </a>
        </li>
        @endif

        <li class="">
        <a href="class" wire:navigate target="_self" class="navItem {{ request()->routeIs('class') ? 'active' : '' }}">
            <span class="flex items-center text-xl">
        <iconify-icon class=" nav-icon" icon="heroicons:tv-solid" style="font-size:36px;"></iconify-icon>
        <span>إدارة الحلقات</span>
            </span>
        </a>
        </li>

        @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin' || auth()->user()->role == 'moderator' || auth()->user()->role == 'accountant' || auth()->user()->role == 'sales')
        <li class="manage-sec {{ request()->routeIs('teacher') || request()->routeIs('student') || request()->routeIs('user') || request()->routeIs('qualification') || request()->routeIs('courses') || request()->routeIs('lectures') || request()->routeIs('subscription') || request()->routeIs('zoomapps') ? 'active' : '' }}">
            <a href="javascript:void(0);" class="navItem" >
                <span class="flex items-center text-xl">
                <iconify-icon class=" nav-icon" icon="heroicons:user-group-solid" style="font-size:36px;"></iconify-icon>
                <span>قسم الإدارة</span>
                </span>
                <iconify-icon class="icon-arrow" icon="heroicons-outline:{{ request()->routeIs('teacher') || request()->routeIs('student') || request()->routeIs('user') || request()->routeIs('qualification') || request()->routeIs('courses') || request()->routeIs('lectures') || request()->routeIs('subscription') || request()->routeIs('zoomapps') ? 'chevron-down' : 'chevron-right'}}" style="font-size:36px;"></iconify-icon>
            </a>
            <ul class="sidebar-submenu {{ request()->routeIs('teacher') || request()->routeIs('student') || request()->routeIs('user') || request()->routeIs('qualification') || request()->routeIs('courses') || request()->routeIs('lectures') || request()->routeIs('subscription') || request()->routeIs('zoomapps') ? 'menu-open' : '' }}">
                @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin' || auth()->user()->role == 'accountant' || auth()->user()->role == 'moderator' || auth()->user()->role == 'sales')
                <li>
                    <a href="student" wire:navigate target="_self" class="{{ request()->routeIs('student') ? 'active' : '' }} text-xl">إدارة الطلاب</a>
                </li>
                @endif
                @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin' || auth()->user()->role == 'accountant' || auth()->user()->role == 'moderator')
                <li>
                    <a href="teacher" wire:navigate target="_self" class="{{ request()->routeIs('teacher') ? 'active' : '' }} text-xl">إدارة المعلمين</a>
                </li>
                @endif
                @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin')
                <li>
                    <a href="user" wire:navigate target="_self" class="{{ request()->routeIs('user') ? 'active' : '' }} text-xl">إدارة الأعضاء</a>
                </li>
                @endif
                @if (auth()->user()->role == 'superadmin' || auth()->user()->role == 'admin')
                <li>
                    <a href="qualification" wire:navigate target="_self" class="{{ request()->routeIs('qualification') ? 'active' : '' }} text-xl">مهارات المعلمين</a>
                </li>
                @endif
                @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin' || auth()->user()->role == 'teacher' || auth()->user()->role == 'student')
                <li>
                    <a href="courses" wire:navigate target="_self" class="{{ request()->routeIs('courses') ? 'active' : '' }} text-xl">إدارة الكورسات</a>
                </li>
                @endif
                @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin' || auth()->user()->role == 'teacher' || auth()->user()->role == 'student')
                <li>
                    <a href="lectures" wire:navigate target="_self" class="{{ request()->routeIs('lectures') ? 'active' : '' }} text-xl">إدارة المحاضرات</a>
                </li>
                @endif
                @if (auth()->user()->role == 'superadmin' || auth()->user()->role == 'admin')
                <li>
                    <a href="subscription" wire:navigate target="_self" class="{{ request()->routeIs('subscription') ? 'active' : '' }} text-xl">إدارة الإشتراكات</a>
                </li>
                @endif
                @if (auth()->user()->role == 'superadmin' || auth()->user()->role == 'admin')
                <li>
                    <a href="zoomapps" wire:navigate target="_self" class="{{ request()->routeIs('zoomapps') ? 'active' : '' }} text-xl">إدارة تطبيقات زوم</a>
                </li>
                @endif
            </ul>
        </li>
        @endif

        @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin' || auth()->user()->role == 'teacher' || auth()->user()->role == 'accountant')
        <li class="manage-sec {{ request()->routeIs('finance') || request()->routeIs('supervisor') || request()->routeIs('classreport') || request()->routeIs('ratings') || request()->routeIs('certificates') || request()->routeIs('achievements')  ? 'active' : '' }}">
            <a href="javascript:void(0);" class="navItem" >
                <span class="flex items-center text-xl">
                <iconify-icon class=" nav-icon" icon="heroicons:currency-pound-16-solid" style="font-size:36px;"></iconify-icon>
                <span>قسم التقارير</span>
                </span>
                <iconify-icon class="icon-arrow" icon="heroicons-outline:{{ request()->routeIs('finance') || request()->routeIs('supervisor') || request()->routeIs('classreport') || request()->routeIs('ratings') || request()->routeIs('certificates') || request()->routeIs('achievements')  ? 'chevron-down' : 'chevron-right'}}" style="font-size:36px;"></iconify-icon>
            </a>
            <ul class="sidebar-submenu {{ request()->routeIs('finance') || request()->routeIs('supervisor') || request()->routeIs('classreport') || request()->routeIs('ratings') || request()->routeIs('certificates') || request()->routeIs('achievements') ? 'menu-open' : '' }}">
                @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin' || auth()->user()->role == 'accountant' || auth()->user()->role == 'teacher')
                <li>
                    <a href="finance" wire:navigate target="_self" class="{{ request()->routeIs('finance') ? 'active' : '' }}">تقارير حضور الحلقات</a>
                </li>
                @endif
                @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin')
                <li>
                    <a href="classreport" wire:navigate target="_self" class="{{ request()->routeIs('classreport') ? 'active' : '' }}">تقارير الحلقات الفنية </a>
                </li>
                @endif
                @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin')
                <li>
                    <a href="supervisor" wire:navigate target="_self" class="{{ request()->routeIs('supervisor') ? 'active' : '' }}">تقارير حضور الإشراف </a>
                </li>
                @endif
                @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin')
                <li>
                    <a href="ratings" wire:navigate target="_self" class="{{ request()->routeIs('ratings') ? 'active' : '' }}">تقارير تقييمات المعلمين </a>
                </li>
                @endif
                @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin' || auth()->user()->role == 'teacher')
                <li>
                    <a href="certificates" wire:navigate target="_self" class="{{ request()->routeIs('certificates') ? 'active' : '' }}">تقارير شهادات التقدير </a>
                </li>
                @endif
                @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin')
                <li>
                    <a href="achievements" wire:navigate target="_self" class="{{ request()->routeIs('achievements') ? 'active' : '' }}">تقارير إنجازات الطالب </a>
                </li>
                @endif
            </ul>
        </li>
        @endif

        @if (auth()->user()->role == 'superadmin' || auth()->user()->role == 'accountant' || auth()->user()->role == 'sales')
        <li class="manage-sec {{ request()->routeIs('expenses') || request()->routeIs('expcategories') || request()->routeIs('expsubcategories') || request()->routeIs('sales') ? 'active' : '' }}">
            <a href="javascript:void(0);" class="navItem" >
                <span class="flex items-center text-xl">
                <iconify-icon class=" nav-icon" icon="heroicons:document-chart-bar-20-solid" style="font-size:36px;"></iconify-icon>
                <span>قسم المالية</span>
                </span>
                <iconify-icon class="icon-arrow" icon="heroicons-outline:{{ request()->routeIs('expenses') || request()->routeIs('expcategories') || request()->routeIs('expsubcategories') || request()->routeIs('sales') || request()->routeIs('refcode') || request()->routeIs('banks') ? 'chevron-down' : 'chevron-right'}}" style="font-size:36px;"></iconify-icon>
            </a>
            <ul class="sidebar-submenu {{ request()->routeIs('expenses') || request()->routeIs('expcategories') || request()->routeIs('expsubcategories') || request()->routeIs('sales') || request()->routeIs('refcode') || request()->routeIs('banks') ? 'menu-open' : '' }}">
                @if (auth()->user()->role == 'superadmin' || auth()->user()->role == 'accountant')
                <li>
                    <a href="salesdashboard" wire:navigate target="_self" class="{{ request()->routeIs('salesdashboard') ? 'active' : '' }}">الصفحة الرئيسية</a>
                </li>
                @endif
                @if (auth()->user()->role == 'superadmin' || auth()->user()->role == 'accountant' || auth()->user()->role == 'sales')
                <li>
                    <a href="sales" wire:navigate target="_self" class="{{ request()->routeIs('sales') ? 'active' : '' }}">تقارير الايرادات </a>
                </li>
                @endif
                @if (auth()->user()->role == 'superadmin' || auth()->user()->role == 'accountant')
                <li>
                    <a href="expenses" wire:navigate target="_self" class="{{ request()->routeIs('expenses') ? 'active' : '' }}">سندات الصرف</a>
                </li>
                @endif
                @if (auth()->user()->role == 'superadmin' || auth()->user()->role == 'accountant')
                <li>
                    <a href="salaries" wire:navigate target="_self" class="{{ request()->routeIs('salaries') ? 'active' : '' }}">تقارير الرواتب الشهرية</a>
                </li>
                @endif
                @if (auth()->user()->role == 'superadmin')
                <li>
                    <a href="expcategories" wire:navigate target="_self" class="{{ request()->routeIs('expcategories') ? 'active' : '' }}">القوائم الرئيسية</a>
                </li>
                @endif
                @if (auth()->user()->role == 'superadmin')
                <li>
                    <a href="expsubcategories" wire:navigate target="_self" class="{{ request()->routeIs('expsubcategories') ? 'active' : '' }}">البنود الفرعية</a>
                </li>
                @endif
                @if (auth()->user()->role == 'superadmin')
                <li>
                    <a href="refcode" wire:navigate target="_self" class="{{ request()->routeIs('refcode') ? 'active' : '' }}">اكواد الخصم</a>
                </li>
                @endif
                @if (auth()->user()->role == 'superadmin')
                <li>
                    <a href="banks" wire:navigate target="_self" class="{{ request()->routeIs('banks') ? 'active' : '' }}">حسابات بنكية</a>
                </li>
                @endif
            </ul>
        </li>
        @endif

        @if (auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin')
        <li class="">
            <a href="setting" wire:navigate target="_self" class="navItem {{ request()->routeIs('setting') ? 'active' : '' }}">
                <span class="flex items-center text-xl">
                    <iconify-icon class=" nav-icon" icon="heroicons:cog-8-tooth-16-solid" style="font-size:36px;"></iconify-icon>
                    <span>الإعدادات</span>
                </span>
            </a>
        </li>
        @endif
    </ul>

    </div>
</div>
