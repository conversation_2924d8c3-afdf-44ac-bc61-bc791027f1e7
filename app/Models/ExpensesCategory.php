<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExpensesCategory extends Model
{
    use HasFactory;

    protected $table = 'expenses_categories';

    protected $fillable = [
        'name',
        'notes',
    ];

    public function subCategory()
    {
        return $this->hasMany(ExpensesSubcategory::class, 'category_id', 'id');
    }
    public function expenses()
    {
        return $this->hasone(Expenses::class, 'subcategory_id', 'id');
    }

}
