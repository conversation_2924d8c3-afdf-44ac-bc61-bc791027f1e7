<div>
    <!-- flash update successful message -->
    <x-flash-message on="flashupdated">
        <div class="flex items-center bg-yellow-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تعديل العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    <div class="card-body flex flex-col p-6">
        <header class="flex mb-5 items-center border-b border-slate-100 dark:border-slate-700 pb-5 -mx-6 px-6">
          <div class="flex-1">
            <div class=" text-[#016241] font-cairo text-2xl font-bold">اهلا بك يا  "{{ auth()->user()->name }}" في صفحة ملفك الشخصي.</div>
          </div>
        </header>
        <form action="add-user">
            <div class="mb-4">
                <label for="image" class="block text-sm font-medium text-gray-700 dark:text-[#016241] mb-2">صورة الملف الشخصي:</label>
                @if (empty($oldimage))
                    <div class="block w-52 h-52">
                        <img src="{{ asset('assets/images/avatar/'). '/' .  auth()->user()->role }}.png" alt="user" class="block border-2 object-fill aspect-square overflow-hidden border-gray-700 rounded-full bg-gray-900" width="208" height="208" />
                    </div>
                @else
                    @if ($image)
                    <div class="block w-52 h-52">
                        <image src="{{ $image->temporaryUrl() }}" class="rounded-full block border-2 object-fill aspect-square overflow-hidden border-gray-700" width="208" height="208" />
                    </div>
                    @else
                    <div class="block w-52 h-52">
                        <image src="storage/{{ $oldimage }}" class="rounded-full block border-2 object-fill aspect-square overflow-hidden border-gray-700" width="208" height="208" />
                    </div>
                    @endif
                @endif
                <button type="button" wire:click.prevent="removeimage" class="w-52 my-4 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-lg font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف الصورة الشخصية</button>
                <input type="file" wire:model.live="image" id="image" class=" my-4 shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسمك" required>
                @error('image')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-[#016241] mb-2">الاسم:</label>
                <input type="text" wire:model.live="name" id="name" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسمك" required>
                @error('name')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="username" class="block text-sm font-medium text-gray-700 dark:text-[#016241] mb-2">اسم المستخدم:</label>
                <input type="text" disabled wire:model.live="username" id="username" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-gray-300" placeholder="اكتب اسم المستخدم" required>
                @error('username')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-[#016241] mb-2">البريد الالكتروني</label>
                <input type="email" disabled wire:model.live="email" id="email" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-gray-300" placeholder="اكتب بريدك الالكتروني"  required>
                @error('email')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="password" class="block text-sm font-medium text-gray-700 dark:text-[#016241] mb-2">كلمة السر</label>
                <input type="{{ $showpass ? 'text' : 'password' }}" wire:model.live="password" id="password" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="ادخل كلمة السر" required>
                <span class="text-sm font-bold text-[#9b6948]">* ادخل كلمة سر جديدة اذا كنت تريد تغييرها </span><br/>
                <label for="showpass" class="inline-flex items-center">
                    <input wire:model.live="showpass" id="showpass" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="showpass">
                    <span class="ms-2 text-sm text-gray-600">{{ __('اظهار الباسورد') }}</span>
                </label>
                @error('password')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-[#016241] mb-2">رقم التليفون:</label>
                <input type="text" wire:model.live="phone" id="phone" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب رقم التليفون" required>
                @error('phone')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="country" class="block text-sm font-medium text-gray-700 dark:text-[#016241] mb-2">البلد:</label>
                <select wire:model.live="country" id="role" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                    <option style="direction:rtl;" value="{{ $country }}" selected>اختر البلد</option>
                    @foreach ( $listcountries as $key => $listcountry)
                        <option style="direction:rtl;" value="{{ $listcountry }}">{{ $listcountry }}</option>
                    @endforeach
                </select>
                @error('country')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            @if (auth()->user()->role == 'teacher')
            <div class="mb-4">
                <label for="zoom_user_id" class="block text-sm font-medium text-gray-700 dark:text-[#016241] mb-2">ZOOM USER ID:</label>
                <input type="text" wire:model.live="zoom_user_id" id="zoom_user_id" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="" required>
                @error('zoom_user_id')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="account_id" class="block text-sm font-medium text-gray-700 dark:text-[#016241] mb-2">ACCOUNT ID: </label>
                <div class="flex justify-start gap-2">
                    <input type="text" wire:model.live="account_id" id="account_id" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="" required>
                </div>
                {{-- #016241 --}}
                @error('account_id')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="client_id" class="block text-sm font-medium text-gray-700 dark:text-[#016241] mb-2">CLIENT ID: </label>
                <div class="flex justify-start gap-2">
                    <input type="text" wire:model.live="client_id" id="client_id" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="" required>
                </div>
                {{-- #016241 --}}
                @error('client_id')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4">
                <label for="client_secret" class="block text-sm font-medium text-gray-700 dark:text-[#016241] mb-2">CLIENT SECRET: </label>
                <div class="flex justify-start gap-2">
                    <input type="text" wire:model.live="client_secret" id="client_secret" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="" required>
                </div>
                {{-- #016241 --}}
                @error('client_secret')
                    <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4 flex justify-start gap-5 font-cairo">
                <div class="text-white">
                    @if ($role === 'admin')
                        <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-danger-500 bg-danger-500">
                            مدير
                    @elseif ($role === 'moderator')
                        <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-warning-500 bg-warning-500">
                            مراقب
                    @elseif ($role === 'supervisor')
                        <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-success-500 bg-success-500">
                            مشرف
                    @elseif ($role === 'sales')
                        <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-secondary-500 bg-secondary-500">
                            بائع
                    @elseif ($role === 'accountant')
                        <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-dark-500 bg-black">
                            محاسب
                    @elseif ($role === 'teacher')
                        <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-primary-500 bg-primary-500">
                            مدرس
                    @elseif ($role === 'student')
                        <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-info-500 bg-info-500">
                            طالب
                    @endif
                        </div>
                    @error('role')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                @endif
                @if (auth()->user()->role == 'student')
                    <div class="text-white font-medium text-xl">الإشتراك: {{ $this->getsubscription($subscription) }}</div>
                    <div class="text-white font-medium text-xl">الحصص المتبقية: {{ $numofclasses }}</div>
                    <div class="text-white font-medium text-xl">رصيد المكافئات: {{ $balance }}</div>
                    <div class="text-white font-medium text-xl">كود الدعوة لصديق: <span class="bg-green-900 px-2 uppercase"><a href="{{ env('APP_URL') . '/register'. '/' . $inv_code }}" target="_blank">{{ $inv_code }}</a></span></div>

            </div>
            {{-- data table --}}
            <div id="report-content" class="overflow-x-auto -mx-6 dashcode-data-table">
                <header class=" block card-header noborder">
                    <h5 class="font-bold text-3xl">شهادات التقدير والإجادة</h5>
                </header>
                <div class="inline-block min-w-full align-middle">
                    <div class="overflow-hidden">
                    @unless ($allcertificates->count() == 0)
                    <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl">
                        <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                        <tr>
                            <th scope="col" class=" table-th text-xl font-medium"><input type="checkbox" wire:model.live="selectall" class="border-gray-300 rounded bg-gray-50 borded ml-1"> الكل </th>
                            <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('student_id')">اسم الطالب</a></th>
                            <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('teacher_id')">اسم المعلم</a></th>
                            <th scope="col" class=" table-th text-xl font-medium">اسم الشهادة</th>
                            <th scope="col" class=" table-th text-xl font-medium">التقدير</th>
                            <th scope="col" class=" table-th text-xl font-medium ">أجراءات</th>
                        </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                        @foreach ($allcertificates as $allcertificate)
                        <tr wire:key="expcat-{{ $allcertificate->id }}">
                            <td class="table-td text-xl font-normal"><input type="checkbox" wire:model.live="selectedcertificates" value="{{ $allcertificate->id }}" class="border-gray-300 rounded bg-gray-50 borded"></td>
                            <td class="table-td text-xl font-normal">
                            <span class=" text-slate-600 dark:text-white capitalize">{{ $allcertificate->student->name }}</span>
                            </td>
                            <td class="table-td text-xl font-normal">
                            <span class=" text-slate-600 dark:text-white capitalize">{{ $allcertificate->teacher->name }}</span>
                            </td>
                            <td class="table-td text-xl font-normal">
                            <span class=" text-slate-600 dark:text-white capitalize">{{ $allcertificate->title }}</span>
                            </td>
                            <td class="table-td text-xl font-normal">
                            <span class=" text-slate-600 dark:text-white capitalize">{{ $allcertificate->grade }}</span>
                            </td>
                            <td class="table-td  text-xl font-normal">
                            <div class="flex space-x-3 rtl:space-x-reverse">
                                <button type="button" wire:click.prevent="showcert({{ $allcertificate->id }})">
                                    <iconify-icon icon="heroicons:eye-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="مشاهدة"></iconify-icon>
                                </button>
                                <button type="button" wire:click.prevent="printcert({{ $allcertificate->id }})">
                                    <iconify-icon icon="heroicons:printer-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="طباعة"></iconify-icon>
                                </button>
                            </div>
                            </td>
                        </tr>
                        @endforeach
                        </tbody>
                    </table>
                    @else
                    <div class="text-center text-xl font-bold text-[#016241]">لا يوجد شهادات حتى الآن.</div>
                    @endunless
                    </div>
                </div>
            </div>
                @endif
            <div class="flex justify-end my-4">
                <button type="button" wire:loading.remove wire:click.prevent="save" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-lg font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>
                <a href="/" wire:navigate class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-center text-lg font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</a>
            </div>
        </form>
    </div>
    @push('scripts')
<script>
    window.addEventListener('open-new-window', function(event){
        // console.log(typeof event.detail[0]);
        const url = event.detail[0];
        window.open(url, '_blank');
    })
</script>
@endpush
</div>
