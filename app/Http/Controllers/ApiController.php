<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Courses;
use App\Models\lectures;
use App\Models\Certificate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ApiController extends Controller
{
    // users methods
    public function userIndex ()
    {
        return response()->json(User::all());
    }
    public function userShow ($id)
    {
        $user = User::find($id);
        return response([
            'user' => $user,
        ]);
    }
    // certificates methods
    public function certificateIndex ()
    {
        return response()->json(Certificate::all());
    }
    public function certificateShow ($id)
    {
        return response()->json(Certificate::find($id));
    }
    // courses methods
    public function courseIndex ()
    {
        return response()->json(Courses::all());
    }
    public function courseShow ($id)
    {
        return response()->json(Courses::find($id));
    }
    // lectures methods
    public function lectureIndex ()
    {
        return response()->json(lectures::all());
    }
    public function lectureShow ($id)
    {
        return response()->json(lectures::find($id));
    }
}
