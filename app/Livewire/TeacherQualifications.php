<?php

namespace App\Livewire;

use App\Models\TeacherQualifications as ModelTeacherQualifications;
use App\Models\Settings;
use App\Traits\UseFilter;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Url;
use Livewire\WithPagination;
use Livewire\Component;

class TeacherQualifications extends Component
{
    use WithPagination, UseFilter;

    public $selectedteachqualifications = [];
    public $selectall = false;

    public $qualification_id;
    public $name = '';
    public $rateperhour = 0;
    public $notes = '';

    public function save()
    {
        $validated = $this->validate([
            'name' => 'required|unique:expenses_categories,name|string|max:120',
            'rateperhour' => 'integer|required',
            'notes' => 'string|max:500',
        ]);

        ModelTeacherQualifications::create($validated);
        $this->closemodal('add');
        $this->dispatch('flashsaved');

    }
    public function showedit(ModelTeacherQualifications $qualification)
    {
        $this->qualification_id = $qualification->id;
        $this->name = $qualification->name;
        $this->rateperhour = $qualification->rateperhour;
        $this->notes = $qualification->notes;
        $this->openmodal('edit');
    }
    public function update()
    {
        $validated = $this->validate([
            'name' => 'required|unique:expenses_categories,name,' . $this->qualification_id . '|string|max:120',
            'rateperhour' => 'integer|required',
            'notes' => 'string|max:500',
        ]);

        ModelTeacherQualifications::find($this->qualification_id)->update($validated);

        $this->closemodal('edit');
        $this->dispatch('flashupdated');
    }
    public function showdelete(ModelTeacherQualifications $qualification)
    {
        $this->qualification_id = $qualification->id;
        $this->name = $qualification->name;
        $this->rateperhour = $qualification->rateperhour;
        $this->notes = $qualification->notes;
        $this->openmodal('delete');
    }
    public function delete(){

        ModelTeacherQualifications::find($this->qualification_id)->delete();

        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }
    public function deleteselected()
    {
        ModelTeacherQualifications::whereIn('id', $this->selectedteachqualifications)->delete();
        $this->selectedteachqualifications = [];
        $this->closemodal('deleteselected');
    }


    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedteachqualifications = ModelTeacherQualifications::paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedteachqualifications = [];
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'qualification_id',
            'name',
            'rateperhour',
            'notes',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function resetsearch()
    {
        $this->reset('search');
    }
    public function mount()
    {
        if(empty($this->perpage))
        {
            $this->perpage = Settings::find(1)->perpage;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'ASC';
    }
    public function render()
    {
        return view('livewire.teacher-qualifications', [
            'qualifications' => ModelTeacherQualifications::search($this->search)
            ->paginate($this->perpage),
        ]);
    }
}
