<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_reports', function (Blueprint $table) {
            $table->id();
            $table->integer('sales_id')->unsigned();
            $table->integer('student_id')->unsigned();
            $table->string('subscription');
            $table->integer('paid');
            $table->integer('bank_id')->unsigned()->nullable();
            $table->string('trans_code')->nullable()->default('');
            $table->string('numofclasses');
            $table->date('startdate')->nullable();
            $table->string('reg_status')->default('new');
            $table->integer('lastupdateuser_id')->unsigned();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_reports');
    }
};
