<?php

namespace App\Livewire;

use App\Models\Banks;
use App\Models\Expenses as ModelExpense;
use App\Models\ExpensesCategory;
use App\Models\ExpensesSubcategory;
use App\Models\Notification;
use App\Models\Settings;
use App\Models\User;
use App\Traits\UseDefault;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\WithPagination;
use App\Traits\UseFilter;

use Livewire\Component;

class Expenses extends Component
{
    use WithPagination, UseFilter, UseDefault;

    public $selectedexpense = [];
    public $selectall = false;

    public $expense_id;
    public $name = '';
    public $category_id;
    public $subcategory_id = '';
    public $date;
    public $amount;
    public $notes = '';
    public $lastupdateuser_id;
    public $lastupdatedat;

    public $totalexpense = 0;
    public $expcategories= [];
    public $banks= [];
    public $expsubcategories= [];
    public $modalexpsubcategories= [];
    public $allsalesmen= [];

    public function save()
    {
        $validated = $this->validate([
            'name' => 'required|string|max:120',
            'category_id' => 'required|numeric|max:99',
            'subcategory_id' => 'required|string|max:99',
            'date' => 'required|date',
            'amount' => 'required|numeric',
            'notes' => 'string|max:500',
            'lastupdateuser_id' => '',
        ]);
        $validated['lastupdateuser_id'] = auth()->user()->id;

        $expense = ModelExpense::create($validated);

        $safebalance = $this->settings->safe;

        if($validated['category_id'] == 1)
        {
            $bank = Banks::find($validated['subcategory_id']);
            $bankbalance = $bank->balance;
            $bank->update([
                'balance' => $bankbalance - $validated['amount'],
            ]);
            $convertedamount = ($validated['amount'] / $this->convertPaidToSAR($bank->country)) * $this->settings->egyptrate;
            // dd($safebalance, $convertedamount, $safebalance + $convertedamount);
            $this->settings->update([
                'safe' => $safebalance + $convertedamount,
            ]);
        } else {
            $this->settings->update([
                'safe' => $safebalance - $validated['amount'],
            ]);
        }

        $notifiedusers = User::whereIn('role', ['superadmin','admin','acountant'])->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'new expense',
                'url' => 'expenses?q=' . $expense->name . '&stdate=' . Carbon::parse($validated['date'])->format('Y-m-d') . '&endate=' . Carbon::parse($validated['date'])->format('Y-m-d'),
                'message' => 'تم اضافة سند مصروفات جديد باسم "' . $expense->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }

        $this->closemodal('add');
        $this->dispatch('flashsaved');
    }
    public function showedit(ModelExpense $expense)
    {
        $this->expense_id = $expense->id;
        $this->name = $expense->name;
        $this->category_id = $expense->category_id;
        $this->subcategory_id = $expense->subcategory_id;
        $this->date = $expense->date;
        $this->amount = $expense->amount;
        $this->notes = $expense->notes;
        $this->lastupdateuser_id = $expense->user->name;
        $this->lastupdatedat = Carbon::parse($expense->updated_at)->format('Y-m-d || A g:i:s');
        // dd($this->expense_id, $this->name, $this->category_id, $this->subcategory_id, $this->date, $this->amount, $this->notes);
        $this->openmodal('edit');
    }
    public function update()
    {
        $validated = $this->validate([
            'name' => 'required|string|max:120',
            'category_id' => 'required|numeric|max:99',
            'subcategory_id' => 'required|string|max:99',
            'date' => 'required|date',
            'amount' => 'required|numeric',
            'notes' => 'string|max:500',
            'lastupdateuser_id' => '',
        ]);
        $validated['lastupdateuser_id'] = auth()->user()->id;

        $expense = ModelExpense::find($this->expense_id);
        $expense->update($validated);

        if($validated['category_id'] == 1)
        {
            $bank = Banks::find($validated['subcategory_id']);
            $bankbalance = $bank->balance;
            $bank->update([
                'balance' => $bankbalance - $validated['amount'],
            ]);
            $convertedamount = ($validated['amount'] / $this->convertPaidToSAR($bank->country)) * $this->settings->egyptrate;
            $settings = Settings::find(1);
            $safebalance = $settings->safe;
            // dd($safebalance, $convertedamount, $safebalance + $convertedamount);
            $settings->update([
                'safe' => $safebalance + $convertedamount,
            ]);
        }

        $notifiedusers = User::where('role', 'admin')->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'expense updated',
                'url' => 'expenses?q=' . $expense->name . '&stdate=' . Carbon::parse($validated['date'])->format('Y-m-d') . '&endate=' . Carbon::parse($validated['date'])->format('Y-m-d'),
                'message' => 'تم تعديل سند مصروفات باسم "' . $expense->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }

        $this->closemodal('edit');
        $this->dispatch('flashupdated');
    }
    public function showdelete(ModelExpense $expense)
    {
        $this->expense_id = $expense->id;
        $this->name = $expense->name;
        $this->notes = $expense->notes;
        $this->openmodal('delete');
    }
    public function delete(){

        $expense = ModelExpense::find($this->expense_id);
        $expense->delete();
        $notifiedusers = User::where('role', 'admin')->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'expense updated',
                'url' => 'expenses?excat=' . $expense->category_id . '&exsub=' . $expense->subcategory_id,
                'message' => 'تم حذف سند مصروفات باسم "' . $expense->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }
        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }
    public function deleteselected()
    {
        ModelExpense::whereIn('id', $this->selectedexpense)->delete();
        $this->selectedexpense = [];
        $this->closemodal('deleteselected');
    }
    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedexpense = ModelExpense::filterexpcate($this->filterexpcate)
            ->filterexpsubcate($this->filterexpsubcate)
            ->filtersalesman($this->filtersalesman)
            ->filterbank($this->filterbank)
            ->filterdate($this->filterdatestart, $this->filterdateend)
            ->orderBy($this->sortBy, $this->sortDir)
            ->search($this->search)
            ->paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedexpense = [];
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'expense_id',
            'name',
            'category_id',
            'subcategory_id',
            'date',
            'amount',
            'filterexpcate',
            'filterexpsubcate',
            'filtersalesman',
            'notes',
            'lastupdateuser_id',
            'lastupdatedat',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function updatedfilterexpcate()
    {
        $this->resetfilter('filterexpsubcate', 'filtersalesman');
    }
    #[Computed]
    public function updated($property)
    {
        if($property == 'category_id')
        {
            if($this->category_id > 2)
            {
                $this->modalexpsubcategories = ExpensesSubcategory::where('category_id', $this->category_id)->get();
            }
        }
        // dd($this->modalexpsubcategories, $this->category_id, $this->expsubcategories);
    }
    public function salesman($fullword)
    {
        $salesid = substr($fullword, 7);
        return 'البائع: ' . User::find($salesid)->name;
    }
    public function getbankname($bankid)
    {
        return Banks::find($bankid)->name;
    }
    public function getsubcattitle(ModelExpense $expense)
    {
        switch ($expense->category_id) {
            case '2':
                return $this->salesman($expense->subcategory_id);
                break;

            case '1':
                return $this->getbankname($expense->subcategory_id);
                break;

            default:
                return $expense->subCategory->name;
                break;
        }
    }
    public function getsum()
    {
        if(empty($this->filterexpcate))
        {
            $totalexpense = ModelExpense::filternotexpcate(1)
            ->filterdate($this->filterdatestart, $this->filterdateend)
            ->orderBy($this->sortBy, $this->sortDir)
            ->search($this->search)
            ->paginate($this->perpage)->sum('amount');
            // exclude bank withdraws in expenses reports
            // $banks = Banks::all();
            // foreach ($banks as $bank) {
            //     $banksexpensesinsar = ModelExpense::filterexpcate(1)
            //     ->filterexpsubcate($bank->id)
            //     ->filterdate($this->filterdatestart, $this->filterdateend)
            //     ->orderBy($this->sortBy, $this->sortDir)
            //     ->search($this->search)
            //     ->paginate($this->perpage)->sum('amount') / $this->convertPaidToSAR($bank->country);
            //     $banksexpensesinsar *= $this->settings->egyptrate;
            //     $totalexpense += $banksexpensesinsar;
            // }
        } else {
            $totalexpense = ModelExpense::filterexpcate($this->filterexpcate)
            ->filterexpsubcate($this->filterexpsubcate)
            ->filtersalesman($this->filtersalesman)
            ->filterbank($this->filterbank)
            ->filterdate($this->filterdatestart, $this->filterdateend)
            ->orderBy($this->sortBy, $this->sortDir)
            ->search($this->search)
            ->paginate($this->perpage)->sum('amount');
        }
        return round($totalexpense);
    }
    public function mount()
    {
        $this->settings = Settings::find(1);
        if (empty($this->perpage)) {
            $this->perpage = $this->settings->perpage;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'DESC';

        $this->expcategories = ExpensesCategory::all();
        $this->banks = Banks::all();
        $this->expsubcategories = ExpensesSubcategory::where('category_id', $this->filterexpcate)->get();
        // $this->modalexpsubcategories = ExpensesSubcategory::where('category_id', $this->category_id)->get();
        // dd($this->category_id, $this->filterexpcate, $this->modalexpsubcategories, $this->expsubcategories);
        $this->allsalesmen = User::where('role', 'sales')->get(['id', 'name']);
        $this->filterbymonth();
        $this->totalexpense = $this->getsum();
    }
    public function render()
    {
        return view('livewire.expenses', [
            'expenses' => ModelExpense::filterexpcate($this->filterexpcate)
            ->filterexpsubcate($this->filterexpsubcate)
            ->filtersalesman($this->filtersalesman)
            ->filterbank($this->filterbank)
            ->filterdate($this->filterdatestart, $this->filterdateend)
            ->orderBy($this->sortBy, $this->sortDir)
            ->search($this->search)
            ->paginate($this->perpage),
        ]);
    }
}
