<?php

namespace App\Traits;


use Livewire\Attributes\Url;
use Carbon\Carbon;

trait UseFilter
{
    // url tags for filtering
    #[url('q')]
    public $search = '';
    #[url('lim')]
    public $perpage;
    public $perpageoptions = [10, 20, 50, 100, 'all'];
    public $showall;
    public $settings;
    public $classperiod = '';
    #[url('sor')]
    public $sortBy = '';
    #[url('dir')]
    public $sortDir = '';
    #[url('cl')]
    public $filterclass = '';
    #[url('te')]
    public $filterteacher = '';
    #[url('st')]
    public $filterstudent = '';
    #[url('ac')]
    public $filteractive = '';
    #[url('sut')]
    public $filterstatus = '';
    #[url('su')]
    public $filtersupervisor = '';
    #[url('sm')]
    public $filtersalesman = '';
    #[url('ge')]
    public $filtergender = '';
    #[url('ro')]
    public $filterrole = '';
    #[url('cn')]
    public $filtercountry = '';
    #[url('sub')]
    public $filtersubscription = '';
    #[url('mo')]
    public $filtermonth = '';
    #[url('qua')]
    public $filterqualifications = '';
    #[url('excat')]
    public $filterexpcate = '';
    #[url('exsub')]
    public $filterexpsubcate = '';
    #[url('exbnk')]
    public $filterbank;
    #[url('inv')]
    public $filterinvcode = '';
    #[url('reg')]
    public $filterregstatus = '';
    #[url('cor')]
    public $filtercourse = '';

    // date start and date end for filtering
    public $filterby = '';
    #[url('stdate')]
    public $filterdatestart;
    #[url('endate')]
    public $filterdateend;

    public function updatedPerpage()
    {
        $this->resetPage();
    }

    // order data by columns
    public function sortfields($fieldname)
    {
        if($this->sortBy == $fieldname){
            $this->sortDir = ($this->sortDir == "ASC") ? 'DESC' : 'ASC';
            return;
        }
        $this->sortBy = $fieldname;
        $this->sortDir = 'ASC';
    }

    // filter by date day/week/month methods
    public function starttoday()
    {
        return Carbon::createFromFormat('Y-m-d', today()->format('Y-m-d'))->startOfDay();
    }
    public function filterbyday()
    {
        $this->reset(['filterdatestart', 'filterdateend', 'filterby']);
        $this->filterby = 'today';
        $this->filterdatestart = $this->starttoday()->startOfDay();
        $this->filterdateend = $this->starttoday()->endOfDay();
    }
    public function filterbyweek()
    {
        $this->reset(['filterdatestart', 'filterdateend', 'filterby']);
        $this->filterby = 'week';
        $this->filterdatestart = $this->starttoday()->startOfWeek(Carbon::SATURDAY)->startOfDay();
        $this->filterdateend = $this->starttoday()->endOfWeek(Carbon::FRIDAY)->endOfDay();
    }
    public function filterbymonth()
    {
        $this->reset(['filterdatestart', 'filterdateend', 'filterby']);
        $this->filterby = 'month';
        $this->filterdatestart = $this->starttoday()->startOfMonth()->startOfDay();
        $this->filterdateend = $this->starttoday()->endOfMonth()->endOfDay();
    }
    public function filterbyyear()
    {
        $this->reset(['filterdatestart', 'filterdateend', 'filterby']);
        $this->filterby = 'year';
        $this->filterdatestart = $this->starttoday()->startOfYear()->startOfDay();
        $this->filterdateend = $this->starttoday()->endOfYear()->endOfDay();
    }
    public function filterbyofspecmonth($month)
    {
        // $month = empty($month) ? 0 : $month;
        $currmonth = Carbon::today()->month;
        if($month == 0)
        {
            $submonths = 0;
        } else {
            $submonths = $currmonth - $month;
        }
        $this->reset(['filterdatestart', 'filterdateend', 'filterby']);
        $this->filterdatestart = $this->starttoday()->subMonths($submonths)->startOfMonth()->startOfDay();
        $this->filterdateend = $this->starttoday()->subMonths($submonths)->endOfMonth()->endOfDay();
    }

    // resetting methods after filtering/search
    public function resetsearch()
    {
        $this->reset('search');
    }
    public function resetfilter($filter)
    {
        $this->reset($filter);
    }
    public function resetallfilter()
    {
        $this->reset([
            'filterstudent',
            'filterteacher',
            'filtersupervisor',
            'filterrole',
            'filtersalesman',
            'filtersubscription',
            'filterqualifications',
            'filterbank',
            'filtercountry',
            'filtermonth',
            'filtergender',
            'filteractive',
            'filterexpcate',
            'filterexpsubcate',
            'filterinvcode',
            'filterregstatus',
            'filterdatestart',
            'filterdateend',
            'filterby',
            'filtercourse',
            'filterstatus',
        ]);
    }
}
