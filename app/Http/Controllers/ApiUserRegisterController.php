<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Student;
use App\Traits\UseDefault;
use App\Models\ReferalCode;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class ApiUserRegisterController extends Controller
{
    use UseDefault;

    public string $name = '';
    public string $username = '';
    public string $email = '';
    public string $password = '';
    public string $password_confirmation = '';
    public $phone = '';
    public $country = '';

    // from student model
    public $studentid;
    public $dateofbirth = '';
    public $subscription = '';
    public $gender_prefer = '';
    public $paid = 0;
    public $numofclasses = '';
    public $ref_code = '';
    public $balance = 0;
    public $refcode_active_date = null;
    public $startdate = '';

    public $role = 'student';
    public $inv_code = '';
    public $getinvitecode = '';
    public $getinviteuser = '';
    public $getdiscountcode;

    public $code = '';
    public $ref_code_stat = false;
    public $retmessage = '';

    public function register (Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'username' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'string', 'min:8', 'max:16'],
            'phone' => 'required|string|min:11|max:20',
            'country' => 'required|string|min:3|max:40',
        ]);
        $validatedstudent = $request->validate([
            'dateofbirth' => 'required|date',
            'gender_prefer' => 'required|string|min:3|max:8',
            'ref_code' => 'string|nullable|sometimes|min:7|max:9',
        ]);
        $validated['password'] = Hash::make($validated['password']);
        $validated['role'] = 'student';

        $user = User::create($validated);
        $student = Student::create([
            'user_id' => $user->id,
            'dateofbirth' => $validatedstudent['dateofbirth'],
            'gender_prefer' => $validatedstudent['gender_prefer'],
            'subscription' => 'trial',
            'paid' => $this->paid,
            'numofclasses' => 3,
            'ref_code' => $validatedstudent['ref_code'],
            'balance' => $this->balance,
            'inv_code' => $this->inv_code,
            'refcode_active_date' => $this->refcode_active_date,
            'startdate' => Carbon::now(),
        ]);

        $token = $user->createToken($user->name . ' token', ['*'], now()->addHours(12))->plainTextToken;
        $response = [
            'user' => $user,
            'student' => $student,
            'token' => $token,
        ];

        return response($response, 201);

        // if(!empty($validated['ref_code']))
        // {
        //     $this->ref_code_stat = true;
        //     $refcode = ReferalCode::where('short_code', $validated['ref_code'])->first();
        //     $addusedby = json_decode($refcode->used_by);
        //     if (is_array($addusedby)) {
        //         array_push($addusedby, $user->id);
        //     } else {
        //         $addusedby[] += $user->id;
        //     }
        //     // dd($addusedby);

        //     $refcode->update([
        //         'used_by' => json_encode($addusedby),
        //     ]);

        //     $refcodediscount = ReferalCode::getdiscount($validated['ref_code'])->first();
        //     if(!empty($refcodediscount))
        //     {
        //         $this->balance = floor($this->paid * ($refcodediscount->discount/100));
        //         $this->refcode_active_date = Carbon::today('Africa/Cairo')->format('Y-m-d');
        //     } else {
        //         $this->balance = 0;
        //         $this->refcode_active_date = null;
        //     }
        // }
        // $notifiedusers = User::whereIn('role', ['superadmin', 'admin', 'moderator'])->pluck('id');
        // foreach ($notifiedusers as $notifieduser) {
        //     Notification::create([
        //         'user_id' => $notifieduser,
        //         'title' => 'new student',
        //         'url' => 'student?q=' . $user->name,
        //         'message' => 'تم تسجيل طالب جديد "' . $user->name  . '".',
        //     ]);
        // }
    }
    public function login(Request $request)
    {
        $validated = $request->validate([
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255'],
            'password' => ['required', 'string', 'min:8', 'max:16'],
        ]);
        $user = User::where('email', $validated['email'])->first();

        if (!$user || !Hash::check($validated['password'], $user->password)) {
            return response([ 'message' => 'Wrong Email Or Password'], 401);
        }
        $student = Student::where('user_id', $user->id)->first();

        if(!empty($user->tokens->last()) && $user->tokens->last()->expires_at <= now())
        {
            $token = $user->tokens->last();
        } else {
            $token = $user->createToken($user->name . ' token', ['*'], now()->addHours(12))->plainTextToken;
        }

        $response = [
            'user' => $user,
            'student' => $student,
            'token' => $token,
        ];

        return response($response, 201);
    }
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json('User Logged Out');
    }
}
