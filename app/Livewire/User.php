<?php

namespace App\Livewire;

use App\Models\Clss;
use App\Models\Notification;
use App\Models\Settings;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\User as ModelsUser;
use App\Traits\UseDefault;
use App\Traits\UseFilter;
use Illuminate\Support\Facades\Hash;
use Livewire\Attributes\Computed;

use Livewire\Component;
use Livewire\WithPagination;

class User extends Component
{
    use WithPagination, UseDefault, UseFilter;

    public $userid = '';
    public $name = '';
    public $username = '';
    public $password = '';
    public $email = '';
    public $role = '';
    public $phone = '';
    public $country = '';
    public $showpass = false;

    public $student_id;
    public $teacher_id;

    public $selectedusers = [];
    public $selectedteachers = [];
    public $selectedstudents = [];
    public $selectall = false;

    public function save()
    {
        $validated = $this->validate([
            'name' => 'required|string|min:3|max:100',
            'username' => 'required|regex:/^[a-zA-Z]+$/|min:3|max:100|unique:user,username',
            'email' => 'required|email|min:5|max:100|unique:user,email',
            'password' => 'required|string|min:8|max:25',
            'role' => 'required|string|min:3|max:10',
            'phone' => 'required|string|min:11|max:20',
            'country' => 'required|string|min:3|max:40',
        ]);

        $validated['password'] = Hash::make($validated['password']);

        $user = ModelsUser::create($validated);
        $notifiedusers = ModelsUser::whereIn('role', ['admin', 'moderator'])->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'new user',
                'url' => 'user?q=' . $user->name,
                'message' => 'تم اضافة عضو جديد "' . $user->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }

        $this->closemodal('add');
        $this->dispatch('flashsaved');
    }
    public function showedit(ModelsUser $user)
    {
        $this->userid = $user->id;
        $this->name = $user->name;
        $this->username = $user->username;
        $this->email = $user->email;
        $this->role = $user->role;
        $this->phone = $user->phone;
        $this->country = $user->country;

        $this->openmodal('edit');
    }
    public function update()
    {
        if(!empty($this->password))
        {
            $validated = $this->validate([
                'name' => 'required|string|min:3|max:100',
                'username' => 'required|regex:/^[a-zA-Z]+$/|min:3|max:100',
                'email' => 'required|email|min:5|max:100',
                'password' => 'required|string|min:8|max:25',
                'role' => 'required|string|min:3|max:10',
                'phone' => 'required|string|min:11|max:20',
                'country' => 'required|string|min:3|max:40',
            ]);
            $validated['password'] = Hash::make($validated['password']);
        } else {
            $validated = $this->validate([
                'name' => 'required|string|min:3|max:100',
                'username' => 'required|regex:/^[a-zA-Z]+$/|min:3|max:100',
                'email' => 'required|email|min:5|max:100',
                'password' => '',
                'role' => 'required|string|min:3|max:10',
                'phone' => 'required|string|min:11|max:20',
                'country' => 'required|string|min:3|max:40',
            ]);
        }

        $user = ModelsUser::find($this->userid);
        $user->update($validated);
        $notifiedusers = ModelsUser::whereIn('role', ['admin', 'moderator'])->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'user updated',
                'url' => 'user?q=' . $user->name,
                'message' => 'تم تعديل العضو "' . $user->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }

        $this->closemodal('edit');
        $this->dispatch('flashupdated');
    }
    public function showdelete(ModelsUser $user)
    {
        $this->userid = $user->id;
        $this->name = $user->name;
        $this->teacher_id = !empty($user->teacher->id) ? $user->teacher->id : '';
        $this->student_id = !empty($user->student->id) ? $user->student->id : '';

        $this->openmodal('delete');
    }
    public function delete()
    {
        $user = ModelsUser::find($this->userid);
        $notifiedusers = ModelsUser::where('role', 'admin')->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'user deleted',
                'url' => 'user',
                'message' => 'تم حذف العضو "' . $user->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }
        $user->delete();

        if (!empty($this->teacher_id))
        {
            Teacher::find($this->teacher_id)->delete();
            $classes = Clss::where('teacher_id', $this->teacher_id)->pluck('id');
            Clss::whereIn('id', $classes)->delete();
        }
        if (!empty($this->student_id))
        {
            Student::find($this->student_id)->delete();
            $classes = Clss::where('student_id', $this->teacher_id)->pluck('id');
            Clss::whereIn('id', $classes)->delete();
        }

        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    #[Computed]
    public function updatedselectedusers()
    {
        $this->selectedteachers = Teacher::query()->whereIn('user_id',  $this->selectedusers)->pluck('id');
        $this->selectedstudents = Student::query()->whereIn('user_id',  $this->selectedusers)->pluck('id');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
        // $this->flashmessage('deleteselected');
    }
    public function deleteselected()
    {
        ModelsUser::query()->whereIn('id', $this->selectedusers)->delete();

        $this->selectedusers = [];
        $this->selectedteachers = [];
        $this->selectedstudents = [];
        $this->selectall = false;
        $this->closemodal('deleteselected');
        $this->flashmessage('flashdeleted');
    }
    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedusers = ModelsUser::search($this->search)
            ->filterrole($this->filterrole)
            ->filtercountry($this->filtercountry)
            ->orderBy($this->sortBy, $this->sortDir)
            ->paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedusers = [];
            $this->selectedteachers = [];
            $this->selectedstudents = [];
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'userid',
            'name',
            'username',
            'email',
            'password',
            'role',
            'phone',
            'country',
            'student_id',
            'teacher_id',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }

    public function mount()
    {
        $this->perpage = Settings::find(1)->perpage;
        $this->sortBy = 'created_at';
        $this->sortDir = 'DESC';
        $this->listcountries;
    }
    public function render()
    {
        $users = $this->perpage === 'all' ? ModelsUser::search($this->search)
        ->filterrole($this->filterrole)
        ->filtercountry($this->filtercountry)
        ->orderBy($this->sortBy, $this->sortDir)
        ->get() :
        ModelsUser::search($this->search)
            ->filterrole($this->filterrole)
            ->filtercountry($this->filtercountry)
            ->orderBy($this->sortBy, $this->sortDir)
            ->paginate($this->perpage);
        return view('livewire.user', [
            'users' => $users,
        ]);
    }
}
