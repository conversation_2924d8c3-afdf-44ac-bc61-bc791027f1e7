<?php

namespace App\Livewire;

use App\Models\Settings;
use Livewire\Component;
use App\Traits\UseCurrency;

class Setting extends Component
{
    use UseCurrency;

    public $classperiod;
    public $perpage;
    public $safe;
    public $tax;
    public $meeting_app;
    public $saudirate;
    public $egyptrate;
    public $uaerate;
    public $kuwaitrate;
    public $qatarrate;
    public $omanrate;
    public $usdrate;

    public function mount()
    {
        $settings = Settings::find(1);
        $this->classperiod = $settings->classperiod;
        $this->perpage = $settings->perpage;
        $this->safe = $settings->safe;
        $this->tax = $settings->tax;
        $this->meeting_app = $settings->meeting_app;
        $this->saudirate = $settings->saudirate;
        $this->egyptrate = $settings->egyptrate;
        $this->uaerate = $settings->uaerate;
        $this->kuwaitrate = $settings->kuwaitrate;
        $this->qatarrate = $settings->qatarrate;
        $this->omanrate = $settings->omanrate;
        $this->usdrate = $settings->usdrate;
    }

    public function save()
    {
        $validated = $this->validate([
            'classperiod' => 'required|string|max:5',
            'perpage' => 'required|string|max:5',
            'safe' => 'required|numeric|min:0|max:1000000',
            'tax' => 'required|numeric|min:0|max:100',
            'meeting_app' => 'required|string|max:20',
            'saudirate' => 'decimal:0,1000|min:0|max:1000',
            'egyptrate' => 'decimal:0,1000|min:0|max:1000',
            'uaerate' => 'decimal:0,1000|min:0|max:1000',
            'kuwaitrate' => 'decimal:0,1000|min:0|max:1000',
            'qatarrate' => 'decimal:0,1000|min:0|max:1000',
            'omanrate' => 'decimal:0,1000|min:0|max:1000',
            'usdrate' => 'decimal:0,1000|min:0|max:1000',
        ]);
        Settings::find(1)->update($validated);

        $this->dispatch('flashupdated');
    }
    public function refreshrate()
    {
        $this->egyptrate = $this->saudirate * $this->generateConversionRate('EGP')['conversion_rate'];
        $this->uaerate = $this->saudirate * $this->generateConversionRate('AED')['conversion_rate'];
        $this->kuwaitrate = $this->saudirate * $this->generateConversionRate('KWD')['conversion_rate'];
        $this->qatarrate = $this->saudirate * $this->generateConversionRate('QAR')['conversion_rate'];
        $this->omanrate = $this->saudirate * $this->generateConversionRate('OMR')['conversion_rate'];
        $this->usdrate = $this->saudirate * $this->generateConversionRate('USD')['conversion_rate'];
    }
    public function render()
    {
        return view('livewire.setting');
    }
}
