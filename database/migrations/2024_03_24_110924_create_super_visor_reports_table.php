<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('super_visor_reports', function (Blueprint $table) {
            $table->id();
            $table->integer('class_id')->unsigned();
            $table->integer('user_id')->unsigned();
            $table->integer('internet');
            $table->integer('camera');
            $table->integer('smartboard');
            $table->integer('audio');
            $table->integer('teachbackground');
            $table->integer('teachenvironment');
            $table->integer('teachrelationship');
            $table->integer('teacheducational');
            $table->longText('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('super_visor_reports');
    }
};
