<?php

namespace App\Livewire;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Banks;
use Livewire\Component;
use App\Models\Settings;
use App\Traits\UseFilter;
use App\Traits\UseDefault;
use App\Models\SalesReport;
use App\Models\Notification;

use Livewire\WithPagination;
use Livewire\Attributes\Computed;

class Sales extends Component
{
    use WithPagination, UseFilter, UseDefault;


    public $salesmantotalsales = 0;
    public $allstudents;
    public $allpaid;
    public $sales;
    public $banks = [];

    //sales properties
    public $salereport_id;
    public $saletype = 'تجديد للطالب';
    public $saletitle = '';
    public $student_id;
    public $sales_id;
    public $subscription = 'fixed';
    public $paid;
    public $bank_id;
    public $trans_code;
    public $numofclasses;
    public $startdate;
    public $reg_status = 'resub';
    public $lastupdateuser_id;
    public $lastupdatedat;

    public $safebalance;

    public function save()
    {
        if($this->saletype ==  'تجديد للطالب')
        {
        $validated = $this->validate([
            'student_id' => 'required||numeric',
            'sales_id' => 'required|numeric',
            'subscription' => 'required|string|max:10',
            'paid' => 'required|numeric',
            'bank_id' => 'required|numeric',
            'trans_code' => 'string|min:3|max:25',
            'numofclasses' => 'required|numeric|min:1|max:100',
            'startdate' => 'required|date',
            'reg_status' => 'required|string|max:120',
        ]);

        $validated['sales_id'] = auth()->user()->id;
        $studentuser = User::find($validated['student_id']);
        $studentcountry = $studentuser->country;
        $convertedpaid = $validated['paid'] / $this->convertPaidToSAR($studentcountry);
        SalesReport::create([
            'sales_id' => $validated['sales_id'],
            'student_id' => $validated['student_id'],
            'subscription' => $validated['subscription'],
            'paid' => $convertedpaid,
            'bank_id' => $validated['bank_id'],
            'trans_code' => $validated['trans_code'],
            'numofclasses' => $validated['numofclasses'],
            'startdate' => $validated['startdate'],
            'reg_status' => $this->reg_status,
            'lastupdateuser_id' => auth()->user()->id,
        ]);

        $bank = Banks::find($this->bank_id);
        $bankbalance = $bank->balance;
        $bank->update([
            'balance' => $bankbalance + $validated['paid'],
        ]);

        $studentname = $studentuser->name;

        $notifiedusers = User::whereIn('role', ['superadmin','admin','accountant'])->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'new sales',
                'url' => 'sales?q=' . $studentname . '$stdate=' . $validated['startdate'],
                'message' => 'تم اضافة سند قبض لتجديد الطالب "' . $studentname . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }
    } else {
        $this->student_id = $this->sales_id = $this->bank_id = $this->numofclasses = 1000;
        $this->subscription = $this->reg_status = 'other';
        $this->trans_code = '';
        $validated = $this->validate([
            'paid' => 'required|numeric',
            'startdate' => 'required|date',
        ]);

        $this->safebalance += $validated['paid'];
        // dd($this->safebalance);

        SalesReport::create([
            'sales_id' => $this->sales_id,
            'student_id' => $this->student_id,
            'subscription' => $this->subscription,
            'paid' => $validated['paid'],
            'bank_id' => $this->bank_id,
            'trans_code' => $this->saletitle,
            'numofclasses' => $this->numofclasses,
            'startdate' => $validated['startdate'],
            'reg_status' => $this->reg_status,
            'lastupdateuser_id' => auth()->user()->id,
        ]);

        $this->settings->update([
            'safe' => $this->safebalance,
        ]);

        $notifiedusers = User::whereIn('role', ['superadmin','admin','accountant'])->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'new sales',
                'url' => 'sales?stdate=' . $validated['startdate'],
                'message' => 'تم اضافة سند قبض بواسطة "' . auth()->user()->name . '".',
            ]);
        }

    }

        $this->closemodal('add');
        $this->dispatch('flashsaved');
    }

    public function showedit(SalesReport $salereport)
    {
        $this->salereport_id = $salereport->id;
        $this->student_id = $salereport->student_id;
        $this->sales_id = $salereport->sales_id;
        $this->paid = $salereport->paid;
        $this->bank_id = $salereport->bank_id;
        $this->numofclasses = $salereport->numofclasses;
        $this->startdate = $salereport->startdate;
        $this->reg_status = $salereport->reg_status;
        $this->lastupdateuser_id = $salereport->user->name;
        $this->lastupdatedat = Carbon::parse($salereport->updated_at)->format('Y-m-d || A g:i:s');
        if ($this->reg_status == 'other')
        {
            $this->saletype = 'ايرادات اخرى';
            $this->saletitle = $salereport->trans_code;
        } else {
            $this->saletype = 'تجديد للطالب';
            $this->trans_code = $salereport->trans_code;
        }

        $this->openmodal('edit');
    }

    public function update()
    {
        $validated = $this->validate([
            'student_id' => 'required||numeric',
            'sales_id' => 'required|numeric',
            'paid' => 'required|numeric',
            'bank_id' => 'required|numeric',
            'trans_code' => 'string|min:3|max:25',
            'numofclasses' => 'required|numeric|min:1|max:100',
            'startdate' => 'required|date',
            'reg_status' => 'required|string',
            'lastupdateuser_id' => '',
        ]);
        $validated['lastupdateuser_id'] = auth()->user()->id;

        SalesReport::find($this->salereport_id)->update($validated);

        $studentname = User::find($validated['student_id'])->name;
        $salesname = User::find($validated['sales_id'])->name;

        $notifiedusers = User::whereIn('role', ['superadmin','admin','accountant'])->pluck('id');
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'sales updated',
                'url' => 'sales?q=' . $studentname . '$stdate=' . $validated['startdate'],
                'message' => 'تم تعديل سند قبض لتجديد الطالب "' . $studentname . '" بواسطة "' . $salesname . '".',
            ]);
        }
        $this->closemodal('edit');
        $this->dispatch('flashsaved');
    }

    public function getcurrency($country)
    {
        return SalesReport::filtercountry($country)
        ->sum('paid');
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'salereport_id',
            'sales_id',
            'student_id',
            'subscription',
            'paid',
            'numofclasses',
            'startdate',
            'reg_status',
            'bank_id',
            'trans_code',
            'saletype',
            'saletitle',
            'lastupdateuser_id',
            'lastupdatedat',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    #[Computed]
    public function updated($property)
    {
        if($property === 'student_id')
        {
            $usercountry = '';
            if (!empty($this->student_id)) {
                $usercountry = User::find($this->student_id)->country;
                $this->banks = Banks::filtercountry($usercountry)->get();
            } else {
                $this->banks = Banks::filtercountry($usercountry)->get();
            }
        }
    }
    public function mount()
    {
        $this->settings = Settings::find(1);
        $this->perpage = $this->settings->perpage;
        $this->safebalance = $this->settings->safe;
        if(auth()->user()->role == 'sales')
        {
            $this->filtersalesman = auth()->user()->id;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'DESC';
        $this->filterbymonth();

        $this->sales = User::filterrole('sales')
        ->get();
        $this->allstudents = User::filterrole('student')->filtersubscription('fixed')
        ->get();
        $this->allpaid = SalesReport::sum('paid');
        $this->banks = Banks::orderBy('country', 'ASC')->get();
    }

    public function render()
    {
        return view('livewire.sales', [
            'salesreports' => SalesReport::filterdate($this->filterdatestart, $this->filterdateend)
            ->search($this->search)
            ->filtersalesman($this->filtersalesman)
            ->filtercountry($this->filtercountry)
            ->filterregstatus($this->filterregstatus)
            ->orderBy($this->sortBy, $this->sortDir)
            ->paginate($this->perpage),
        ]);
    }
}
