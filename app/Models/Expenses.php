<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Expenses extends Model
{
    use HasFactory;

    protected $table = 'expenses';

    protected $fillable = [
        'name',
        'category_id',
        'subcategory_id',
        'date',
        'amount',
        'notes',
        'lastupdateuser_id',

    ];

    public function subCategory()
    {
        return $this->belongsTo(ExpensesSubcategory::class, 'subcategory_id', 'id');
    }
    public function category()
    {
        return $this->belongsTo(ExpensesCategory::class, 'category_id', 'id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'lastupdateuser_id', 'id');
    }

    public function scopeSearch($query, $value)
    {
        return $query->where('name', 'like', '%' . $value . '%');
    }
    public function scopeFilterdate($query, $startdate, $enddate)
    {
        return $query->when($startdate, function ($query, $startdate){
            return $query->whereDate('date', '>=', $startdate);
        })
        ->when($enddate, function ($query, $enddate){
            return $query->whereDate('date', '<=', $enddate);
        });
    }
    public function scopeFilterexpcate($query, $value)
    {
        if (empty($value))
        {
            return $query;
        }
        return $query->whereHas('category', function ($query) use ($value) {
            $query->where('category_id', 'like', $value);
        });
    }
    public function scopeFilterexpsubcate($query, $value)
    {
        if (empty($value))
        {
            return $query;
        }
        return $query->whereHas('subCategory', function ($query) use ($value) {
            $query->where('subcategory_id', 'like', $value);
        });
    }
    public function scopeFiltersalesman($query, $value)
    {
        if (empty($value))
        {
            return $query;
        }
        return $query->where('subcategory_id', $value );
    }
    public function scopeFilterbank($query, $value)
    {
        return $query->when($value, function ($query, $value){
            return $query->where('subcategory_id', $value );
        });
    }
    public function scopeFilternotexpcate($query, $value)
    {
        return $query->where('category_id', '!=', $value );
    }
}
