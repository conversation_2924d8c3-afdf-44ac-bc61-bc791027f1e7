<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('students', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->unsigned();
            $table->date('dateofbirth');
            $table->string('gender_prefer')->nullable();
            $table->string('subscription');
            $table->integer('subscription_id')->unsigned()->nullable();
            $table->integer('paid');
            $table->integer('bank_id')->unsigned()->nullable();
            $table->integer('numofclasses');
            $table->string('ref_code')->nullable();
            $table->string('inv_code')->nullable();
            $table->integer('balance')->default(0);
            $table->date('refcode_active_date')->nullable();
            $table->date('startdate')->nullable();
            $table->string('reg_status')->default('new');
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('students');
    }
};
