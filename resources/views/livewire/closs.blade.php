<div>
{{-- crud modals --}}
    {{-- add modal --}}
    <x-g-modal name="add">
        <div class="bg-white dark:bg-blue-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">إضافة حلقة جديدة</h1>
                <button type="button" wire:click.prevent="closemodal('add')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="add-class"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="mb-4">
                    <label for="student_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الطالب:</label>
                    <select wire:model.live="student_id" id="student_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  selected>اختر الطالب</option>
                        @foreach ( $students as $student)
                            <option style="direction:rtl;" value="{{ $student->id }}">{{ $student->name }}</option>
                        @endforeach
                    </select>
                    @error('student_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="qualifi_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المهارة:</label>
                    <select wire:model.live="qualifi_id" id="qualifi_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  value="" selected>اختر المهارة</option>
                        @foreach ( $qualifications as $qualification)
                            <option style="direction:rtl;" value="{{ $qualification->id }}">{{ $qualification->name }}</option>
                        @endforeach
                    </select>
                    @error('qualifi_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="studentgenderpref" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المعلم:</label>
                    <select wire:model.live="studentgenderpref" id="studentgenderpref" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                            <option style="direction:rtl;" value="" selected>معلم / معلمة</option>
                            <option style="direction:rtl;" value="">أيهما</option>
                            <option style="direction:rtl;" value="male">معلم</option>
                            <option style="direction:rtl;" value="female">معلمة</option>
                    </select>
                    @error('studentgenderpref')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="teacher_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المعلم:</label>
                    <select wire:model.live="teacher_id" id="teacher_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  selected>اختر المعلم</option>
                        @foreach ( $teachers as $teacher)
                            <option style="direction:rtl;" value="{{ $teacher->id }}">{{ $teacher->name }}</option>
                        @endforeach
                    </select>
                    @error('teacher_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <div
                        x-data
                        x-init="flatpickr($refs.datetimewidget, {wrap: true, enableTime: true, dateFormat: 'Y-m-dTH:i:S'});"
                        x-ref="datetimewidget"
                        class="flatpick mx-auto mt-5"
                    >
                        <label for="date_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">وقت الحصة:</label>
                        <div class="flex align-middle align-content-center">
                            <input
                                x-ref="date_time"
                                type="text"
                                id="date_time"
                                wire:model.live="date_time"
                                data-input
                                placeholder="اختر التاريخ والوقت للحصة"
                                class="block w-full p-2 border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-r-md shadow-sm"
                            >
                            <a
                                class="h-12 w-10 input-button cursor-pointer rounded-l-md bg-slate-300 hover:bg-slate-600 text-slate-600 hover:text-slate-100 border-gray-300 border-t border-b border-l"
                                title="clear" data-clear
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mt-2 ml-1" viewBox="0 0 20 20" fill="#4b4b4b">
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                    @error('date_time')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="save" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="save"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('add')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- addschedule modal --}}
    <x-g-modal name="addschedule">
        <div class="bg-white dark:bg-blue-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">إضافة جدول حلقات جديد</h1>
                <button type="button" wire:click.prevent="closemodal('addschedule')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="add-shedule"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="mb-4">
                    <label for="student_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الطالب:</label>
                    <select wire:model.live="student_id" id="student_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  value="" selected>اختر الطالب</option>
                        @foreach ( $students as $student)
                            <option style="direction:rtl;" value="{{ $student->id }}">{{ $student->name }}</option>
                        @endforeach
                    </select>
                    @error('student_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="qualifi_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المهارة:</label>
                    <select wire:model.live="qualifi_id" id="qualifi_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  value="" selected>اختر المهارة</option>
                        @foreach ( $qualifications as $qualification)
                            <option style="direction:rtl;" value="{{ $qualification->id }}">{{ $qualification->name }}</option>
                        @endforeach
                    </select>
                    {{ $qualifi_id }}
                    @error('qualifi_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="studentgenderpref" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المعلم:</label>
                    <select wire:model.live="studentgenderpref" id="studentgenderpref" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;" value="" selected>معلم / معلمة</option>
                        <option style="direction:rtl;" value="">أيهما</option>
                        <option style="direction:rtl;" value="male">معلم</option>
                        <option style="direction:rtl;" value="female">معلمة</option>
                    </select>
                    @error('studentgenderpref')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="teacher_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المعلم:</label>
                    <select wire:model.live="teacher_id" id="teacher_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  selected>اختر المعلم</option>
                        @foreach ( $teachers as $teacher)
                            <option style="direction:rtl;" value="{{ $teacher->id }}">{{ $teacher->name }} : {{ $teacher->teacher->qualifications }}</option>
                        @endforeach
                    </select>
                    @error('teacher_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="classinweek" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عدد الحصص :</label>
                    <input type="text" wire:model.live="classinweek" id="classinweek" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="عدد الحصص" required>
                    @error('classinweek')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <div
                        x-data
                        x-init="flatpickr($refs.datetimewidget, {wrap: true, enableTime: false, dateFormat: 'Y-m-d'});"
                        x-ref="datetimewidget"
                        class="flatpick mx-auto mt-5"
                    >
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ البدء:</label>
                        <div class="flex align-middle align-content-center">
                            <input
                                x-ref="date_time"
                                type="text"
                                id="date_time"
                                wire:model.live="date_time"
                                data-input
                                placeholder="اختر التاريخ "
                                class="block w-full p-2 border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-r-md shadow-sm"
                            >
                            <a
                                class="h-12 w-10 input-button cursor-pointer rounded-l-md bg-slate-300 hover:bg-slate-600 text-slate-600 hover:text-slate-100 border-gray-300 border-t border-b border-l"
                                title="clear" data-clear
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mt-2 ml-1" viewBox="0 0 20 20" fill="#4b4b4b">
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                    @error('date_time')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ايام الاسبوع:</label>
                    @php
                        $curr = 0 ;
                    @endphp
                    @foreach ( $classesdaytime as $index => $classdaytime)
                    <div class="flex items-center mb-4">
                        <label class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">يوم: </label>
                        <select wire:model.live="classesdaytime.{{ $index }}.classweekdays" style="direction:rtl;" class="text-left mr-2 shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">اختر اليوم</option>
                            @foreach ($timebydays as $key =>  $timebyday)
                                <option style="direction:rtl;" value="{{ $key }}" {{ $index == 0 ? 'disabled' : ''}} {{ $classesdaytime[$index]['classweekdays'] == $key ? 'selected' : '' }}>{{ $timebyday }}</option>
                            @endforeach
                        </select>
                        <label class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">الموعد: </label>
                        <select wire:model.live="classesdaytime.{{ $index }}.classweektime" style="direction:rtl;" class="text-left mr-2 shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">اختر الساعة</option>
                            @foreach ($timebyhours as $timebyhour)
                                <option style="direction:rtl;" value="{{ $timebyhour }}" {{ $classesdaytime[$index]['classweektime'] == $timebyhour ? 'selected' : ''}}>{{ $timebyhour }}</option>
                            @endforeach
                        </select>
                        <select wire:model.live="classesdaytime.{{ $index }}.classweektimepmam" style="direction:rtl;" class="text-left mr-2 shadow-sm rounded-md w-28 px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option style="direction:rtl;" value="PM">مساءا</option>
                            <option style="direction:rtl;" value="AM">صباحا</option>
                        </select>
                        @if ($index == 0)
                        <button type="button" disabled class="w-full mr-2 bg-gray-700 text-white font-bold py-2 px-2 rounded align-middle">حذف اليوم</button>
                        @elseif ($classesdaytimenum <= 1)
                            <button type="button" disabled wire:click.prevent="removeclassday({{ $index }})" class="w-full mr-2 bg-gray-700 text-white font-bold py-2 px-2 rounded align-middle">حذف اليوم</button>
                        @else
                            <button type="button" wire:click.prevent="removeclassday({{ $index }})" class="w-full mr-2 bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-2 px-2 rounded align-middle">حذف اليوم</button>
                        @endif
                    </div>
                    @php
                        $curr = $index ;
                    @endphp
                    @endforeach
                    @if ($classesdaytimenum == 7)
                    <div class="flex place-content-end mb-4"></div>
                    @else
                    <div class="flex place-content-end mb-4">
                        <button type="button" {{ $classesdaytimenum == 7 ? 'disabled' : '' }} wire:click.prevent="addclassday({{ $curr + 1 }})" class="w-1/3 mr-2 bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-2 px-2 rounded align-middle">اضافة يوم</button>
                    </div>
                    @endif
                    {{-- {{ var_export($classweekdays) }} --}}
                    {{-- {{ var_export($classweektime) }} --}}
                    {{-- {{ var_export($classweektimepmam) }} --}}
                    {{-- {{ var_export($classesdaytime) }}<br> --}}
                    @foreach ($classesdaytime as $key => $classdaytime)
                        @foreach ($classdaytime as $ukey => $udata)
                            {{ $ukey }} => {{ $udata }} :
                        @endforeach
                        <br>
                    @endforeach
                    {{-- {{ var_export($studentest) }}<br> --}}
                    {{-- {{ var_export($studentgenderpref) }}<br> --}}
                    {{-- {{ var_export($classinweek) }}<br> --}}
                    @error('classesdaytime')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="flex justify-end">
                    <button type="button"  wire:loading.remove wire:click.prevent="addschedule" class="py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">إضافة جدول</button>

                    <x-loading method="addschedule"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('addschedule')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- edit modal --}}
    <x-g-modal name="edit">
        <div class="bg-white dark:bg-yellow-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">تعدبل بيانات الحلقة</h1>
                <button type="button" wire:click.prevent="closemodal('edit')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="edit-class"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="mb-4">
                    <label for="student_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الطالب:</label>
                    <select wire:model.live="student_id" id="student_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  value="{{ $student_id }}" selected>{{ $student_name }}</option>
                        @foreach ( $students as $student)
                            <option style="direction:rtl;" value="{{ $student->id }}">{{ $student->name }}</option>
                        @endforeach
                    </select>
                    @error('student_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="qualifi_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المهارة:</label>
                    <select wire:model.live="qualifi_id" id="qualifi_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  value="" selected>اختر المهارة</option>
                        @foreach ( $qualifications as $qualification)
                            <option style="direction:rtl;" value="{{ $qualification->id }}">{{ $qualification->name }}</option>
                        @endforeach
                    </select>
                    @error('qualifi_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="teacher_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المعلم:</label>
                    <select wire:model.live="teacher_id" id="teacher_id" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  value="{{ $teacher_id }}" selected>{{ $teacher_name }}</option>
                        @foreach ( $teachers as $teacher)
                            <option style="direction:rtl;" value="{{ $teacher->id }}">{{ $teacher->name }}</option>
                        @endforeach
                    </select>
                    @error('teacher_id')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <div
                        x-data
                        x-init="flatpickr($refs.datetimewidget, {wrap: true, enableTime: true, dateFormat: 'Y-m-dTH:i:S'});"
                        x-ref="datetimewidget"
                        class="flatpick mx-auto mt-5"
                    >
                        <label for="date_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">وقت الحصة:</label>
                        <div class="flex align-middle align-content-center">
                            <input
                                x-ref="date_time"
                                type="text"
                                id="date_time"
                                data-input
                                placeholder="اختر التاريخ والوقت للحصة"
                                class="block w-full p-2 border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-r-md shadow-sm"
                                wire:model.live="date_time"
                            >
                            <a
                                class="h-12 w-10 input-button cursor-pointer rounded-l-md bg-slate-300 hover:bg-slate-600 text-slate-600 hover:text-slate-100 border-gray-300 border-t border-b border-l"
                                title="clear" data-clear
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mt-2 ml-1" viewBox="0 0 20 20" fill="#4b4b4b">
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                    @error('date_time')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="start_meeting" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رابط الحصة:</label>
                    <input type="text" wire:model.live="start_meeting" id="start_meeting" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="ادخل رابط الحصة" required>
                    @error('start_meeting')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="join_meeting" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رابط الدخول:</label>
                    <input type="text" wire:model.live="join_meeting" id="join_meeting" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="ادخل رابط الحصة" required>
                    @error('join_meeting')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="flex justify-end">
                    <button type="button"  wire:loading.remove wire:click.prevent="update" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="update"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('edit')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
            @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                <div class="mb-4 text-right flex justify-center">
                    <div dir="ltr" class="text-xl font-bold text-gray-300">
                        {{ $lastupdatedat }} : {{ $lastupdateuser_id }}
                    </div>
                </div>
            @else

            @endif
        </div>
    </x-g-modal>
    {{-- delete modal --}}
    <x-g-modal name="delete">
        <div class="bg-white dark:bg-red-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">حذف الحلقة </h1>
                <button type="button" wire:click.prevent="closemodal('delete')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="delete-class">
                <div class="mb-4">
                    <div class="block text-right text-lg font-bold text-white">هل انت متأكد من حذف المستخدم ؟!</div>
                </div>
                <div class="flex justify-end">
                    <button type="button"  wire:loading.remove wire:click.prevent="delete" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>

                    <x-loading method="delete"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('delete')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- delete selected modal --}}
    <x-g-modal name="deleteselected">
        <div class="bg-white dark:bg-gray-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">حذف الحلقات</h1>
                <button type="button" wire:click.prevent="closemodal('deleteselected')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="delete-selectedteachers">
                @if (empty($selectedclasses))
                    <div class="mb-4">
                        <div class="block text-right text-lg font-bold text-white">لابد من اختيار عناصر حتى يتم حذفهم!</div>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" disabled  wire:loading.remove wire:click.prevent="deleteselected" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 h focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>

                        <x-loading method="delete"></x-loading>

                        <button type="button" wire:click.prevent="closemodal('deleteselected')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                    </div>
                @else
                    <div class="mb-4">
                        <div class="block text-right text-lg font-bold text-white">هل انت متأكد من حذف المستخدمين؟!</div>
                    </div>
                    <div class="flex justify-end">
                        <button type="button"  wire:loading.remove wire:click.prevent="deleteselected" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>

                        <x-loading method="deleteselected"></x-loading>

                        <button type="button" wire:click.prevent="closemodal('deleteselected')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                    </div>
                @endif
            </form>
        </div>
    </x-g-modal>
    {{-- error message modal --}}
    <x-g-modal name="show-error-message">
        <div class="bg-white dark:bg-gray-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">رسالة خطأ ❗</h1>
                <button type="button" wire:click.prevent="closemodal('show-error-message')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="show-error-message">
                <div class="mb-4">
                    <div class="block text-right text-lg font-bold text-white">{{ $errormessage }}</div>
                </div>
                <div class="flex justify-end">
                    <button type="button" wire:click.prevent="closemodal('show-error-message')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>

{{-- reports modals --}}
    {{-- add teacher report modal --}}
    <x-g-modal name="add-teacher-report">
        <div class="bg-white dark:bg-blue-700 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">إضافة تقرير للحلقة</h1>
                <button type="button" wire:click.prevent="closemodal('add-teacher-report')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="add-teacher-report"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="flex justify-between gap-2">
                    <div class="mb-4 w-1/2">
                        <label for="revision_eval" class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">تقدير المراجعة:</label>
                        <select wire:model="revision_eval" name="revision_eval" id="revision_eval" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">اختر التقييم المناسب</option>
                            <option value="ممتاز">ممتاز</option>
                            <option value="جيد جدا">جيد جدا</option>
                            <option value="جيد">جيد</option>
                            <option value="مقبول">مقبول</option>
                        </select>
                        @error('revision_eval')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4 w-1/2">
                        <label for="recite_eval" class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">تقدير التسميع:</label>
                        <select wire:model="recite_eval" name="recite_eval" id="recite_eval" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">اختر التقييم المناسب</option>
                            <option value="ممتاز">ممتاز</option>
                            <option value="جيد جدا">جيد جدا</option>
                            <option value="جيد">جيد</option>
                            <option value="مقبول">مقبول</option>
                        </select>
                        @error('recite_eval')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="mb-4">
                    <div class="mb-4">
                        <label for="reprevision" class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">مراجعة:</label>
                        <textarea wire:model.live="reprevision" name="reprevision" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required></textarea>
                        @error('reprevision')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label for="reprecite" class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">تسميع:</label>
                        <textarea wire:model.live="reprecite" name="reprecite" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required></textarea>
                        @error('reprecite')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="flex justify-start">
                    <div class="mb-4 w-1/2">
                        <label for="recite_eval" class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">تقدير التسميع:</label>
                        <select wire:model="recite_eval" name="recite_eval" id="recite_eval" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">اختر التقييم المناسب</option>
                            <option value="ممتاز">ممتاز</option>
                            <option value="جيد جدا">جيد جدا</option>
                            <option value="جيد">جيد</option>
                            <option value="مقبول">مقبول</option>
                        </select>
                        @error('recite_eval')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="flex justify-end">
                    <button type="button"  wire:loading.remove wire:click.prevent="reportadd('teacherreport')" class="py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="reportadd"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('add-teacher-report')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
        </form>
        </div>
    </x-g-modal>
    {{-- view teacher report modal --}}
    <x-g-modal name="view-teacher-report">
        <div class="bg-white dark:bg-blue-700 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">إضافة تقرير للحلقة</h1>
                <button type="button" wire:click.prevent="closemodal('view-teacher-report')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="view-teacher-report"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="mb-4">
                    <div class="mb-4 flex gap-3">
                        <label for="revision_eval" class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">تقدير المراجعة:</label>
                        <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">{{ $revision_eval }}</span>
                        @error('revision_eval')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4 flex gap-3">
                        <label for="recite_eval" class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">تقدير التسميع:</label>
                        <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">{{ $recite_eval }}</span>
                        @error('recite_eval')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label for="reprevision" class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">مراجعة:</label>
                        <textarea wire:model.live="reprevision" {{ auth()->user()->role == 'teacher' || auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  ? '' : 'disabled' }} name="reprevision" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required></textarea>
                        @error('reprevision')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label for="reprecite" class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">تسميع:</label>
                        <textarea wire:model.live="reprecite" {{ auth()->user()->role == 'teacher' || auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  ? '' : 'disabled' }} name="reprecite" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required></textarea>
                        @error('reprecite')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    @if (auth()->user()->role == 'superadmin' || auth()->user()->role == 'admin' || auth()->user()->role == 'student')
                    <div class="mb-4">
                        <label for="class_rate" class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">تقييمك للحلقة:</label>
                        @if ($class_rated)
                        <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">{{ $class_rate }} / 5</span>
                        @else
                        <select wire:model="class_rate" name="class_rate" id="class_rate" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">اختر التقييم المناسب</option>
                            <option value=5>جيدة جدا</option>
                            <option value=4>جيدة</option>
                            <option value=3>مقبولة</option>
                            <option value=2>سيئة</option>
                            <option value=1>سيئة جدا</option>
                        </select>
                        @endif
                        @error('class_rate')
                            <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                        @enderror
                    </div>
                    @endif
                </div>
                <div class="flex justify-end">
                    @if (auth()->user()->role == 'teacher' || auth()->user()->role == 'admin' || auth()->user()->role == 'superadmin')
                    <button type="button"  wire:loading.remove wire:click.prevent="reportupdate('teacherreport')" class="py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="reportupdate"></x-loading>

                    @elseif (auth()->user()->role == 'student')
                    <button type="button"  wire:loading.remove wire:click.prevent="addrate({{ auth()->user()->id }}, {{ $class_id }}, {{ $teacher_id }})" class="py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="addrate"></x-loading>

                    @endif
                    <button type="button" wire:click.prevent="closemodal('view-teacher-report')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- add supervisor report modal --}}
    <x-g-modal name="add-supervisor-report">
        <div class="bg-white dark:bg-green-700 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">إضافة تقرير للحلقة</h1>
                <button type="button" wire:click.prevent="closemodal('add-supervisor-report')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="add-supervisor-report"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="flex justify-between gap-2">
                    <div class="w-1/2">
                        <label class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">التقييم الفني</label>
                        <div class="mb-4">
                            <label for="internet" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">جودة الانترنت:</label>
                            <select wire:model.live="internet" id="internet" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;" value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('internet')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="camera" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">استخدام الكاميرة:</label>
                            <select wire:model.live="camera" id="camera" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;" value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('camera')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="smartboard" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">استخدام السمارت بورد:</label>
                            <select wire:model.live="smartboard" id="smartboard" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;" value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('smartboard')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="audio" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">جودة الصوت:</label>
                            <select wire:model.live="audio" id="audio" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;" value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('audio')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="w-1/2">
                        <label class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">التقييم المهني</label>
                        <div class="mb-4">
                            <label for="teachbackground" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">خلفية المعلم وهيئته:</label>
                            <select wire:model.live="teachbackground" id="teachbackground" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;" value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('teachbackground')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="teachenvironment" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">مكان المعلم اثناء الحلقة:</label>
                            <select wire:model.live="teachenvironment" id="teachenvironment" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;" value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('teachenvironment')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="teachrelationship" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">الآداء المعني للمعلم مع الطالب:</label>
                            <select wire:model.live="teachrelationship" id="teachrelationship" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;" value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('teachrelationship')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="teacheducational" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">الآداء التعليمي للمعلم:</label>
                            <select wire:model.live="teacheducational" id="teacheducational" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;" value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('teacheducational')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                <div class="mb-4">
                    <label for="notes" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات:</label>
                    <textarea wire:model.live="notes" name="notes" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required></textarea>
                    @error('notes')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="flex justify-end">
                    <button type="button"  wire:loading.remove wire:click.prevent="reportadd('supervisorreport')" class="py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="reportadd"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('add-supervisor-report')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- view supervisor report modal --}}
    <x-g-modal name="view-supervisor-report">
        <div class="bg-white dark:bg-green-700 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">إضافة تقرير للحلقة</h1>
                <button type="button" wire:click.prevent="closemodal('view-supervisor-report')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="view-supervisor-report"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="flex justify-between gap-2">
                    <div class="w-1/2">
                        <label class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">التقييم الفني</label>
                        <div class="mb-4">
                            <label for="internet" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">جودة الانترنت:</label>
                            <select wire:model.live="internet" id="internet" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;" {{ auth()->user()->role !== 'admin' ? 'disabled' : ''  }} value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('internet')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="camera" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">استخدام الكاميرة:</label>
                            <select wire:model.live="camera" id="camera" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;"{{ auth()->user()->role !== 'admin' ? 'disabled' : ''  }} value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('camera')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="smartboard" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">استخدام السمارت بورد:</label>
                            <select wire:model.live="smartboard" id="smartboard" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;"{{ auth()->user()->role !== 'admin' ? 'disabled' : ''  }} value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('smartboard')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="audio" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">جودة الصوت:</label>
                            <select wire:model.live="audio" id="audio" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;"{{ auth()->user()->role !== 'admin' ? 'disabled' : ''  }} value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('audio')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="w-1/2">
                        <label class="block text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">التقييم المهني</label>
                        <div class="mb-4">
                            <label for="teachbackground" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">خلفية المعلم وهيئته:</label>
                            <select wire:model.live="teachbackground" id="teachbackground" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;"{{ auth()->user()->role !== 'admin' ? 'disabled' : ''  }} value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('teachbackground')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="teachenvironment" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">مكان المعلم اثناء الحلقة:</label>
                            <select wire:model.live="teachenvironment" id="teachenvironment" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;"{{ auth()->user()->role !== 'admin' ? 'disabled' : ''  }} value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('teachenvironment')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="teachrelationship" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">الآداء المعني للمعلم مع الطالب:</label>
                            <select wire:model.live="teachrelationship" id="teachrelationship" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;"{{ auth()->user()->role !== 'admin' ? 'disabled' : ''  }} value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('teachrelationship')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="teacheducational" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">الآداء التعليمي للمعلم:</label>
                            <select wire:model.live="teacheducational" id="teacheducational" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                                <option style="direction:rtl;" value="" selected>اختر التقييم المناسب</option>
                                @for ($i=10;$i>=0;$i--)
                                    <option style="direction:rtl;"{{ auth()->user()->role !== 'admin' ? 'disabled' : ''  }} value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                            @error('teacheducational')
                                <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                <div class="mb-4">
                    <label for="notes" class="block text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات:</label>
                    <textarea wire:model.live="notes" name="notes" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required></textarea>
                    @error('notes')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="flex justify-between">
                    <span class="block text-lg font-bold text-gray-700 dark:text-gray-300">مجموع التقييم: {{ $this->totalevaluation() ?? 0 }} %</span>
                    <div class="flex justify-end">
                        <button type="button"  wire:loading.remove wire:click.prevent="reportupdate('supervisorreport')" class="py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                        <x-loading method="reportupdate"></x-loading>

                        <button type="button" wire:click.prevent="closemodal('view-supervisor-report')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                    </div>
                </div>
            </form>
        </div>
    </x-g-modal>

{{-- flash messages modals --}}
    <!-- flash save successful message -->
    <x-flash-message on="flashsaved">
        <div class="flex items-center bg-green-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تسجيل العنصر بنجاح.</p>
        </div>
    </x-flash-message>
    <!-- flash update successful message -->
    <x-flash-message on="flashupdated">
        <div class="flex items-center bg-yellow-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تعديل العنصر بنجاح.</p>
        </div>
    </x-flash-message>
    <!-- flash delete successful message -->
    <x-flash-message on="flashdeleted">
        <div class="flex items-center bg-red-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم حذف العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    {{-- data tables --}}
    <div class="card">
        <header class="flex justify-between card-header noborder">
          <h4 class="text-primary-500 font-bold text-3xl">إدارة الحلقات</h4>
          <div class="flex justify-end">
            <a href="class" class="flex place-items-center gap-2 text-[#016241] text-xl hover:text-gray-500">
                تحديث البيانات
                <iconify-icon icon="heroicons:arrow-path-rounded-square-16-solid" style="font-size: 36px;color:#016241;font-weight: bold;" alt="حذف"></iconify-icon>
            </a>
          </div>
        </header>
        <div x-data="{open: false, shown: true }" x-defer class="card-body px-6 pb-6">
          <div class="sm:grid sm:my-2 md:flex sm:place-content-center md:justify-between w-auto mr-0">
            {{-- control buttons --}}
            <div class="grid sm:grid-cols-1 lg:grid-cols-6 gap-3">
                {{-- add modal button --}}
                @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  || auth()->user()->role == 'moderator')
                <button type="button" x-data="" x-on:click.prevent="$dispatch('open-modal', 'add')" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg align-middle flex">
                    <div class="flex place-items-center">
                    <iconify-icon icon="heroicons:plus-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                     اضافة حصة
                    </div>
                </button>
                {{-- add modal button --}}
                <button type="button" x-data="" x-on:click.prevent="$dispatch('open-modal', 'addschedule')" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg align-middle flex">
                    <div class="flex place-items-center">
                    <iconify-icon icon="heroicons:plus-circle" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                     اضافة جدول
                    </div>
                </button>
                {{-- delete selected modal button --}}
                <button type="button"  wire:click.prevent="showdeleteselected"  class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg align-middle flex">
                    <div class="flex place-items-center">
                    <iconify-icon icon="heroicons:trash-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="حذف"></iconify-icon>
                    حذف المحددين
                    </div>
                </button>
                @endif
                {{-- advanced section modal button --}}
                <button type="button" x-on:click="open = ! open" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg align-middle flex">
                    <div x-show="! open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اظهار البحث الــمتقدم
                    </div>
                    <div x-show="open" class="flex place-items-center">
                        <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اخفاء البحث الــمتقدم
                    </div>
                </button>
                @if (!empty($filterdatestart) || !empty($filterdateend) || !empty($filterteacher) || !empty($filteractive))
                    <button wire:click.prevent="resetallfilter" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg align-middle flex">
                        <div class="flex place-items-center">
                            <iconify-icon icon="heroicons:arrow-uturn-down-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                            إعادة تعيين التصفية
                        </div>
                    </button>
                @endif
                {{-- FullCalendar button --}}
                @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  || auth()->user()->role == 'moderator' || auth()->user()->role == 'supervisor' || auth()->user()->role == 'teacher' || auth()->user()->role == 'student')
                <button type="button" x-on:click="shown = ! shown" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg align-middle flex">
                    <div x-show="! shown" class="flex place-items-center">
                        <iconify-icon icon="heroicons:calendar-days" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اظهار الجدول بالتقويم
                    </div>
                    <div x-show="shown" class="flex place-items-center">
                        <iconify-icon icon="heroicons:calendar-days" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                        اخفاء الجدول بالتقويم
                    </div>
                </button>
                @endif
            </div>
            {{-- search field --}}
            <div class="my-3 sm:w-auto sm:mt-0 sm:ml-auto md:ml-0">
                <div class="relative flex w-80 text-gray-700 mr-auto my-auto place-items-center">
                    @if (!empty($search))
                        <button type="button" wire:click.prevent="resetsearch" class="absolute inset-y-0 left-0 pl-3 flex items-center z-1">
                            <iconify-icon icon="heroicons:x-mark-20-solid" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                        </button>
                    @endif
                    <iconify-icon icon="heroicons:magnifying-glass-solid" style="font-size: 24px;color:#016241;" alt="تعديل" class="ml-2"></iconify-icon>
                    <input wire:model.live.debounce.500="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-lg rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="بحث..." wire:ignore>
                </div>
            </div>
          </div>
          {{-- advanced filter by status and teacher --}}
          <div x-show="open" class="grid sm:grid-cols-1 lg:grid-cols-2 place-items-center">
            <div class="flex w-full place-items-center my-2">
                <label for="filterteacher" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">المعلم: </label>
                <select name="filterteacher" wire:model.live="filterteacher" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option style="direction:rtl;" value="" selected>اختر البند الرئيسي</option>
                    @foreach ( $teachers as $teacher )
                        <option style="direction:rtl;" value="{{ $teacher->id }}">{{ $teacher->name }}</option>
                    @endforeach
                </select>
                @if (!empty($filterteacher))
                <button type="button" wire:click.prevent="resetfilter('filterteacher')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                    <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                </button>
                @endif
            </div>
            <div class="flex w-full place-items-center my-2 mr-2">
                <label for="filteractive" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mb-2 ml-2">الحالة:</label>
                <select name="filteractive" wire:model.live="filteractive" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option style="direction:ltr;" value="" selected>اختر الحالة</option>
                    <option style="direction:ltr;" value=false>متاح</option>
                    <option style="direction:ltr;" value=true>غير متاح</option>
                    <option style="direction:ltr;" value="ended">منتهية</option>
                </select>
                @if (!empty($filteractive))
                <button type="button" wire:click.prevent="resetfilter('filteractive')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                    <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                </button>
                @endif
            </div>
          </div>
          {{-- advanced date filter --}}
          <div x-show="open" class="grid sm:grid-cols-1 lg:grid-cols-3 gap-3 place-items-center my-2">
            <div class="flex w-full place-items-center">
                <label for="filterdatestart" class="block text-wrap text-lg font-medium text-gray-700 dark:text-[#016241] ml-2">من: </label>
                <input type="date" id="filterdatestart" wire:model.live="filterdatestart" value="{{ $filterdatestart }}" name="filterdatestart" min="2023-01-01" max="2050-12-31" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
                @if (!empty($filterdatestart))
                <button type="button" wire:click.prevent="resetfilter('filterdatestart')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 mr-2 p-1 dark:hover:bg-slate-600 dark:hover:text-white">
                    <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                </button>
                @endif
            </div>
            <div class="flex w-full place-items-center my-2">
                <label for="filterdateend" class="block text-lg font-medium text-gray-700 dark:text-[#016241] ml-2 lg:mx-2">حتى: </label>
                <input type="date" id="filterdateend" wire:model.live="filterdateend" value="{{ $filterdateend }}" name="filterdateend" min="2023-01-01" max="2050-12-31" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
                @if (!empty($filterdateend))
                <button type="button" wire:click.prevent="resetfilter('filterdateend')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 mr-2 p-1 dark:hover:bg-slate-600 dark:hover:text-white">
                    <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                </button>
                @endif
            </div>
            <div class="grid sm:grid-cols-1 lg:grid-cols-3 gap-3 w-full">
                {{-- filter day/week/month button --}}
                <button type="button" wire:click.prevent="filterbyday" class="{{ $filterby == 'today' ? 'bg-[#b36700]' : 'bg-[#906842]' }} hover:bg-[#b36700] text-white font-bold py-4 px-4 rounded-lg place-content-center">
                    <iconify-icon icon="heroicons:calendar-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    حلقات اليوم
                </button>
                <button type="button" wire:click.prevent="filterbyweek" class="{{ $filterby == 'week' ? 'bg-[#b36700]' : 'bg-[#906842]' }} hover:bg-[#b36700] text-white font-bold py-4 px-4 rounded-lg place-content-center">
                    <iconify-icon icon="heroicons:calendar-days-16-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    حلقات الاسبوع
                </button>
                <button type="button" wire:click.prevent="filterbymonth" class="{{ $filterby == 'month' ? 'bg-[#b36700]' : 'bg-[#906842]' }} hover:bg-[#b36700] text-white font-bold py-4 px-4 rounded-lg align-middle mr-0 flex place-content-center">
                    <iconify-icon icon="heroicons:calendar-days-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                    حلقات الشهر
                </button>
            </div>
          </div>
          {{-- fullcalendar --}}
        @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  || auth()->user()->role == 'moderator' || auth()->user()->role == 'supervisor' || auth()->user()->role == 'teacher' || auth()->user()->role == 'student')
          <div x-show="shown" class="block mx-auto w-full h-1/2">
            <div wire:ignore>
                <div id="calendar" class="w-full" wire:ignore style="height: 500px;"></div>
            </div>
          </div>
        @endif
          {{-- data table --}}
          <div class="overflow-x-auto -mx-6">
            <div class="inline-block min-w-full align-middle">
              <div class="overflow-hidden">
                <div class="text-lg font-medium text-white">{{ $filterdatestart }}, {{ $filterdateend }}</div>
                @unless ($classes->count() == 0)
                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl">
                  <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                    <tr>
                        @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  || auth()->user()->role == 'moderator')
                      <th scope="col" class=" table-th text-xl font-medium"><input type="checkbox" wire:model.live="selectall" class="border-gray-300 rounded bg-gray-50 borded ml-1"> الكل </th>
                      @endif
                      @if (auth()->user()->role !== 'student')
                        <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('username')">اسم الطالب</a></th>
                      @endif
                      @if (auth()->user()->role !== 'teacher')
                          <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('name')">اسم المعلم</a></th>
                      @endif
                        <th scope="col" class=" table-th text-xl font-medium">المهارة</th>
                        <th scope="col" class=" table-th text-xl font-medium">وقت الحصة</th>
                      @if (auth()->user()->role == 'supervisor' || auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                        <th scope="col" class=" table-td text-xl font-medium text-[#cbd5e1]">ت . الاشراف</th>
                      @endif
                      @if (auth()->user()->role == 'student' || auth()->user()->role == 'teacher' || auth()->user()->role == 'supervisor' || auth()->user()->role == 'moderator' || auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                        <th scope="col" class=" table-td text-xl font-medium text-[#cbd5e1]">ت . التدريس</th>
                      @endif
                      @if (auth()->user()->role == 'teacher' || auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                        <th scope="col" class=" table-td text-xl font-medium text-[#cbd5e1]">دخول الحلقة</th>
                      @endif
                      @if (auth()->user()->role == 'student' || auth()->user()->role == 'supervisor' || auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                        <th scope="col" class=" table-td text-xl font-medium text-[#cbd5e1]">دخول الحلقة</th>
                      @endif
                        <th scope="col" class=" table-td text-xl font-medium text-[#cbd5e1]">الحالة</th>
                        @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  || auth()->user()->role == 'moderator')
                        <th scope="col" class=" table-th text-xl font-medium ">أجراءات</th>
                        @endif
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                    @foreach ($classes as $class)
                    <tr wire:key="class-{{ $class->id }}">
                        @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  || auth()->user()->role == 'moderator')
                        <td class="table-td text-xl font-normal"><input type="checkbox" wire:model.live="selectedclasses" value="{{ $class->id }}" class="border-gray-300 rounded bg-gray-50 borded"></td>
                        @endif
                      @if (auth()->user()->role !== 'student')
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $class->student->name }}</span>
                      </td>
                      @endif
                      @if (auth()->user()->role !== 'teacher')
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $class->teacher->name ?? '' }}</span>
                      </td>
                      @endif
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $class->qualification->name ?? '' }}</span>
                      </td>
                      <td class="table-td text-xl font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">{{ $this->gettimezone($class->date_time, auth()->user()->id) }}</span>
                      </td>
                      @if (auth()->user()->role == 'supervisor' || auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                      <td class="table-td text-sm font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize">
                            @if (!empty($class->supreport->class_id))
                            <button type="button" wire:click.prevent="suprepview({{ $class }})" class="bg-yellow-500 hover:bg-yellow-700 text-slate-900 text-sm font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">عرض التقرير</a>
                            @else
                            <button type="button" wire:click.prevent="suprepadd({{ $class }})" class="bg-[#016241] hover:bg-[#b36700] text-white hover:text-[#016241] text-sm font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">اضافة تقرير</a>
                            @endif
                        </span>
                      </td>
                      @endif
                      @if (auth()->user()->role == 'student' || auth()->user()->role == 'teacher' || auth()->user()->role == 'supervisor' || auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                      <td class="table-td text-sm font-normal">
                        <span class=" text-slate-600 dark:text-white capitalize w-full flex justify-content-center">
                            @if (!empty($class->teachreport->class_id))
                                <button type="button" wire:click.prevent="tearepview({{ $class }})" class="bg-yellow-500 hover:bg-yellow-700 text-slate-900 text-sm font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">عرض التقرير</a>
                            @else
                                @if (auth()->user()->role == 'supervisor' || auth()->user()->role == 'moderator' || auth()->user()->role == 'student')
                                    <span class=" text-slate-600 dark:text-white text-lg capitalize mx-auto">لا يوجد تقرير</span>
                                @else
                                    <button type="button" wire:click.prevent="tearepadd({{ $class }})" class="bg-[#016241] hover:bg-[#b36700] text-white hover:text-[#016241] text-sm font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">اضافة تقرير</a>
                                @endif
                            @endif
                        </span>
                      </td>
                      @endif
                      @if (auth()->user()->role == 'teacher' || auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                      <td class="table-td text-sm font-normal">
                        @if ($class->is_active == 'not')
                            <button type="button"  wire:loading.remove wire:click.prevent="goteacher('{{ $class->start_meeting }}', '{{ $class->id }}', '{{ $class->teacher_id }}', '{{ $class->student_id }}', '{{ auth()->user()->id }}', '{{ auth()->user()->role }}')" class="bg-[#016241] hover:bg-[#b36700] text-white hover:text-[#016241] text-sm font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">بدء الحصة</button>

                            <x-loading method="goteacher"></x-loading>

                        @elseif ($class->is_active == 'active')
                            <button type="button"  wire:loading.remove wire:click.prevent="stopteacher('{{ $class->id }}', '{{ $class->teacher_id }}', '{{ $class->student_id }}', '{{ auth()->user()->id }}')" class="bg-[#016241] hover:bg-[#b36700] text-white hover:text-[#016241] text-sm font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">انهاء الحصة</button>

                            <x-loading method="stopteacher"></x-loading>

                        @else
                            <button type="button" disabled class="bg-[#b36700] text-[#016241] text-sm font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">حصة منتهية</button>
                        @endif
                      </td>
                      @endif
                      @if (auth()->user()->role == 'student' || auth()->user()->role == 'supervisor' || auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin' )
                      <td class="table-td text-sm font-normal">
                        @if ($class->is_active == 'not')
                            <button type="button" disabled class="bg-[#016241] hover:bg-[#b36700] text-white hover:text-[#016241] text-sm font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">لم تبدأ الحصة</button>
                        @elseif ($class->is_active == 'active')
                            @if ($this->checkloggedin($class->id))
                                @if ($this->checkloggedout($class->id))
                                    <button type="button" disabled class="bg-black hover:bg-slate-700 text-white font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">تم الخروج من الحصة</button>
                                @else
                                    <button type="button"  wire:loading.remove wire:click.prevent="stopstudent('{{ $class->id }}', '{{ $class->teacher_id }}', '{{ $class->student_id }}', '{{ auth()->user()->id }}')" class="bg-black hover:bg-slate-700 text-white font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">خروج من الحصة</button>

                                    <x-loading method="stopstudent"></x-loading>

                                @endif
                            @else
                                <button type="button"  wire:loading.remove wire:click.prevent="gostudent('{{ $class->join_meeting }}', '{{ $class->id }}', '{{ $class->teacher_id }}', '{{ $class->student_id }}', '{{ auth()->user()->id }}', '{{ auth()->user()->role }}')" class="bg-[#016241] hover:bg-[#ffb44f] text-white hover:text-[#016241] font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">دخول الحصة</button>

                                <x-loading method="gostudent"></x-loading>

                            @endif
                        @else
                            <button type="button" disabled class="bg-[#016241] hover:bg-[#ffb44f] text-white hover:text-[#016241] font-bold mx-auto py-2 px-2 rounded align-middle flex place-content-center">حصة منتهية</button>
                        @endif
                      </td>
                      @endif
                      <td class=" text-center text-xl font-medium text-[#cbd5e1]">
                        @if ($class->is_active === 'active')
                            @if ($this->checkwhoactive($class->teacher_id, $class->student_id) === 'teacher')
                                <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white bg-info-500 border border-red-900}}">
                                    معلم فقط
                            @elseif ($this->checkwhoactive($class->teacher_id, $class->student_id) === 'both')
                                <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white bg-danger-500 border border-red-900}}">
                                    تم التواصل
                            @else
                                <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white bg-info-500 border border-red-900}}">
                                    ادمن فقط
                            @endif
                        @elseif ($class->is_active === 'ended')
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border-[#016241] border text-gray-300 bg-success-500">
                                منتهية
                        @else
                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 border-[#016241] border text-green-500 bg-success-500">
                                متاح
                        @endif
                        </div>
                      </td>
                      @if (auth()->user()->role == 'admin'  || auth()->user()->role == 'superadmin'  || auth()->user()->role == 'moderator')
                      <td class="table-td  text-xl font-normal">
                        <div class="flex space-x-3 rtl:space-x-reverse">
                          <button type="button" wire:click.prevent="showedit({{ $class }})">
                            <iconify-icon icon="heroicons:pencil-square" style="font-size: 24px;color:#016241;font-weight:bold;" alt="تعديل"></iconify-icon>
                          </button>
                          <button type="button" wire:click.prevent="showdelete({{ $class }})">
                            <iconify-icon icon="heroicons:trash" style="font-size: 24px;color:#016241;font-weight:bold;" alt="حذف"></iconify-icon>
                          </button>
                        </div>
                      </td>
                      @endif
                    </tr>
                    @endforeach
                  </tbody>
                </table>
                <div class="flex my-4 justify-between font-cairo text-xl text-[#016241]">
                    @if (auth()->user()->role == 'student')
                    <div class="w-full">
                        <div class="flex justify-center">
                            اجمالي الحلقات: {{ $this->studentnumofclasses() }}
                        </div>
                    </div>
                    <div class="w-full">
                        <div class="flex justify-center">
                            الحلقات المتبقية: {{ $this->studentbalance() }}
                        </div>
                    </div>
                    @endif
                </div>
                <div class="flex my-4 justify-center font-cairo text-xl text-[#016241]">
                    {{ $defaulttimezone }}
                </div>
                @else
                <div class="text-center text-xl font-bold text-[#016241]">لا يوجد حلقات حتى الآن.</div>
                @endunless
                <div class="text-center text-xl font-bold text-[#016241]">
                    @foreach ($selectedclasses as $selectedclass)
                    {{ $selectedclass }},
                    @endforeach
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
<div class="flex my-4">
    <div class="flex justify-start w-[50%] items-center">
        <label for="perpage" class="text-lg font-medium text-gray-700 dark:text-[#be9539]">عدد النتائج في كل صفحة:</label>
        <select wire:model.live="perpage" id="perpage" style="direction:ltr;" class="w-20 shadow-sm rounded-md mx-4 px-5 py-1 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            @foreach ($perpageoptions as $option)
            <option style="direction:rtl;" value="{{ $option }}">{{ $option === 'all' ? 'الكل' : $option }}</option>
            @endforeach
        </select>
    </div>
    @if ($perpage === 'all')

    @else
    <div class="w-[100%] mr-4">{{ $classes->links('vendor.livewire.tailwind') }}</div>
    @endif
</div>

@push('scripts')
<style>
    .fc-daygrid-day-events, .fc-event
    {
        text-wrap: wrap;
        margin-bottom: 4px;
        text-align: right;
    }
    .fc-daygrid-day-events
    {
        margin: 8px;
    }
    .fc-col-header-cell
    {
        background-color: red;
        color:red;
    }
    .fc-button
    {
        color: #016241;
    }
    .fc-button-active
    {
        background-color: #016241;
        color: #ffffff;
    }
    .fc-button-primary
    {
        color:red;
    }
</style>
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.11/index.global.min.js'></script>
<script>
    document.addEventListener('livewire:navigated', () => {
        var calendarEl = document.getElementById('calendar');
        var calendar = new FullCalendar.Calendar(calendarEl, {
            themeSystem: 'standard',
            height: 650,
            buttonText: {
                today:    'اليوم',
                month:    'الشهر',
                week:     'الاسبوع',
                day:      'اليوم',
                list:     'قائمة'
            },
            locale: 'ar',
            initialView: 'timeGridWeek',
            dateClick: function (info) {
                calendar.changeView('timeGridDay', info.dateStr);
            },
            headerToolbar: {
            left: 'prev,next dayGridMonth',
            center: 'title',
            right: 'timeGridWeek,timeGridDay,listWeek'
            },
            slotMinTime: '00:00:00',
            slotMaxTime: '23:30:00',
            slotDuration: '00:30:00',
            events: @json($events),
        });
        calendar.render();
    })
    window.addEventListener('open-new-window', function(event){
        // console.log(typeof event.detail[0]);
        const url = event.detail[0];
        window.open(url, '_blank');
    })
</script>
@endpush
</div>
