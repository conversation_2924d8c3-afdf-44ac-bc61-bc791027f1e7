@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&amp;display=swap");
/*
! tailwindcss v3.2.4 | MIT License | https://tailwindcss.com
*/
/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/
*,
::before,
::after {
    box-sizing: border-box;
 /* 1 */
    border-width: 0;
 /* 2 */
    border-style: solid;
 /* 2 */
    border-color: #E5E7EB;
 /* 2 */
}

::before,
::after {
    --tw-content: '';
}
/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
*/
html {
    line-height: 1.5;
 /* 1 */
    -webkit-text-size-adjust: 100%;
 /* 2 */
    -moz-tab-size: 4;
 /* 3 */
    tab-size: 4;
 /* 3 */
    font-family: Inter, sans-serif;
 /* 4 */
    font-feature-settings: normal;
 /* 5 */
}
/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/
body {
    margin: 0;
 /* 1 */
    line-height: inherit;
 /* 2 */
}
/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/
hr {
    height: 0;
 /* 1 */
    color: inherit;
 /* 2 */
    border-top-width: 1px;
 /* 3 */
}
/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr:where([title]) {
    text-decoration: underline dotted;
}
/*
Remove the default font size and weight for headings.
*/
h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit;
}
/*
Reset links to optimize for opt-in styling instead of opt-out.
*/
a {
    color: inherit;
    text-decoration: inherit;
}
/*
Add the correct font weight in Edge and Safari.
*/
b,
strong {
    font-weight: bolder;
}
/*
1. Use the user's configured `mono` font family by default.
2. Correct the odd `em` font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
  font-family: ui-monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}
/*
Add the correct font size in all browsers.
*/
small {
  font-size: 80%;
}
/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/
table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}
/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}
/*
Remove the inheritance of text transform in Edge and Firefox.
*/
button,
select {
  text-transform: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/
button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}
/*
Use the modern Firefox focus style for all focusable elements.
*/
:-moz-focusring {
  outline: auto;
}
/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/
:-moz-ui-invalid {
  box-shadow: none;
}
/*
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
  vertical-align: baseline;
}
/*
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}
/*
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}
/*
Add the correct display in Chrome and Safari.
*/
summary {
  display: list-item;
}
/*
Removes the default spacing and border for appropriate elements.
*/
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}
fieldset {
  margin: 0;
  padding: 0;
}
legend {
  padding: 0;
}
ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
/*
Prevent resizing textareas horizontally by default.
*/
textarea {
  resize: vertical;
}
/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/
input::placeholder,
textarea::placeholder {
    opacity: 1;
 /* 1 */
    color: #9FA6B2;
 /* 2 */
}
/*
Set the default cursor for buttons.
*/
button,
[role="button"] {
    cursor: pointer;
}
/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}
/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}
/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/
img,
video {
  max-width: 100%;
  height: auto;
}
/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden] {
  display: none;
}
*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}
::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}
.container{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 15px;
  padding-left: 15px;
}
.\!container{
  width: 100% !important;
  margin-right: auto !important;
  margin-left: auto !important;
  padding-right: 15px !important;
  padding-left: 15px !important;
}
@media (min-width: 640px){
  .container{
    max-width: 640px;
    padding-right: 15px;
    padding-left: 15px;
  }
  .\!container{
    max-width: 640px !important;
    padding-right: 15px !important;
    padding-left: 15px !important;
  }
}
@media (min-width: 768px){
  .container{
    max-width: 768px;
  }
  .\!container{
    max-width: 768px !important;
  }
}
@media (min-width: 1024px){
  .container{
    max-width: 1024px;
    padding-right: 15px;
    padding-left: 15px;
  }
  .\!container{
    max-width: 1024px !important;
    padding-right: 15px !important;
    padding-left: 15px !important;
  }
}
@media (min-width: 1280px){
  .container{
    max-width: 1280px;
    padding-right: 15px;
    padding-left: 15px;
  }
  .\!container{
    max-width: 1280px !important;
    padding-right: 15px !important;
    padding-left: 15px !important;
  }
}
.aspect-w-1{
  position: relative;
  padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
  --tw-aspect-w: 1;
}
.aspect-w-1 > *{
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.aspect-h-2{
  --tw-aspect-h: 2;
}
.aspect-w-3{
  position: relative;
  padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
  --tw-aspect-w: 3;
}
.aspect-w-3 > *{
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.aspect-h-4{
  --tw-aspect-h: 4;
}
:root{
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #dc3545;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffc107;
  --bs-green: #198754;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-white: #fff;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-gray-100: #f8f9fa;
  --bs-gray-200: #e9ecef;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-primary-rgb: 13, 110, 253;
  --bs-secondary-rgb: 108, 117, 125;
  --bs-success-rgb: 25, 135, 84;
  --bs-info-rgb: 13, 202, 240;
  --bs-warning-rgb: 255, 193, 7;
  --bs-danger-rgb: 220, 53, 69;
  --bs-light-rgb: 248, 249, 250;
  --bs-dark-rgb: 33, 37, 41;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-body-color-rgb: 33, 37, 41;
  --bs-body-bg-rgb: 255, 255, 255;
  --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --bs-body-font-family: var(--bs-font-sans-serif);
  --bs-body-font-size: 1rem;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.5;
  --bs-body-color: #212529;
  --bs-body-bg: #fff;
}
.form-control[type=file]{
  overflow: hidden;
}
.form-control[type=file]:not(:disabled):not([readonly]){
  cursor: pointer;
}
.form-control:focus{
  box-shadow: 0 0 0 1px rgb(37, 99, 235);
}
.form-control::file-selector-button{
  padding: 0.375rem 0.75rem;
  margin: -0.375rem -0.75rem;
  margin-inline-end: 0.75rem;
  color: #212529;
  background-color: #e9ecef;
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button{
  background-color: #dde0e3;
}
.form-control::-webkit-file-upload-button{
  padding: 0.375rem 0.75rem;
  margin: -0.375rem -0.75rem;
  margin-inline-end: 0.75rem;
  color: rgb(55, 65, 81);
  background-color: rgb(243, 244, 246);
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button{
  background-color: #dde0e3;
}
.form-select{
  -moz-padding-start: calc(0.75rem - 3px);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-size: 16px 12px;
}
.form-select:focus{
  box-shadow: 0 0 0 1px rgb(37, 99, 235);
}
.form-select[multiple]{
  padding-right: 0.75rem;
  background-image: none;
}
.form-select[size]:not([size="1"]){
  padding-right: 0.75rem;
  background-image: none;
}
.form-select:disabled{
  background-color: #e9ecef;
}
.form-select:-moz-focusring{
  color: transparent;
  -webkit-text-shadow: 0 0 0 #212529;
  text-shadow: 0 0 0 #212529;
}
.btn-check[disabled] + .btn{
  pointer-events: none;
  filter: none;
  opacity: 0.65;
}
.btn-check:disabled + .btn{
  pointer-events: none;
  filter: none;
  opacity: 0.65;
}
.form-floating > .form-control{
  height: calc(3.5rem + 2px);
  line-height: 1.25;
  padding: 1rem 0.75rem;
}
.form-floating > .form-select{
  height: calc(3.5rem + 2px);
  line-height: 1.25;
}
.form-floating > .form-control::-webkit-input-placeholder{
  color: transparent;
}
.form-floating > .form-control::-moz-placeholder{
  color: transparent;
}
.form-floating > .form-control:-ms-input-placeholder{
  color: transparent;
}
.form-floating > .form-control::placeholder{
  color: transparent;
}
.form-floating > .form-control:focus{
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:not(:placeholder-shown){
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:-webkit-autofill{
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:focus ~ label{
  opacity: 0.65;
  -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  -ms-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:not(:placeholder-shown) ~ label{
  opacity: 0.65;
  -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  -ms-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-select ~ label{
  opacity: 0.65;
  -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  -ms-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.input-group > .form-control{
  width: 1%;
}
.input-group > .form-select{
  width: 1%;
}
.input-group > .form-control:focus{
  z-index: 3;
}
.input-group > .form-select:focus{
  z-index: 3;
}
.input-group .btn{
  position: relative;
  z-index: 2;
}
.input-group .btn:focus{
  z-index: 3;
}
.input-group-lg > .form-select{
  padding-right: 3rem;
}
.input-group-sm > .form-select{
  padding-right: 3rem;
}
.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu){
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3){
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu){
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group.has-validation > .dropdown-toggle:nth-last-child(n+4){
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback){
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.was-validated .form-control:valid{
  border-color: #198754;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.form-control.is-valid{
  border-color: #198754;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-control:valid:focus{
  border-color: #198754;
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}
.form-control.is-valid:focus{
  border-color: #198754;
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}
.was-validated textarea.form-control:valid{
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
textarea.form-control.is-valid{
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
.was-validated .form-select:valid{
  border-color: #198754;
}
.form-select.is-valid{
  border-color: #198754;
}
.was-validated .form-select:valid:not([multiple]):not([size]){
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-select:valid:not([multiple])[size="1"]{
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.form-select.is-valid:not([multiple]):not([size]){
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.form-select.is-valid:not([multiple])[size="1"]{
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-select:valid:focus{
  border-color: #198754;
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}
.form-select.is-valid:focus{
  border-color: #198754;
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}
.was-validated .input-group .form-control:valid{
  z-index: 1;
}
.input-group .form-control.is-valid{
  z-index: 1;
}
.was-validated .input-group .form-select:valid{
  z-index: 1;
}
.input-group .form-select.is-valid{
  z-index: 1;
}
.was-validated .input-group .form-control:valid:focus{
  z-index: 3;
}
.input-group .form-control.is-valid:focus{
  z-index: 3;
}
.was-validated .input-group .form-select:valid:focus{
  z-index: 3;
}
.input-group .form-select.is-valid:focus{
  z-index: 3;
}
.is-invalid ~ .invalid-feedback{
  display: block;
}
.is-invalid ~ .invalid-tooltip{
  display: block;
}
.was-validated .form-control:invalid{
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.form-control.is-invalid{
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-control:invalid:focus{
  border-color: #dc3545;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}
.form-control.is-invalid:focus{
  border-color: #dc3545;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}
.was-validated textarea.form-control:invalid{
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
textarea.form-control.is-invalid{
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
.was-validated .form-select:invalid{
  border-color: #dc3545;
}
.form-select.is-invalid{
  border-color: #dc3545;
}
.was-validated .form-select:invalid:not([multiple]):not([size]){
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-select:invalid:not([multiple])[size="1"]{
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.form-select.is-invalid:not([multiple]):not([size]){
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.form-select.is-invalid:not([multiple])[size="1"]{
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-select:invalid:focus{
  border-color: #dc3545;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}
.form-select.is-invalid:focus{
  border-color: #dc3545;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}
.form-check-input.is-invalid{
  border-color: #dc3545;
}
.form-check-input.is-invalid:checked{
  background-color: #dc3545;
}
.form-check-input.is-invalid:focus{
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}
.form-check-input.is-invalid ~ .form-check-label{
  color: #dc3545;
}
.was-validated .input-group .form-control:invalid{
  z-index: 2;
}
.input-group .form-control.is-invalid{
  z-index: 2;
}
.was-validated .input-group .form-select:invalid{
  z-index: 2;
}
.input-group .form-select.is-invalid{
  z-index: 2;
}
.was-validated .input-group .form-control:invalid:focus{
  z-index: 3;
}
.input-group .form-control.is-invalid:focus{
  z-index: 3;
}
.was-validated .input-group .form-select:invalid:focus{
  z-index: 3;
}
.input-group .form-select.is-invalid:focus{
  z-index: 3;
}
.btn-check:focus + .btn{
  outline: 0;
  box-shadow: none;
}
.btn:focus{
  outline: 0;
  box-shadow: none;
}
.btn-check:checked + .btn{
  box-shadow: none;
}
.btn-check:active + .btn{
  box-shadow: none;
}
.btn:active{
  box-shadow: none;
}
.btn.active{
  box-shadow: none;
}
.btn.\!active{
  box-shadow: none !important;
}
.btn-check:checked + .btn:focus{
  box-shadow: none;
}
.btn-check:active + .btn:focus{
  box-shadow: none;
}
.btn:active:focus{
  box-shadow: none;
}
.btn.active:focus{
  box-shadow: none;
}
.btn.\!active:focus{
  box-shadow: none !important;
}
.fade{
  -webkit-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
}
.fade:not(.show){
  opacity: 0;
}
.collapse:not(.show){
  display: none;
}
.collapsing{
  height: 0;
  overflow: hidden;
  -webkit-transition: height 0.35s ease;
  transition: height 0.35s ease;
}
.collapsing.collapse-horizontal{
  width: 0;
  height: auto;
  -webkit-transition: width 0.35s ease;
  transition: width 0.35s ease;
}
.dropdown-menu{
  z-index: 1000;
}
.dropdown-item.active{
  color: rgb(31, 41, 55);
  -webkit-text-decoration: none;
  text-decoration: none;
  background-color: #0d6efd;
}
.dropdown-item.\!active{
  color: rgb(31, 41, 55) !important;
  -webkit-text-decoration: none !important;
  text-decoration: none !important;
  background-color: #0d6efd !important;
}
.dropdown-item:active{
  color: rgb(31, 41, 55);
  -webkit-text-decoration: none;
  text-decoration: none;
  background-color: #0d6efd;
}
.dropdown-item:disabled{
  color: #adb5bd;
  pointer-events: none;
  background-color: transparent;
}
.dropdown-menu.show{
  display: block;
}
.dropdown-menu-dark .dropdown-item.active{
  color: #fff;
  background-color: #0d6efd;
}
.dropdown-menu-dark .dropdown-item.\!active{
  color: #fff !important;
  background-color: #0d6efd !important;
}
.dropdown-menu-dark .dropdown-item:active{
  color: #fff;
  background-color: #0d6efd;
}
.dropdown-menu-dark .dropdown-item.disabled{
  color: #adb5bd;
}
.dropdown-menu-dark .dropdown-item:disabled{
  color: #adb5bd;
}
.nav-tabs .nav-link{
  color: rgb(75, 85, 99);
}
.nav-tabs .nav-link:hover{
  isolation: isolate;
}
.nav-tabs .nav-link:focus{
  isolation: isolate;
}
.nav-tabs .nav-link.disabled{
  color: rgb(156, 163, 175);
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active{
  color: rgb(37, 99, 235);
  border-color: rgb(37, 99, 235);
}
.nav-tabs .nav-link.\!active{
  color: rgb(37, 99, 235) !important;
  border-color: rgb(37, 99, 235) !important;
}
.nav-tabs .nav-item.show .nav-link{
  color: rgb(37, 99, 235);
  border-color: rgb(37, 99, 235);
}
.nav-tabs .dropdown-menu{
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.nav-pills .nav-link{
  background: rgb(243, 244, 246);
  color: rgb(75, 85, 99);
  box-shadow: none;
}
.nav-pills .nav-link.active{
  background: rgb(37, 99, 235);
  color: #fff;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
.nav-pills .nav-link.\!active{
  background: rgb(37, 99, 235) !important;
  color: #fff !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}
.nav-pills .show > .nav-link{
  background: rgb(37, 99, 235);
  color: #fff;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
.nav-pills .disabled{
  color: rgb(156, 163, 175);
  background-color: rgba(243, 244, 246, 0.5);
}
.nav-pills.menu-sidebar .nav-link{
  background-color: transparent;
  box-shadow: none;
  padding: 0 5px;
  border-radius: 0;
}
.nav-pills.menu-sidebar .nav-link.active{
  color: #1266f1;
  font-weight: 600;
  border-left: 0.125rem solid #1266f1;
}
.nav-pills.menu-sidebar .nav-link.\!active{
  color: #1266f1 !important;
  font-weight: 600 !important;
  border-left: 0.125rem solid #1266f1 !important;
}
.nav-justified > .nav-link{
  -webkit-flex-basis: 0;
  -ms-flex-basis: 0;
  flex-basis: 0;
}
.nav-justified .nav-item{
  -webkit-flex-basis: 0;
  -ms-flex-basis: 0;
  flex-basis: 0;
}
.tab-content > .tab-pane{
  display: none;
}
.tab-content > .active{
  display: block;
}
.tab-content > .\!active{
  display: block !important;
}
.navbar-expand .navbar-nav{
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu{
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link{
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}
.navbar-expand .offcanvas-header{
  display: none;
}
.navbar-expand .offcanvas{
  position: inherit;
  bottom: 0;
  z-index: 1000;
  -webkit-flex-grow: 1;
  -ms-flex-grow: 1;
  flex-grow: 1;
  visibility: visible !important;
  background-color: transparent;
  border-right: 0;
  border-left: 0;
  -webkit-transition: none;
  transition: none;
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
}
.navbar-expand .offcanvas-body{
  display: flex;
  -webkit-flex-grow: 0;
  -ms-flex-grow: 0;
  flex-grow: 0;
  padding: 0;
  overflow-y: visible;
}
.navbar-light .navbar-nav .nav-link.disabled{
  color: rgba(0, 0, 0, 0.3);
}
.navbar-light .navbar-nav .show > .nav-link{
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-nav .nav-link.active{
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-nav .nav-link.\!active{
  color: rgba(0, 0, 0, 0.9) !important;
}
.navbar-dark .navbar-nav .nav-link.disabled{
  color: rgba(255, 255, 255, 0.25);
}
.navbar-dark .navbar-nav .show > .nav-link{
  color: #fff;
}
.navbar-dark .navbar-nav .nav-link.active{
  color: #fff;
}
.navbar-dark .navbar-nav .nav-link.\!active{
  color: #fff !important;
}
.accordion-item:last-of-type .accordion-button.collapsed{
  border-bottom-right-radius: calc(0.5rem - 1px);
  border-bottom-left-radius: calc(0.5rem - 1px);
}
.btn-close.disabled{
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  opacity: 0.25;
}
.modal{
  z-index: 1055;
}
.modal-dialog{
  margin: 0.5rem;
}
.modal.fade .modal-dialog{
  -webkit-transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  -webkit-transform: translate(0, -50px);
  -ms-transform: translate(0, -50px);
  transform: translate(0, -50px);
}
.modal.show .modal-dialog{
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
}
.modal.modal-static .modal-dialog{
  -webkit-transform: scale(1.02);
  -ms-transform: scale(1.02);
  transform: scale(1.02);
}
.modal-dialog-scrollable .modal-content{
  max-height: 100%;
  overflow: hidden;
}
.modal-dialog-scrollable .modal-body{
  overflow-y: auto;
}
.modal-backdrop{
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.modal-backdrop.fade{
  opacity: 0;
}
.modal-backdrop.show{
  opacity: 0.5;
}
.modal-body{
  -webkit-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}
.modal-fullscreen .modal-content{
  height: 100%;
  border: 0;
  border-radius: 0;
}
.modal-fullscreen .modal-body{
  overflow-y: auto;
}
.tooltip{
  position: absolute;
  z-index: 1080;
  display: block;
  margin: 0;
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  -webkit-text-align: start;
  text-align: start;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-text-shadow: none;
  text-shadow: none;
  -webkit-text-transform: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show{
  opacity: 1;
}
.bs-tooltip-top .tooltip-arrow{
  bottom: 0;
}
.bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow{
  bottom: 0;
}
.bs-tooltip-top .tooltip-arrow::before{
  top: -1px;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000;
}
.bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before{
  top: -1px;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000;
}
.bs-tooltip-end .tooltip-arrow{
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow{
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-end .tooltip-arrow::before{
  right: -1px;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #000;
}
.bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow::before{
  right: -1px;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #000;
}
.bs-tooltip-bottom .tooltip-arrow{
  top: 0;
}
.bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow{
  top: 0;
}
.bs-tooltip-bottom .tooltip-arrow::before{
  bottom: -1px;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000;
}
.bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before{
  bottom: -1px;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000;
}
.bs-tooltip-start .tooltip-arrow{
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow{
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-start .tooltip-arrow::before{
  left: -1px;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #000;
}
.bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before{
  left: -1px;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #000;
}
.tooltip-inner{
  max-width: 200px;
  font-size: 14px;
  padding: 6px 16px;
  color: #fff;
  -webkit-text-align: center;
  text-align: center;
  background-color: #6d6d6d;
  border-radius: 0.25rem;
}
.popover{
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1070;
  display: block;
  max-width: 276px;
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  -webkit-text-align: start;
  text-align: start;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-text-shadow: none;
  text-shadow: none;
  -webkit-text-transform: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 0;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
.bs-popover-top > .popover-arrow{
  bottom: calc(-0.5rem - 1px);
}
.bs-popover-auto[data-popper-placement^=top] > .popover-arrow{
  bottom: calc(-0.5rem - 1px);
}
.bs-popover-top > .popover-arrow::before{
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before{
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-top > .popover-arrow::after{
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #fff;
}
.bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after{
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #fff;
}
.bs-popover-end > .popover-arrow{
  left: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
}
.bs-popover-auto[data-popper-placement^=right] > .popover-arrow{
  left: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
}
.bs-popover-end > .popover-arrow::before{
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before{
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-end > .popover-arrow::after{
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #fff;
}
.bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after{
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #fff;
}
.bs-popover-bottom > .popover-arrow{
  top: calc(-0.5rem - 1px);
}
.bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow{
  top: calc(-0.5rem - 1px);
}
.bs-popover-bottom > .popover-arrow::before{
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before{
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-bottom > .popover-arrow::after{
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #fff;
}
.bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after{
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #fff;
}
.bs-popover-bottom .popover-header::before{
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid #f0f0f0;
}
.bs-popover-auto[data-popper-placement^=bottom] .popover-header::before{
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid #f0f0f0;
}
.bs-popover-start > .popover-arrow{
  right: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
}
.bs-popover-auto[data-popper-placement^=left] > .popover-arrow{
  right: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
}
.bs-popover-start > .popover-arrow::before{
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before{
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-start > .popover-arrow::after{
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #fff;
}
.bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after{
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #fff;
}
.popover-header{
  padding: 0.5rem 1rem;
  margin-bottom: 0;
  font-size: 1rem;
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  font-weight: 500;
}
.popover-header:empty{
  display: none;
}
.popover-body{
  padding: 1rem 1rem;
  color: #212529;
}
.carousel.pointer-event{
  touch-action: pan-y;
}
.carousel-item{
  display: none;
  margin-right: -100%;
  backface-visibility: hidden;
  -webkit-transition: transform 0.6s ease-in-out;
  transition: transform 0.6s ease-in-out;
}
.carousel-item.active{
  display: block;
}
.carousel-item.\!active{
  display: block !important;
}
.carousel-item-next{
  display: block;
}
.carousel-item-prev{
  display: block;
}
.carousel-item-next:not(.carousel-item-start){
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
}
.active.carousel-item-end{
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
}
.\!active.carousel-item-end{
  -webkit-transform: translateX(100%) !important;
  -ms-transform: translateX(100%) !important;
  transform: translateX(100%) !important;
}
.carousel-item-prev:not(.carousel-item-end){
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  transform: translateX(-100%);
}
.active.carousel-item-start{
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  transform: translateX(-100%);
}
.\!active.carousel-item-start{
  -webkit-transform: translateX(-100%) !important;
  -ms-transform: translateX(-100%) !important;
  transform: translateX(-100%) !important;
}
.carousel-fade .carousel-item{
  opacity: 0;
  -webkit-transition-property: opacity;
  transition-property: opacity;
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
}
.carousel-fade .carousel-item.active{
  z-index: 1;
  opacity: 1;
}
.carousel-fade .carousel-item.\!active{
  z-index: 1 !important;
  opacity: 1 !important;
}
.carousel-fade .carousel-item-next.carousel-item-start{
  z-index: 1;
  opacity: 1;
}
.carousel-fade .carousel-item-prev.carousel-item-end{
  z-index: 1;
  opacity: 1;
}
.carousel-fade .active.carousel-item-start{
  z-index: 0;
  opacity: 0;
  -webkit-transition: opacity 0s 0.6s;
  transition: opacity 0s 0.6s;
}
.carousel-fade .\!active.carousel-item-start{
  z-index: 0 !important;
  opacity: 0 !important;
  -webkit-transition: opacity 0s 0.6s !important;
  transition: opacity 0s 0.6s !important;
}
.carousel-fade .active.carousel-item-end{
  z-index: 0;
  opacity: 0;
  -webkit-transition: opacity 0s 0.6s;
  transition: opacity 0s 0.6s;
}
.carousel-fade .\!active.carousel-item-end{
  z-index: 0 !important;
  opacity: 0 !important;
  -webkit-transition: opacity 0s 0.6s !important;
  transition: opacity 0s 0.6s !important;
}
.carousel-indicators{
  z-index: 2;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none;
}
.carousel-indicators [data-bs-target]{
  box-sizing: content-box;
  -webkit-flex: 0 1 auto;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  padding: 0;
  margin-right: 3px;
  margin-left: 3px;
  -webkit-text-indent: -999px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  -webkit-transition: opacity 0.6s ease;
  transition: opacity 0.6s ease;
}
.carousel-indicators .active{
  opacity: 1;
}
.carousel-indicators .\!active{
  opacity: 1 !important;
}
.carousel-dark .carousel-indicators [data-bs-target]{
  background-color: #000;
}
.offcanvas{
  z-index: 1045;
}
.offcanvas-backdrop{
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.offcanvas-backdrop.fade{
  opacity: 0;
}
.offcanvas-backdrop.show{
  opacity: 0.5;
}
.offcanvas-end{
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
}
.offcanvas.show{
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
}
.sticky-top{
  position: sticky;
  top: 0;
  z-index: 1020;
}
.vr{
  display: inline-block;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  width: 1px;
  min-height: 1em;
  background-color: currentColor;
  opacity: 0.25;
}
.animation{
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  padding: auto;
}
.fade-in{
  -webkit-animation-name: _fade-in;
  animation-name: _fade-in;
}
.fade-out{
  -webkit-animation-name: _fade-out;
  animation-name: _fade-out;
}
.animation.infinite{
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.animation.delay-1s{
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
.animation.delay-2s{
  -webkit-animation-delay: 2s;
  animation-delay: 2s;
}
.animation.delay-3s{
  -webkit-animation-delay: 3s;
  animation-delay: 3s;
}
.animation.delay-4s{
  -webkit-animation-delay: 4s;
  animation-delay: 4s;
}
.animation.delay-5s{
  -webkit-animation-delay: 5s;
  animation-delay: 5s;
}
.animation.fast{
  -webkit-animation-duration: 800ms;
  animation-duration: 800ms;
}
.animation.faster{
  -webkit-animation-duration: 500ms;
  animation-duration: 500ms;
}
.animation.slow{
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}
.animation.slower{
  -webkit-animation-duration: 3s;
  animation-duration: 3s;
}
.slide-in-left{
  -webkit-animation-name: _slide-in-left;
  animation-name: _slide-in-left;
}
.slide-in-right{
  -webkit-animation-name: _slide-in-right;
  animation-name: _slide-in-right;
}
.slide-out-left{
  -webkit-animation-name: _slide-out-left;
  animation-name: _slide-out-left;
}
.slide-out-right{
  -webkit-animation-name: _slide-out-right;
  animation-name: _slide-out-right;
}
.ripple-surface{
  position: relative;
  overflow: hidden;
  display: inline-block;
  vertical-align: bottom;
}
.ripple-surface-unbound{
  overflow: visible;
}
.ripple-wave{
  background-image: radial-gradient(circle, rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, transparent 70%);
  border-radius: 50%;
  opacity: 0.5;
  pointer-events: none;
  position: absolute;
  touch-action: none;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -webkit-transition-property: transform, opacity;
  transition-property: transform, opacity;
  -webkit-transition-timing-function: cubic-bezier(0, 0, 0.15, 1), cubic-bezier(0, 0, 0.15, 1);
  transition-timing-function: cubic-bezier(0, 0, 0.15, 1), cubic-bezier(0, 0, 0.15, 1);
  z-index: 999;
}
.ripple-wave.active{
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  opacity: 0;
}
.ripple-wave.\!active{
  -webkit-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  transform: scale(1) !important;
  opacity: 0 !important;
}
.btn .ripple-wave{
  background-image: radial-gradient(circle, hsla(0deg, 0%, 100%, 0.2) 0, hsla(0deg, 0%, 100%, 0.3) 40%, hsla(0deg, 0%, 100%, 0.4) 50%, hsla(0deg, 0%, 100%, 0.5) 60%, hsla(0deg, 0%, 100%, 0) 70%);
}
.ripple-surface-primary .ripple-wave{
  background-image: radial-gradient(circle, rgba(18, 102, 241, 0.2) 0, rgba(18, 102, 241, 0.3) 40%, rgba(18, 102, 241, 0.4) 50%, rgba(18, 102, 241, 0.5) 60%, rgba(18, 102, 241, 0) 70%);
}
.ripple-surface-secondary .ripple-wave{
  background-image: radial-gradient(circle, rgba(178, 60, 253, 0.2) 0, rgba(178, 60, 253, 0.3) 40%, rgba(178, 60, 253, 0.4) 50%, rgba(178, 60, 253, 0.5) 60%, rgba(178, 60, 253, 0) 70%);
}
.ripple-surface-success .ripple-wave{
  background-image: radial-gradient(circle, rgba(0, 183, 74, 0.2) 0, rgba(0, 183, 74, 0.3) 40%, rgba(0, 183, 74, 0.4) 50%, rgba(0, 183, 74, 0.5) 60%, rgba(0, 183, 74, 0) 70%);
}
.ripple-surface-info .ripple-wave{
  background-image: radial-gradient(circle, rgba(57, 192, 237, 0.2) 0, rgba(57, 192, 237, 0.3) 40%, rgba(57, 192, 237, 0.4) 50%, rgba(57, 192, 237, 0.5) 60%, rgba(57, 192, 237, 0) 70%);
}
.ripple-surface-warning .ripple-wave{
  background-image: radial-gradient(circle, rgba(255, 169, 0, 0.2) 0, rgba(255, 169, 0, 0.3) 40%, rgba(255, 169, 0, 0.4) 50%, rgba(255, 169, 0, 0.5) 60%, rgba(255, 169, 0, 0) 70%);
}
.ripple-surface-danger .ripple-wave{
  background-image: radial-gradient(circle, rgba(249, 49, 84, 0.2) 0, rgba(249, 49, 84, 0.3) 40%, rgba(249, 49, 84, 0.4) 50%, rgba(249, 49, 84, 0.5) 60%, rgba(249, 49, 84, 0) 70%);
}
.ripple-surface-light .ripple-wave{
  background-image: radial-gradient(circle, hsla(0deg, 0%, 98.4%, 0.2) 0, hsla(0deg, 0%, 98.4%, 0.3) 40%, hsla(0deg, 0%, 98.4%, 0.4) 50%, hsla(0deg, 0%, 98.4%, 0.5) 60%, hsla(0deg, 0%, 98.4%, 0) 70%);
}
.ripple-surface-dark .ripple-wave{
  background-image: radial-gradient(circle, rgba(38, 38, 38, 0.2) 0, rgba(38, 38, 38, 0.3) 40%, rgba(38, 38, 38, 0.4) 50%, rgba(38, 38, 38, 0.5) 60%, rgba(38, 38, 38, 0) 70%);
}
.ripple-surface-white .ripple-wave{
  background-image: radial-gradient(circle, hsla(0deg, 0%, 100%, 0.2) 0, hsla(0deg, 0%, 100%, 0.3) 40%, hsla(0deg, 0%, 100%, 0.4) 50%, hsla(0deg, 0%, 100%, 0.5) 60%, hsla(0deg, 0%, 100%, 0) 70%);
}
.ripple-surface-black .ripple-wave{
  background-image: radial-gradient(circle, rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, transparent 70%);
}
.datepicker-toggle-button{
  position: absolute;
  outline: none;
  border: none;
  background-color: transparent;
  right: 10px;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.datepicker-toggle-button:focus{
  color: #2979ff;
}
.datepicker-toggle-button:hover{
  color: #2979ff;
}
.datepicker-backdrop{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1065;
}
.datepicker-dropdown-container{
  width: 328px;
  height: 380px;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 1066;
}
.datepicker-modal-container{
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 328px;
  height: 512px;
  background-color: #fff;
  border-radius: 0.6rem 0.6rem 0.5rem 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 1066;
}
.datepicker-header{
  height: 120px;
  padding-right: 24px;
  padding-left: 24px;
  background-color: #2979ff;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  border-radius: 0.5rem 0.5rem 0 0;
}
.datepicker-title{
  height: 32px;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}
.datepicker-title-text{
  font-size: 10px;
  font-weight: 400;
  -webkit-text-transform: uppercase;
  text-transform: uppercase;
  letter-spacing: 1.7px;
  color: #fff;
}
.datepicker-date{
  height: 72px;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}
.datepicker-date-text{
  font-size: 34px;
  font-weight: 400;
  color: #fff;
}
.datepicker-main{
  position: relative;
  height: 100%;
}
.datepicker-date-controls{
  padding: 10px 12px 0 12px;
  display: flex;
  -webkit-box-pack: space-between;
  -webkit-justify-content: space-between;
  -ms-flex-pack: space-between;
  justify-content: space-between;
  color: rgba(0, 0, 0, 0.64);
}
.datepicker-view-change-button{
  padding: 10px;
  color: #666;
  font-weight: 500;
  font-size: 0.9rem;
  border-radius: 10px;
  box-shadow: none;
  background-color: transparent;
  margin: 0;
  border: none;
}
.datepicker-view-change-button:hover{
  background-color: #eee;
}
.datepicker-view-change-button:focus{
  background-color: #eee;
}
.datepicker-view-change-button:after{
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top-width: 5px;
  border-top-style: solid;
  margin: 0 0 0 5px;
  vertical-align: middle;
}
.datepicker-arrow-controls{
  margin-top: 10px;
}
.datepicker-previous-button{
  position: relative;
  padding: 0;
  width: 40px;
  height: 40px;
  line-height: 40px;
  border: none;
  outline: none;
  margin: 0;
  color: rgba(0, 0, 0, 0.64);
  background-color: transparent;
  margin-right: 24px;
}
.datepicker-previous-button:hover{
  background-color: #eee;
  border-radius: 50%;
}
.datepicker-previous-button:focus{
  background-color: #eee;
  border-radius: 50%;
}
.datepicker-previous-button::after{
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: absolute;
  content: "";
  margin: 15.5px;
  border: 0 solid currentColor;
  border-top-width: 2px;
  border-left-width: 2px;
  -webkit-transform: translateX(2px) rotate(-45deg);
  -ms-transform: translateX(2px) rotate(-45deg);
  transform: translateX(2px) rotate(-45deg);
}
.datepicker-next-button{
  position: relative;
  padding: 0;
  width: 40px;
  height: 40px;
  line-height: 40px;
  border: none;
  outline: none;
  margin: 0;
  color: rgba(0, 0, 0, 0.64);
  background-color: transparent;
}
.datepicker-next-button:hover{
  background-color: #eee;
  border-radius: 50%;
}
.datepicker-next-button:focus{
  background-color: #eee;
  border-radius: 50%;
}
.datepicker-next-button::after{
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: absolute;
  content: "";
  margin: 15.5px;
  border: 0 solid currentColor;
  border-top-width: 2px;
  border-right-width: 2px;
  -webkit-transform: translateX(-2px) rotate(45deg);
  -ms-transform: translateX(-2px) rotate(45deg);
  transform: translateX(-2px) rotate(45deg);
}
.datepicker-view{
  padding-left: 12px;
  padding-right: 12px;
  outline: none;
}
.datepicker-table{
  margin-right: auto;
  margin-left: auto;
  width: 304px;
}
.datepicker-day-heading{
  width: 40px;
  height: 40px;
  -webkit-text-align: center;
  text-align: center;
  font-size: 12px;
  font-weight: 400;
}
.datepicker-cell{
  -webkit-text-align: center;
  text-align: center;
}
.datepicker-cell.disabled{
  color: #ccc;
  cursor: default;
  pointer-events: none;
}
.datepicker-cell.disabled:hover{
  cursor: default;
}
.datepicker-cell:hover{
  cursor: pointer;
}
.datepicker-cell:not(.disabled):not(.selected):hover .datepicker-cell-content{
  background-color: #d3d3d3;
}
.datepicker-cell.selected .datepicker-cell-content{
  background-color: #2979ff;
  color: #fff;
}
.datepicker-cell:not(.selected).focused .datepicker-cell-content{
  background-color: #eee;
}
.datepicker-cell.focused .datepicker-cell-content.selected{
  background-color: #2979ff;
}
.datepicker-cell.current .datepicker-cell-content{
  border: 1px solid #000;
}
.datepicker-small-cell{
  width: 40px;
  height: 40px;
}
.datepicker-small-cell-content{
  width: 36px;
  height: 36px;
  line-height: 36px;
  border-radius: 50%;
  font-size: 13px;
}
.datepicker-large-cell{
  width: 76px;
  height: 42px;
}
.datepicker-large-cell-content{
  width: 72px;
  height: 40px;
  line-height: 40px;
  padding: 1px 2px;
  border-radius: 999px;
}
.datepicker-footer{
  height: 56px;
  display: flex;
  position: absolute;
  width: 100%;
  bottom: 0;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-left: 12px;
  padding-right: 12px;
}
.datepicker-footer-btn{
  background-color: #fff;
  color: #2979ff;
  border: none;
  cursor: pointer;
  padding: 0 10px;
  -webkit-text-transform: uppercase;
  text-transform: uppercase;
  font-size: 0.8rem;
  font-weight: 500;
  height: 40px;
  line-height: 40px;
  letter-spacing: 0.1rem;
  border-radius: 10px;
  margin-bottom: 10px;
}
.datepicker-footer-btn:hover{
  background-color: #eee;
}
.datepicker-footer-btn:focus{
  background-color: #eee;
}
.datepicker-clear-btn{
  margin-right: auto;
}
.timepicker-wrapper{
  touch-action: none;
  z-index: 1065;
  opacity: 0;
  right: 0;
  bottom: 0;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.4);
}
.timepicker-elements{
  min-width: 310px;
  min-height: 325px;
  background: #fff;
  border-top-right-radius: 0.6rem;
  border-top-left-radius: 0.6rem;
}
.timepicker-head{
  background-color: #2979ff;
  height: 100px;
  border-top-right-radius: 0.5rem;
  border-top-left-radius: 0.5rem;
  padding: 10px 24px 10px 50px;
}
.timepicker-button{
  font-size: 0.8rem;
  min-width: 64px;
  box-sizing: border-box;
  font-weight: 500;
  line-height: 40px;
  border-radius: 10px;
  letter-spacing: 0.1rem;
  -webkit-text-transform: uppercase;
  text-transform: uppercase;
  color: #2979ff;
  border: none;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  outline: none;
  padding: 0 10px;
  height: 40px;
  margin-bottom: 10px;
}
.timepicker-button:hover{
  background-color: rgba(0, 0, 0, 0.08);
}
.timepicker-button:focus{
  outline: none;
  background-color: rgba(0, 0, 0, 0.08);
}
.timepicker-current{
  font-size: 3.75rem;
  font-weight: 300;
  line-height: 1.2;
  letter-spacing: -0.00833em;
  color: #fff;
  opacity: 0.54;
  border: none;
  background: transparent;
  padding: 0;
}
.timepicker-current.active{
  opacity: 1;
}
.timepicker-current.\!active{
  opacity: 1 !important;
}
.timepicker-current-wrapper{
  direction: ltr;
}
.timepicker-mode-wrapper{
  margin-left: 20px;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.54);
}
.timepicker-mode-wrapper.active{
  opacity: 1;
}
.timepicker-mode-wrapper.\!active{
  opacity: 1 !important;
}
.timepicker-clock-wrapper{
  min-width: 310px;
  max-width: 325px;
  min-height: 305px;
  overflow-x: hidden;
  height: 100%;
}
.timepicker-clock{
  position: relative;
  border-radius: 100%;
  width: 260px;
  height: 260px;
  cursor: default;
  margin: 0 auto;
  background-color: rgba(0, 0, 0, 0.07);
}
.timepicker-time-tips-minutes.active{
  color: #fff;
  background-color: #2979ff;
  font-weight: 400;
}
.timepicker-time-tips-minutes.\!active{
  color: #fff !important;
  background-color: #2979ff !important;
  font-weight: 400 !important;
}
.timepicker-time-tips-inner.active{
  color: #fff;
  background-color: #2979ff;
  font-weight: 400;
}
.timepicker-time-tips-inner.\!active{
  color: #fff !important;
  background-color: #2979ff !important;
  font-weight: 400 !important;
}
.timepicker-time-tips-hours.active{
  color: #fff;
  background-color: #2979ff;
  font-weight: 400;
}
.timepicker-time-tips-hours.\!active{
  color: #fff !important;
  background-color: #2979ff !important;
  font-weight: 400 !important;
}
.timepicker-time-tips-minutes.disabled{
  color: #b3afaf;
  pointer-events: none;
  background-color: transparent;
}
.timepicker-time-tips-inner.disabled{
  color: #b3afaf;
  pointer-events: none;
  background-color: transparent;
}
.timepicker-time-tips-hours.disabled{
  color: #b3afaf;
  pointer-events: none;
  background-color: transparent;
}
.timepicker-dot{
  font-weight: 300;
  line-height: 1.2;
  letter-spacing: -0.00833em;
  color: #fff;
  font-size: 3.75rem;
  opacity: 0.54;
  border: none;
  background: transparent;
  padding: 0;
}
.timepicker-middle-dot{
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: #2979ff;
}
.timepicker-hand-pointer{
  background-color: #2979ff;
  bottom: 50%;
  height: 40%;
  left: calc(50% - 1px);
  -webkit-transform-origin: center bottom 0;
  -ms-transform-origin: center bottom 0;
  transform-origin: center bottom 0;
  width: 2px;
}
.timepicker-time-tips.active{
  color: #fff;
}
.timepicker-time-tips.\!active{
  color: #fff !important;
}
.timepicker-circle{
  top: -21px;
  left: -15px;
  width: 4px;
  border: 14px solid #2979ff;
  height: 4px;
  box-sizing: content-box;
  border-radius: 100%;
}
.timepicker-hour-mode{
  padding: 0;
  background-color: transparent;
  border: none;
  color: #fff;
  opacity: 0.54;
  cursor: pointer;
}
.timepicker-hour{
  cursor: pointer;
}
.timepicker-minute{
  cursor: pointer;
}
.timepicker-hour-mode:hover{
  background-color: rgba(0, 0, 0, 0.15);
  outline: none;
}
.timepicker-hour-mode:focus{
  background-color: rgba(0, 0, 0, 0.15);
  outline: none;
}
.timepicker-hour:hover{
  background-color: rgba(0, 0, 0, 0.15);
  outline: none;
}
.timepicker-hour:focus{
  background-color: rgba(0, 0, 0, 0.15);
  outline: none;
}
.timepicker-minute:hover{
  background-color: rgba(0, 0, 0, 0.15);
  outline: none;
}
.timepicker-minute:focus{
  background-color: rgba(0, 0, 0, 0.15);
  outline: none;
}
.timepicker-hour-mode.active{
  color: #fff;
  opacity: 1;
}
.timepicker-hour-mode.\!active{
  color: #fff !important;
  opacity: 1 !important;
}
.timepicker-hour.active{
  color: #fff;
  opacity: 1;
}
.timepicker-hour.\!active{
  color: #fff !important;
  opacity: 1 !important;
}
.timepicker-minute.active{
  color: #fff;
  opacity: 1;
}
.timepicker-minute.\!active{
  color: #fff !important;
  opacity: 1 !important;
}
.timepicker-footer{
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  display: flex;
  -webkit-box-pack: space-between;
  -webkit-justify-content: space-between;
  -ms-flex-pack: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 56px;
  padding-left: 12px;
  padding-right: 12px;
  background-color: #fff;
}
.timepicker-container{
  max-height: calc(100% - 64px);
  overflow-y: auto;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
.timepicker-icon-up.active{
  opacity: 1;
}
.timepicker-icon-up.\!active{
  opacity: 1 !important;
}
.timepicker-icon-down.active{
  opacity: 1;
}
.timepicker-icon-down.\!active{
  opacity: 1 !important;
}
.timepicker-toggle-button{
  position: absolute;
  outline: none;
  border: none;
  background-color: transparent;
  right: 10px;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer;
}
.timepicker-toggle-button:hover{
  color: #2979ff;
}
.timepicker-toggle-button:focus{
  color: #2979ff;
}
.timepicker-input:focus + .timepicker-toggle-button{
  color: #2979ff;
}
.timepicker-input:focus + .timepicker-toggle-button i{
  color: #2979ff;
}
.timepicker a.timepicker-toggle-button{
  right: 1px;
}
.timepicker-toggle-button.timepicker-icon{
  right: 1px;
}
.timepicker-modal .fade.show{
  opacity: 1;
}
.stepper{
  position: relative;
  padding: 0;
  margin: 0;
  width: 100%;
  list-style: none;
  overflow: hidden;
  -webkit-transition: height 0.2s ease-in-out;
  transition: height 0.2s ease-in-out;
}
.stepper:not(.stepper-vertical){
  display: flex;
  -webkit-box-pack: space-between;
  -webkit-justify-content: space-between;
  -ms-flex-pack: space-between;
  justify-content: space-between;
}
.stepper:not(.stepper-vertical) .stepper-content{
  position: absolute;
  width: 100%;
  padding: 1rem;
}
.stepper:not(.stepper-vertical) .stepper-step{
  -webkit-flex: auto;
  -ms-flex: auto;
  flex: auto;
  height: 4.5rem;
}
.stepper:not(.stepper-vertical) .stepper-step:first-child .stepper-head{
  padding-left: 1.5rem;
}
.stepper:not(.stepper-vertical) .stepper-step:last-child .stepper-head{
  padding-right: 1.5rem;
}
.stepper:not(.stepper-vertical) .stepper-step:not(:first-child) .stepper-head:before{
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 1px;
  width: 100%;
  margin-right: 0.5rem;
  content: "";
  background-color: rgba(0, 0, 0, 0.1);
}
.stepper:not(.stepper-vertical) .stepper-step:not(:last-child) .stepper-head:after{
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 1px;
  width: 100%;
  margin-left: 0.5rem;
  content: "";
  background-color: rgba(0, 0, 0, 0.1);
}
.stepper:not(.stepper-vertical) .stepper-head-icon{
  margin: 1.5rem 0.5rem 1.5rem 0;
}
.stepper.stepper-mobile{
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
}
.stepper.stepper-mobile.stepper-progress-bar .stepper-head-icon{
  display: none;
}
.stepper.stepper-mobile .stepper-step{
  -webkit-flex: unset;
  -ms-flex: unset;
  flex: unset;
  height: fit-content;
  margin: 1rem 0 1rem 0;
}
.stepper.stepper-mobile .stepper-step:not(:last-child) .stepper-head:after{
  margin-left: 0;
}
.stepper.stepper-mobile .stepper-step:not(:first-child) .stepper-head:before{
  margin-right: 0;
}
.stepper.stepper-mobile .stepper-step:not(:last-child):not(:first-child) .stepper-head{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.stepper.stepper-mobile .stepper-head-icon{
  font-size: 0;
  margin: 0;
  height: 0.5rem;
  width: 0.5rem;
  z-index: 1;
}
.stepper.stepper-mobile .stepper-head-text{
  display: none;
}
.stepper.stepper-mobile .stepper-content{
  top: 2.56rem;
}
.stepper-back-btn .btn-link{
  color: unset;
}
.stepper-next-btn .btn-link{
  color: unset;
}
.collapse{
  visibility: visible !important;
}
@media (prefers-reduced-motion: reduce){
  .form-control::file-selector-button{
    -webkit-transition: none;
    transition: none;
  }
  .form-control::-webkit-file-upload-button{
    -webkit-transition: none;
    transition: none;
  }
  .form-switch .form-check-input{
    -webkit-transition: none;
    transition: none;
  }
  .form-range::-webkit-slider-thumb{
    -webkit-transition: none;
    transition: none;
  }
  .form-range::-moz-range-thumb{
    -webkit-transition: none;
    transition: none;
  }
  .form-floating > label{
    -webkit-transition: none;
    transition: none;
  }
  .fade{
    -webkit-transition: none;
    transition: none;
  }
  .collapsing{
    -webkit-transition: none;
    transition: none;
  }
  .collapsing.collapse-horizontal{
    -webkit-transition: none;
    transition: none;
  }
  .accordion-button::after{
    -webkit-transition: none;
    transition: none;
  }
  .modal.fade .modal-dialog{
    -webkit-transition: none;
    transition: none;
  }
  .carousel-item{
    -webkit-transition: none;
    transition: none;
  }
  .carousel-fade .active.carousel-item-start{
    -webkit-transition: none;
    transition: none;
  }
  .carousel-fade .active.carousel-item-end{
    -webkit-transition: none;
    transition: none;
  }
  .carousel-control-prev{
    -webkit-transition: none;
    transition: none;
  }
  .carousel-control-next{
    -webkit-transition: none;
    transition: none;
  }
  .carousel-indicators [data-bs-target]{
    -webkit-transition: none;
    transition: none;
  }
  .spinner-border{
    -webkit-animation-duration: 1.5s;
    animation-duration: 1.5s;
  }
  .spinner-grow{
    -webkit-animation-duration: 1.5s;
    animation-duration: 1.5s;
  }
/*   {
    -webkit-transition: none !important;
    transition: none !important;
  } */
  .carousel-fade .\!active.carousel-item-start{
    -webkit-transition: none !important;
    transition: none !important;
  }
  .carousel-fade .\!active.carousel-item-end{
    -webkit-transition: none !important;
    transition: none !important;
  }
/*   {
    -webkit-animation-duration: 1.5s !important;
    animation-duration: 1.5s !important;
    -webkit-transition: none !important;
    transition: none !important;
  } */
  .carousel-fade .\!active.carousel-item-start{
    -webkit-transition: none !important;
    transition: none !important;
  }
  .carousel-fade .\!active.carousel-item-end{
    -webkit-transition: none !important;
    transition: none !important;
  }
/*   {
    -webkit-transition: none !important;
    transition: none !important;
    -webkit-animation-duration: 1.5s !important;
    animation-duration: 1.5s !important;
  } */
}
@media (min-width: 576px){
  .navbar-expand-sm{
    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav{
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu{
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link{
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-sm .navbar-nav-scroll{
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse{
    display: flex !important;
    -webkit-flex-basis: auto;
    -ms-flex-basis: auto;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler{
    display: none;
  }
  .navbar-expand-sm .offcanvas-header{
    display: none;
  }
  .navbar-expand-sm .offcanvas{
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-flex-grow: 1;
    -ms-flex-grow: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    transition: none;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
  .navbar-expand-sm .offcanvas-top{
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-sm .offcanvas-bottom{
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-sm .offcanvas-body{
    display: flex;
    -webkit-flex-grow: 0;
    -ms-flex-grow: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
  .modal-dialog{
    max-width: 500px;
    margin: 1.75rem auto;
  }
  .modal-dialog-scrollable{
    height: calc(100% - 3.5rem);
  }
  .modal-dialog-centered{
    min-height: calc(100% - 3.5rem);
  }
  .modal-sm{
    max-width: 300px;
  }
  .sticky-sm-top{
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 768px){
  .navbar-expand-md{
    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav{
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu{
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link{
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-md .navbar-nav-scroll{
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse{
    display: flex !important;
    -webkit-flex-basis: auto;
    -ms-flex-basis: auto;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler{
    display: none;
  }
  .navbar-expand-md .offcanvas-header{
    display: none;
  }
  .navbar-expand-md .offcanvas{
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-flex-grow: 1;
    -ms-flex-grow: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    transition: none;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
  .navbar-expand-md .offcanvas-top{
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-md .offcanvas-bottom{
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-md .offcanvas-body{
    display: flex;
    -webkit-flex-grow: 0;
    -ms-flex-grow: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
  .sticky-md-top{
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 992px){
  .navbar-expand-lg{
    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav{
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu{
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link{
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-lg .navbar-nav-scroll{
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse{
    display: flex !important;
    -webkit-flex-basis: auto;
    -ms-flex-basis: auto;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler{
    display: none;
  }
  .navbar-expand-lg .offcanvas-header{
    display: none;
  }
  .navbar-expand-lg .offcanvas{
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-flex-grow: 1;
    -ms-flex-grow: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    transition: none;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
  .navbar-expand-lg .offcanvas-top{
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-lg .offcanvas-bottom{
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-lg .offcanvas-body{
    display: flex;
    -webkit-flex-grow: 0;
    -ms-flex-grow: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
  .modal-lg{
    max-width: 800px;
  }
  .modal-xl{
    max-width: 800px;
  }
  .sticky-lg-top{
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 1200px){
  .navbar-expand-xl{
    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav{
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu{
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link{
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xl .navbar-nav-scroll{
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse{
    display: flex !important;
    -webkit-flex-basis: auto;
    -ms-flex-basis: auto;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler{
    display: none;
  }
  .navbar-expand-xl .offcanvas-header{
    display: none;
  }
  .navbar-expand-xl .offcanvas{
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-flex-grow: 1;
    -ms-flex-grow: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    transition: none;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
  .navbar-expand-xl .offcanvas-top{
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-xl .offcanvas-bottom{
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-xl .offcanvas-body{
    display: flex;
    -webkit-flex-grow: 0;
    -ms-flex-grow: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
  .modal-xl{
    max-width: 1140px;
  }
  .sticky-xl-top{
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 1400px){
  .navbar-expand-xxl{
    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-xxl .navbar-nav{
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-xxl .navbar-nav .dropdown-menu{
    position: absolute;
  }
  .navbar-expand-xxl .navbar-nav .nav-link{
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xxl .navbar-nav-scroll{
    overflow: visible;
  }
  .navbar-expand-xxl .navbar-collapse{
    display: flex !important;
    -webkit-flex-basis: auto;
    -ms-flex-basis: auto;
    flex-basis: auto;
  }
  .navbar-expand-xxl .navbar-toggler{
    display: none;
  }
  .navbar-expand-xxl .offcanvas-header{
    display: none;
  }
  .navbar-expand-xxl .offcanvas{
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-flex-grow: 1;
    -ms-flex-grow: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    transition: none;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
  .navbar-expand-xxl .offcanvas-top{
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-xxl .offcanvas-bottom{
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-xxl .offcanvas-body{
    display: flex;
    -webkit-flex-grow: 0;
    -ms-flex-grow: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
  .sticky-xxl-top{
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (max-width: 575.98px){
  .modal-fullscreen-sm-down{
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-sm-down .modal-content{
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-header{
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-body{
    overflow-y: auto;
  }
  .modal-fullscreen-sm-down .modal-footer{
    border-radius: 0;
  }
}
@media (max-width: 767.98px){
  .modal-fullscreen-md-down{
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-md-down .modal-content{
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-header{
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-body{
    overflow-y: auto;
  }
  .modal-fullscreen-md-down .modal-footer{
    border-radius: 0;
  }
}
@media (max-width: 991.98px){
  .modal-fullscreen-lg-down{
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-lg-down .modal-content{
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-header{
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-body{
    overflow-y: auto;
  }
  .modal-fullscreen-lg-down .modal-footer{
    border-radius: 0;
  }
}
@media (max-width: 1199.98px){
  .modal-fullscreen-xl-down{
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xl-down .modal-content{
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-header{
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-body{
    overflow-y: auto;
  }
  .modal-fullscreen-xl-down .modal-footer{
    border-radius: 0;
  }
}
@media (max-width: 1399.98px){
  .modal-fullscreen-xxl-down{
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xxl-down .modal-content{
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-header{
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-body{
    overflow-y: auto;
  }
  .modal-fullscreen-xxl-down .modal-footer{
    border-radius: 0;
  }
}
@media (prefers-reduced-motion){
  .animation{
    -webkit-transition: none !important;
    transition: none !important;
    -webkit-animation: unset !important;
    animation: unset !important;
  }
}
@media screen and (min-width: 320px) and (max-width: 820px) and (orientation: landscape){
  .datepicker-modal-container .datepicker-header{
    height: 100%;
  }
  .datepicker-modal-container .datepicker-date{
    margin-top: 100px;
  }
  .datepicker-modal-container .datepicker-day-cell{
    width: 32x;
    height: 32x;
  }
  .datepicker-modal-container{
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    width: 475px;
    height: 360px;
  }
  .datepicker-modal-container.datepicker-day-cell{
    width: 36px;
    height: 36px;
  }
}
@media screen and (min-width: 320px) and (max-width: 825px) and (orientation: landscape){
  .timepicker-elements{
    -webkit-flex-direction: row !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important;
    border-bottom-left-radius: 0.5rem;
    min-width: auto;
    min-height: auto;
    overflow-y: auto;
  }
  .timepicker-head{
    border-top-right-radius: 0;
    border-bottom-left-radius: 0;
    padding: 10px;
    padding-right: 10px !important;
    height: auto;
    min-height: 305px;
  }
  .timepicker-head-content{
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
  .timepicker-mode-wrapper{
    -webkit-box-pack: space-around !important;
    -webkit-justify-content: space-around !important;
    -ms-flex-pack: space-around !important;
    justify-content: space-around !important;
    -webkit-flex-direction: row !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .timepicker-current{
    font-size: 3rem;
    font-weight: 400;
  }
  .timepicker-dot{
    font-size: 3rem;
    font-weight: 400;
  }
}
@keyframes _spinner-grow{
  0%{
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }
  50%{
    opacity: 1;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
}
@keyframes _fade-in{
  from{
    opacity: 0;
  }
  to{
    opacity: 1;
  }
}
@keyframes _fade-out{
  from{
    opacity: 1;
  }
  to{
    opacity: 0;
  }
}
@keyframes _fade-in-down{
  from{
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    -ms-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to{
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes _fade-in-left{
  from{
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    -ms-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
  to{
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes _fade-in-right{
  from{
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  to{
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes _fade-in-up{
  from{
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    -ms-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  to{
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes _fade-out-down{
  from{
    opacity: 1;
  }
  to{
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    -ms-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
@keyframes _fade-out-left{
  from{
    opacity: 1;
  }
  to{
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    -ms-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes _fade-out-right{
  from{
    opacity: 1;
  }
  to{
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
@keyframes _fade-out-up{
  from{
    opacity: 1;
  }
  to{
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    -ms-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
@keyframes _slide-in-down{
  from{
    visibility: visible;
    -webkit-transform: translate3d(0, -100%, 0);
    -ms-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to{
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes _slide-in-left{
  from{
    visibility: visible;
    -webkit-transform: translate3d(-100%, 0, 0);
    -ms-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
  to{
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes _slide-in-right{
  from{
    visibility: visible;
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  to{
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes _slide-in-up{
  from{
    visibility: visible;
    -webkit-transform: translate3d(0, 100%, 0);
    -ms-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  to{
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes _slide-out-down{
  from{
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to{
    visibility: hidden;
    -webkit-transform: translate3d(0, 100%, 0);
    -ms-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
@keyframes _slide-out-left{
  from{
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to{
    visibility: hidden;
    -webkit-transform: translate3d(-100%, 0, 0);
    -ms-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes _slide-out-right{
  from{
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to{
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
@keyframes _slide-out-up{
  from{
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to{
    visibility: hidden;
    -webkit-transform: translate3d(0, -100%, 0);
    -ms-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
@keyframes _slide-down{
  from{
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to{
    -webkit-transform: translate3d(0, 100%, 0);
    -ms-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
@keyframes _slide-left{
  from{
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to{
    -webkit-transform: translate3d(-100%, 0, 0);
    -ms-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes _slide-right{
  from{
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to{
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
@keyframes _slide-up{
  from{
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to{
    -webkit-transform: translate3d(0, -100%, 0);
    -ms-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
@keyframes _zoom-in{
  from{
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    -ms-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50%{
    opacity: 1;
  }
}
@keyframes _zoom-out{
  from{
    opacity: 1;
  }
  50%{
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    -ms-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to{
    opacity: 0;
  }
}
@keyframes _tada{
  from{
    -webkit-transform: scale3d(1, 1, 1);
    -ms-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  10%{
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    -ms-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  20%{
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    -ms-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%{
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    -ms-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  50%{
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    -ms-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  70%{
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    -ms-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  90%{
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    -ms-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%{
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    -ms-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  60%{
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    -ms-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  80%{
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    -ms-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  to{
    -webkit-transform: scale3d(1, 1, 1);
    -ms-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes _pulse{
  from{
    -webkit-transform: scale3d(1, 1, 1);
    -ms-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50%{
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    -ms-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }
  to{
    -webkit-transform: scale3d(1, 1, 1);
    -ms-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes _show-up-clock{
  0%{
    opacity: 0;
    -webkit-transform: scale(0.7);
    -ms-transform: scale(0.7);
    transform: scale(0.7);
  }
  to{
    opacity: 1;
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}
.sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none{
  pointer-events: none;
}
.pointer-events-auto{
  pointer-events: auto;
}
.visible{
  visibility: visible;
}
.invisible{
  visibility: hidden;
}
.collapse{
  visibility: collapse;
}
.static{
  position: static;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.\!relative{
  position: relative !important;
}
.sticky{
  position: sticky;
}
.inset-\[11px\]{
  top: 11px;
  right: 11px;
  bottom: 11px;
  left: 11px;
}
.inset-0{
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}
.inset-y-4{
  top: 1rem;
  bottom: 1rem;
}
.inset-x-2{
  left: 0.5rem;
  right: 0.5rem;
}
.left-0{
  left: 0px;
}
.top-1\/2{
  top: 50%;
}
.right-0{
  right: 0px;
}
.top-6{
  top: 1.5rem;
}
.right-8{
  right: 2rem;
}
.bottom-12{
  bottom: 3rem;
}
.left-16{
  left: 4rem;
}
.bottom-0{
  bottom: 0px;
}
.-bottom-4{
  bottom: -1rem;
}
.bottom-14{
  bottom: 3.5rem;
}
.top-0{
  top: 0px;
}
.bottom-\[-130px\]{
  bottom: -130px;
}
.left-1\/2{
  left: 50%;
}
.right-3{
  right: 0.75rem;
}
.top-2\.5{
  top: 0.625rem;
}
.top-2{
  top: 0.5rem;
}
.left-2{
  left: 0.5rem;
}
.top-\[2px\]{
  top: 2px;
}
.right-2{
  right: 0.5rem;
}
.top-\[100px\]{
  top: 100px;
}
.left-1{
  left: 0.25rem;
}
.right-1{
  right: 0.25rem;
}
.top-\[1px\]{
  top: 1px;
}
.-right-0{
  right: -0px;
}
.top-\[3px\]{
  top: 3px;
}
.right-\[5px\]{
  right: 5px;
}
.left-\[0px\]{
  left: 0px;
}
.right-\[17px\]{
  right: 17px;
}
.top-1\/4{
  top: 25%;
}
.top-\[80px\]{
  top: 80px;
}
.top-\[-20px\]{
  top: -20px;
}
.-top-\[24px\]{
  top: -24px;
}
.-top-\[14px\]{
  top: -14px;
}
.top-\[4px\]{
  top: 4px;
}
.-top-\[5px\]{
  top: -5px;
}
.-right-1{
  right: -0.25rem;
}
.\!top-\[29px\]{
  top: 29px !important;
}
.-top-\[6px\]{
  top: -6px;
}
.\!top-\[23px\]{
  top: 23px !important;
}
.top-1{
  top: 0.25rem;
}
.z-30{
  z-index: 30;
}
.z-\[2\]{
  z-index: 2;
}
.z-\[1\]{
  z-index: 1;
}
.z-\[-1\]{
  z-index: -1;
}
.z-20{
  z-index: 20;
}
.z-\[3\]{
  z-index: 3;
}
.z-\[55\]{
  z-index: 55;
}
.z-\[9999\]{
  z-index: 9999;
}
.z-\[99\]{
  z-index: 99;
}
.z-\[9\]{
  z-index: 9;
}
.z-\[999\]{
  z-index: 999;
}
.z-\[888\]{
  z-index: 888;
}
.z-10{
  z-index: 10;
}
.z-50{
  z-index: 50;
}
.-z-\[1\]{
  z-index: -1;
}
.z-\[5\]{
  z-index: 5;
}
.z-\[66\]{
  z-index: 66;
}
.z-\[45\]{
  z-index: 45;
}
.order-last{
  order: 9999;
}
.order-2{
  order: 2;
}
.col-span-3{
  grid-column: span 3 / span 3;
}
.col-span-8{
  grid-column: span 8 / span 8;
}
.col-span-4{
  grid-column: span 4 / span 4;
}
.col-span-12{
  grid-column: span 12 / span 12;
}
.col-span-1{
  grid-column: span 1 / span 1;
}
.col-span-2{
  grid-column: span 2 / span 2;
}
.col-start-1{
  grid-column-start: 1;
}
.col-end-4{
  grid-column-end: 4;
}
.row-span-2{
  grid-row: span 2 / span 2;
}
.row-start-3{
  grid-row-start: 3;
}
.row-end-5{
  grid-row-end: 5;
}
.float-right{
  float: right;
}
.float-left{
  float: left;
}
.clear-left{
  clear: left;
}
.m-4{
  margin: 1rem;
}
.m-0{
  margin: 0px;
}
.m-auto{
  margin: auto;
}
.m-1{
  margin: 0.25rem;
}
.m-5{
  margin: 1.25rem;
}
.-mx-6{
  margin-left: -1.5rem;
  margin-right: -1.5rem;
}
.my-2{
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.mx-96{
  margin-left: 24rem;
  margin-right: 24rem;
}
.my-8{
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.my-5{
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.my-7{
  margin-top: 1.75rem;
  margin-bottom: 1.75rem;
}
.mx-6{
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}
.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-6{
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.mx-\[3px\]{
  margin-left: 3px;
  margin-right: 3px;
}
.mx-\[2px\]{
  margin-left: 2px;
  margin-right: 2px;
}
.my-3{
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.mx-1{
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.-my-5{
  margin-top: -1.25rem;
  margin-bottom: -1.25rem;
}
.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-5{
  margin-left: 1.25rem;
  margin-right: 1.25rem;
}
.mb-5{
  margin-bottom: 1.25rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mr-1{
  margin-right: 0.25rem;
}
.mr-4{
  margin-right: 1rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-8{
  margin-top: 2rem;
}
.mt-1{
  margin-top: 0.25rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.mt-0{
  margin-top: 0px;
}
.mt-\[clamp\(30px\2c 100px\)\]{
  margin-top: clamp(30px,100px);
}
.mb-3{
  margin-bottom: 0.75rem;
}
.ml-4{
  margin-left: 1rem;
}
.mt-6{
  margin-top: 1.5rem;
}
.mt-12{
  margin-top: 3rem;
}
.mb-10{
  margin-bottom: 2.5rem;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.ml-auto{
  margin-left: auto;
}
.mt-7{
  margin-top: 1.75rem;
}
.mb-14{
  margin-bottom: 3.5rem;
}
.-mt-\[40px\]{
  margin-top: -40px;
}
.-mt-\[73px\]{
  margin-top: -73px;
}
.mt-3{
  margin-top: 0.75rem;
}
.-mt-6{
  margin-top: -1.5rem;
}
.mr-6{
  margin-right: 1.5rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mb-\[6px\]{
  margin-bottom: 6px;
}
.-mb-6{
  margin-bottom: -1.5rem;
}
.\!mt-7{
  margin-top: 1.75rem !important;
}
.mt-10{
  margin-top: 2.5rem;
}
.mt-5{
  margin-top: 1.25rem;
}
.ml-28{
  margin-left: 7rem;
}
.mt-\[6px\]{
  margin-top: 6px;
}
.mb-8{
  margin-bottom: 2rem;
}
.mr-5{
  margin-right: 1.25rem;
}
.ml-5{
  margin-left: 1.25rem;
}
.ml-3{
  margin-left: 0.75rem;
}
.mb-7{
  margin-bottom: 1.75rem;
}
.-ml-3{
  margin-left: -0.75rem;
}
.mr-auto{
  margin-right: auto;
}
.mb-\[3px\]{
  margin-bottom: 3px;
}
.mb-12{
  margin-bottom: 3rem;
}
.mr-3{
  margin-right: 0.75rem;
}
.mb-\[2px\]{
  margin-bottom: 2px;
}
.-mr-2{
  margin-right: -0.5rem;
}
.\!mb-0{
  margin-bottom: 0px !important;
}
.mt-24{
  margin-top: 6rem;
}
.mt-\[18px\]{
  margin-top: 18px;
}
.mb-\[17px\]{
  margin-bottom: 17px;
}
.mb-0{
  margin-bottom: 0px;
}
.mr-0{
  margin-right: 0px;
}
.ml-\[10px\]{
  margin-left: 10px;
}
.\!ml-0{
  margin-left: 0px !important;
}
.box-border{
  box-sizing: border-box;
}
.box-content{
  box-sizing: content-box;
}
.block{
  display: block;
}
.inline-block{
  display: inline-block;
}
.\!inline-block{
  display: inline-block !important;
}
.inline{
  display: inline;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.grid{
  display: grid;
}
.inline-grid{
  display: inline-grid;
}
.list-item{
  display: list-item;
}
.hidden{
  display: none;
}
.h-full{
  height: 100%;
}
.h-24{
  height: 6rem;
}
.h-\[3\.23rem\]{
  height: 3.23rem;
}
.h-\[calc\(100\%\+1rem\)\]{
  height: calc(100% + 1rem);
}
.h-\[var\(--height\)\]{
  height: var(--height);
}
.h-16{
  height: 4rem;
}
.h-7{
  height: 1.75rem;
}
.h-20{
  height: 5rem;
}
.h-\[42px\]{
  height: 42px;
}
.h-auto{
  height: auto;
}
.h-12{
  height: 3rem;
}
.h-\[248px\]{
  height: 248px;
}
.h-\[56px\]{
  height: 56px;
}
.h-4{
  height: 1rem;
}
.h-\[10px\]{
  height: 10px;
}
.h-\[88px\]{
  height: 88px;
}
.h-\[350px\]{
  height: 350px;
}
.h-\[96px\]{
  height: 96px;
}
.h-\[1px\]{
  height: 1px;
}
.h-5{
  height: 1.25rem;
}
.h-32{
  height: 8rem;
}
.h-\[40px\]{
  height: 40px;
}
.h-\[50px\]{
  height: 50px;
}
.h-\[30px\]{
  height: 30px;
}
.h-10{
  height: 2.5rem;
}
.h-min{
  height: min-content;
}
.h-8{
  height: 2rem;
}
.h-6{
  height: 1.5rem;
}
.h-14{
  height: 3.5rem;
}
.h-\[300px\]{
  height: 300px;
}
.h-9{
  height: 2.25rem;
}
.h-2{
  height: 0.5rem;
}
.h-\[150px\]{
  height: 150px;
}
.h-\[140px\]{
  height: 140px;
}
.h-\[16px\]{
  height: 16px;
}
.h-\[6px\]{
  height: 6px;
}
.h-\[100px\]{
  height: 100px;
}
.h-\[46px\]{
  height: 46px;
}
.h-\[95px\]{
  height: 95px;
}
.h-\[65px\]{
  height: 65px;
}
.h-screen{
  height: 100vh;
}
.h-\[60px\]{
  height: 60px;
}
.h-\[calc\(100\%-80px\)\]{
  height: calc(100% - 80px);
}
.h-\[200px\]{
  height: 200px;
}
.h-\[70px\]{
  height: 70px;
}
.h-\[27px\]{
  height: 27px;
}
.h-\[360px\]{
  height: 360px;
}
.h-\[290px\]{
  height: 290px;
}
.h-1{
  height: 0.25rem;
}
.h-3{
  height: 0.75rem;
}
.h-\[28px\]{
  height: 28px;
}
.max-h-\[3\.23rem\]{
  max-height: 3.23rem;
}
.max-h-\[calc\(100\%\+1rem\)\]{
  max-height: calc(100% + 1rem);
}
.max-h-\[var\(--height\)\]{
  max-height: var(--height);
}
.max-h-screen{
  max-height: 100vh;
}
.max-h-\[calc\(100vh-200px\)\]{
  max-height: calc(100vh - 200px);
}
.min-h-\[3\.23rem\]{
  min-height: 3.23rem;
}
.min-h-\[calc\(100\%\+1rem\)\]{
  min-height: calc(100% + 1rem);
}
.min-h-\[var\(--height\)\]{
  min-height: var(--height);
}
.min-h-0{
  min-height: 0px;
}
.min-h-screen{
  min-height: 100vh;
}
.min-h-full{
  min-height: 100%;
}
.min-h-\[calc\(100vh-100px\)\]{
  min-height: calc(100vh - 100px);
}
.min-h-\[250px\]{
  min-height: 250px;
}
.w-9{
  width: 2.25rem;
}
.w-full{
  width: 100%;
}
.w-\[3\.23rem\]{
  width: 3.23rem;
}
.w-\[calc\(100\%\+1rem\)\]{
  width: calc(100% + 1rem);
}
.w-\[var\(--width\)\]{
  width: var(--width);
}
.w-12{
  width: 3rem;
}
.w-7{
  width: 1.75rem;
}
.w-20{
  width: 5rem;
}
.w-\[42px\]{
  width: 42px;
}
.w-\[56px\]{
  width: 56px;
}
.w-4{
  width: 1rem;
}
.w-\[10px\]{
  width: 10px;
}
.w-\[65px\]{
  width: 65px;
}
.w-\[285px\]{
  width: 285px;
}
.w-\[96px\]{
  width: 96px;
}
.w-\[25px\]{
  width: 25px;
}
.w-auto{
  width: auto;
}
.w-5{
  width: 1.25rem;
}
.w-10{
  width: 2.5rem;
}
.w-\[320px\]{
  width: 320px;
}
.w-\[2px\]{
  width: 2px;
}
.w-6{
  width: 1.5rem;
}
.w-14{
  width: 3.5rem;
}
.w-1\/2{
  width: 50%;
}
.w-\[140px\]{
  width: 140px;
}
.w-8{
  width: 2rem;
}
.w-\[16px\]{
  width: 16px;
}
.w-\[46px\]{
  width: 46px;
}
.w-11{
  width: 2.75rem;
}
.\!w-\[100px\]{
  width: 100px !important;
}
.w-\[6px\]{
  width: 6px;
}
.w-\[100px\]{
  width: 100px;
}
.w-2{
  width: 0.5rem;
}
.w-\[150px\]{
  width: 150px;
}
.w-\[50px\]{
  width: 50px;
}
.w-96{
  width: 24rem;
}
.w-0{
  width: 0px;
}
.w-screen{
  width: 100vw;
}
.w-\[70px\]{
  width: 70px;
}
.w-\[27px\]{
  width: 27px;
}
.w-max{
  width: max-content;
}
.w-\[28px\]{
  width: 28px;
}
.w-44{
  width: 11rem;
}
.w-\[335px\]{
  width: 335px;
}
.min-w-\[3\.23rem\]{
  min-width: 3.23rem;
}
.min-w-\[calc\(100\%\+1rem\)\]{
  min-width: calc(100% + 1rem);
}
.min-w-\[var\(--width\)\]{
  min-width: var(--width);
}
.min-w-min{
  min-width: min-content;
}
.min-w-full{
  min-width: 100%;
}
.min-w-\[90px\]{
  min-width: 90px;
}
.min-w-\[120px\]{
  min-width: 120px;
}
.min-w-max{
  min-width: max-content;
}
.min-w-\[270px\]{
  min-width: 270px;
}
.min-w-\[160px\]{
  min-width: 160px;
}
.min-w-\[140px\]{
  min-width: 140px;
}
.min-w-\[170px\]{
  min-width: 170px;
}
.min-w-\[60px\]{
  min-width: 60px;
}
.min-w-\[184px\]{
  min-width: 184px;
}
.max-w-xl{
  max-width: 36rem;
}
.max-w-md{
  max-width: 28rem;
}
.max-w-4xl{
  max-width: 56rem;
}
.max-w-\[3\.23rem\]{
  max-width: 3.23rem;
}
.max-w-\[calc\(100\%\+1rem\)\]{
  max-width: calc(100% + 1rem);
}
.max-w-\[var\(--width\)\]{
  max-width: var(--width);
}
.max-w-full{
  max-width: 100%;
}
.max-w-\[546px\]{
  max-width: 546px;
}
.max-w-\[300px\]{
  max-width: 300px;
}
.max-w-\[90\%\]{
  max-width: 90%;
}
.max-w-\[180px\]{
  max-width: 180px;
}
.max-w-\[168px\]{
  max-width: 168px;
}
.max-w-\[160px\]{
  max-width: 160px;
}
.max-w-\[500px\]{
  max-width: 500px;
}
.max-w-\[400px\]{
  max-width: 400px;
}
.max-w-\[520px\]{
  max-width: 520px;
}
.max-w-\[525px\]{
  max-width: 525px;
}
.max-w-\[980px\]{
  max-width: 980px;
}
.\!max-w-full{
  max-width: 100% !important;
}
.max-w-2xl{
  max-width: 42rem;
}
.max-w-\[350px\]{
  max-width: 350px;
}
.max-w-\[516px\]{
  max-width: 516px;
}
.max-w-\[50px\]{
  max-width: 50px;
}
.max-w-\[242px\]{
  max-width: 242px;
}
.max-w-\[63\%\]{
  max-width: 63%;
}
.max-w-\[124px\]{
  max-width: 124px;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-none{
  flex: none;
}
.flex-shrink{
  flex-shrink: 1;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.flex-grow{
  flex-grow: 1;
}
.flex-grow-0{
  flex-grow: 0;
}
.grow{
  flex-grow: 1;
}
.grow-0{
  flex-grow: 0;
}
.table-fixed{
  table-layout: fixed;
}
.border-collapse{
  border-collapse: collapse;
}
.origin-top-right{
  transform-origin: top right;
}
.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-5{
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-4{
  --tw-translate-x: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-6{
  --tw-translate-y: 1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-3{
  --tw-translate-x: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\!-translate-y-1\/2{
  --tw-translate-y: -50% !important;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;
}
.\!-translate-y-0{
  --tw-translate-y: -0px !important;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;
}
.translate-y-1\/2{
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[23deg\]{
  --tw-rotate: 23deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[2\.3rad\]{
  --tw-rotate: 2.3rad;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[401grad\]{
  --tw-rotate: 401grad;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[1\.5turn\]{
  --tw-rotate: 1.5turn;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-3{
  --tw-rotate: 3deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-45{
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[60deg\]{
  --tw-rotate: 60deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90{
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-90{
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.skew-y-12{
  --tw-skew-y: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.skew-x-12{
  --tw-skew-x: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-50{
  --tw-scale-x: .5;
  --tw-scale-y: .5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-gpu{
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.animate-none{
  animation: none;
}
@keyframes spin{
  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
@keyframes pulse{
  50%{
    opacity: .5;
  }
}
.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes tada{
  0%{
    transform: scale3d(1, 1, 1);
  }
  10%, 20%{
    transform: scale3d(1, 1, 0.95) rotate3d(0, 0, 1, -10deg);
  }
  30%, 50%, 70%, 90%{
    transform: scale3d(1, 1, 1) rotate3d(0, 0, 1, 10deg);
  }
  40%, 60%, 80%{
    transform: rotate3d(0, 0, 1, -10deg);
  }
  100%{
    transform: scale3d(1, 1, 1);
  }
}
.animate-tada{
  animation: tada 1.5s ease-in-out infinite;
}
.cursor-not-allowed{
  cursor: not-allowed;
}
.cursor-pointer{
  cursor: pointer;
}
.cursor-grab{
  cursor: grab;
}
.cursor-default{
  cursor: default;
}
.select-none{
  user-select: none;
}
.resize-none{
  resize: none;
}
.resize{
  resize: both;
}
.list-inside{
  list-style-position: inside;
}
.list-none{
  list-style-type: none;
}
.list-disc{
  list-style-type: disc;
}
.list-decimal{
  list-style-type: decimal;
}
.appearance-none{
  appearance: none;
}
.auto-cols-min{
  grid-auto-columns: min-content;
}
.grid-flow-row{
  grid-auto-flow: row;
}
.auto-rows-max{
  grid-auto-rows: max-content;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-\[200px\2c repeat\(auto-fill\2c minmax\(15\%\2c 100px\)\)\2c 300px\]{
  grid-template-columns: 200px repeat(auto-fill,minmax(15%,100px)) 300px;
}
.grid-cols-4{
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-12{
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-3{
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-rows-3{
  grid-template-rows: repeat(3, minmax(0, 1fr));
}
.flex-row{
  flex-direction: row;
}
.flex-row-reverse{
  flex-direction: row-reverse;
}
.flex-col{
  flex-direction: column;
}
.flex-wrap{
  flex-wrap: wrap;
}
.place-content-center{
  place-content: center;
}
.place-content-start{
  place-content: start;
}
.place-items-end{
  place-items: end;
}
.content-center{
  align-content: center;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.items-stretch{
  align-items: stretch;
}
.justify-start{
  justify-content: flex-start;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.justify-around{
  justify-content: space-around;
}
.justify-evenly{
  justify-content: space-evenly;
}
.justify-items-end{
  justify-items: end;
}
.gap-6{
  gap: 1.5rem;
}
.gap-4{
  gap: 1rem;
}
.gap-5{
  gap: 1.25rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-7{
  gap: 1.75rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-x-2{
  column-gap: 0.5rem;
}
.gap-y-3{
  row-gap: 0.75rem;
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-\[20cm\] > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(20cm * var(--tw-space-x-reverse));
  margin-left: calc(20cm * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-\[calc\(20\%-1cm\)\] > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(calc(20% - 1cm) * var(--tw-space-x-reverse));
  margin-left: calc(calc(20% - 1cm) * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-x-5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.25rem * var(--tw-space-x-reverse));
  margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-7 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.75rem * var(--tw-space-x-reverse));
  margin-left: calc(1.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-\[2px\] > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2px * var(--tw-space-y-reverse));
}
.space-y-10 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}
.-space-x-1\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.375rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.375rem * calc(1 - var(--tw-space-x-reverse)));
}
.-space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-\[6px\] > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(6px * var(--tw-space-x-reverse));
  margin-left: calc(6px * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-reverse > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 1;
}
.space-x-reverse > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 1;
}
.divide-y > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-x-reverse: 0;
  border-right-width: calc(2px * var(--tw-divide-x-reverse));
  border-left-width: calc(2px * calc(1 - var(--tw-divide-x-reverse)));
}
.divide-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(4px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(4px * var(--tw-divide-y-reverse));
}
.divide-x-0 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-x-reverse: 0;
  border-right-width: calc(0px * var(--tw-divide-x-reverse));
  border-left-width: calc(0px * calc(1 - var(--tw-divide-x-reverse)));
}
.divide-y-0 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(0px * var(--tw-divide-y-reverse));
}
.divide-dotted > :not([hidden]) ~ :not([hidden]){
  border-style: dotted;
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity));
}
.divide-slate-100 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-divide-opacity));
}
.divide-opacity-50 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 0.5;
}
.place-self-center{
  place-self: center;
}
.self-end{
  align-self: flex-end;
}
.self-center{
  align-self: center;
}
.justify-self-start{
  justify-self: start;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.overflow-x-hidden{
  overflow-x: hidden;
}
.overscroll-contain{
  overscroll-behavior: contain;
}
.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.overflow-ellipsis{
  text-overflow: ellipsis;
}
.text-ellipsis{
  text-overflow: ellipsis;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.whitespace-pre-wrap{
  white-space: pre-wrap;
}
.break-words{
  overflow-wrap: break-word;
}
.break-all{
  word-break: break-all;
}
.rounded-md{
  border-radius: 0.375rem;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-\[999px\]{
  border-radius: 999px;
}
.rounded-lg{
  border-radius: 0.5rem;
}
.rounded-\[6px\]{
  border-radius: 6px;
}
.rounded-2xl{
  border-radius: 1rem;
}
.rounded-\[40px\]{
  border-radius: 40px;
}
.rounded-sm{
  border-radius: 0.125rem;
}
.rounded-xl{
  border-radius: 0.75rem;
}
.rounded-\[3px\]{
  border-radius: 3px;
}
.rounded-none{
  border-radius: 0px;
}
.rounded-\[100\%\]{
  border-radius: 100%;
}
.rounded-\[25px\]{
  border-radius: 25px;
}
.rounded-t-md{
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}
.rounded-t{
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.rounded-b{
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.rounded-t-lg{
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.rounded-r-2xl{
  border-top-right-radius: 1rem;
  border-bottom-right-radius: 1rem;
}
.rounded-r-xl{
  border-top-right-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}
.rounded-tr{
  border-top-right-radius: 0.25rem;
}
.rounded-br{
  border-bottom-right-radius: 0.25rem;
}
.rounded-bl{
  border-bottom-left-radius: 0.25rem;
}
.border-0{
  border-width: 0px;
}
.border-2{
  border-width: 2px;
}
.border-\[2\.5px\]{
  border-width: 2.5px;
}
.border{
  border-width: 1px;
}
.border-4{
  border-width: 4px;
}
.border-x-0{
  border-left-width: 0px;
  border-right-width: 0px;
}
.border-b{
  border-bottom-width: 1px;
}
.border-r{
  border-right-width: 1px;
}
.border-l{
  border-left-width: 1px;
}
.border-b-0{
  border-bottom-width: 0px;
}
.border-t-0{
  border-top-width: 0px;
}
.border-b-2{
  border-bottom-width: 2px;
}
.border-t{
  border-top-width: 1px;
}
.border-solid{
  border-style: solid;
}
.border-dashed{
  border-style: dashed;
}
.border-none{
  border-style: none;
}
.border-slate-100{
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity));
}
.border-transparent{
  border-color: transparent;
}
.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(210 214 220 / var(--tw-border-opacity));
}
.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}
.border-\[\#f00\]{
  --tw-border-opacity: 1;
  border-color: rgb(255 0 0 / var(--tw-border-opacity));
}
.border-slate-800{
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity));
}
.border-gray-500{
  --tw-border-opacity: 1;
  border-color: rgb(104 118 138 / var(--tw-border-opacity));
}
.border-slate-200{
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
}
.border-secondary-500{
  --tw-border-opacity: 1;
  border-color: rgb(160 174 192 / var(--tw-border-opacity));
}
.border-danger-500{
  --tw-border-opacity: 1;
  border-color: rgb(241 89 92 / var(--tw-border-opacity));
}
.border-slate-300{
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity));
}
.\!border-success-500{
  --tw-border-opacity: 1 !important;
  border-color: rgb(80 199 147 / var(--tw-border-opacity)) !important;
}
.\!border-danger-500{
  --tw-border-opacity: 1 !important;
  border-color: rgb(241 89 92 / var(--tw-border-opacity)) !important;
}
.\!border-slate-400{
  --tw-border-opacity: 1 !important;
  border-color: rgb(148 163 184 / var(--tw-border-opacity)) !important;
}
.\!border-slate-300{
  --tw-border-opacity: 1 !important;
  border-color: rgb(203 213 225 / var(--tw-border-opacity)) !important;
}
.border-slate-400{
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity));
}
.border-primary-500{
  --tw-border-opacity: 1;
  border-color: rgb(70 105 250 / var(--tw-border-opacity));
}
.border-success-500{
  --tw-border-opacity: 1;
  border-color: rgb(80 199 147 / var(--tw-border-opacity));
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.border-slate-600{
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity));
}
.border-r-slate-200{
  --tw-border-opacity: 1;
  border-right-color: rgb(226 232 240 / var(--tw-border-opacity));
}
.border-l-slate-200{
  --tw-border-opacity: 1;
  border-left-color: rgb(226 232 240 / var(--tw-border-opacity));
}
.border-b-\[\#9AA2AF\]{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(154 162 175 / var(--tw-border-opacity));
}
.border-b-gray-500{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(104 118 138 / var(--tw-border-opacity));
}
.border-b-slate-300{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(203 213 225 / var(--tw-border-opacity));
}
.border-opacity-10{
  --tw-border-opacity: 0.1;
}
.border-opacity-\[16\%\]{
  --tw-border-opacity: 16%;
}
.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(244 245 247 / var(--tw-bg-opacity));
}
.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}
.bg-\[\#0f0\]{
  --tw-bg-opacity: 1;
  background-color: rgb(0 255 0 / var(--tw-bg-opacity));
}
.bg-\[\#ff0000\]{
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 0 / var(--tw-bg-opacity));
}
.bg-\[\#0000ffcc\]{
  background-color: #0000ffcc;
}
.bg-\[rgb\(123\2c 123\2c 123\)\]{
  --tw-bg-opacity: 1;
  background-color: rgb(123 123 123 / var(--tw-bg-opacity));
}
.bg-\[rgba\(123\2c 123\2c 123\2c 0\.5\)\]{
  background-color: rgba(123,123,123,0.5);
}
.bg-\[hsl\(0\2c 100\%\2c 50\%\)\]{
  --tw-bg-opacity: 1;
  background-color: hsl(0 100% 50% / var(--tw-bg-opacity));
}
.bg-\[hsla\(0\2c 100\%\2c 50\%\2c 0\.3\)\]{
  background-color: hsla(0,100%,50%,0.3);
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-success-500{
  --tw-bg-opacity: 1;
  background-color: rgb(80 199 147 / var(--tw-bg-opacity));
}
.bg-warning-500{
  --tw-bg-opacity: 1;
  background-color: rgb(250 145 107 / var(--tw-bg-opacity));
}
.bg-danger-500{
  --tw-bg-opacity: 1;
  background-color: rgb(241 89 92 / var(--tw-bg-opacity));
}
.bg-slate-200{
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity));
}
.bg-slate-50{
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity));
}
.bg-primary-500{
  --tw-bg-opacity: 1;
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
}
.bg-slate-100{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}
.\!bg-transparent{
  background-color: transparent !important;
}
.bg-slate-900{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
.bg-info-500{
  --tw-bg-opacity: 1;
  background-color: rgb(12 231 250 / var(--tw-bg-opacity));
}
.bg-\[\#E5F9FF\]{
  --tw-bg-opacity: 1;
  background-color: rgb(229 249 255 / var(--tw-bg-opacity));
}
.bg-\[\#EDFFE5\]{
  --tw-bg-opacity: 1;
  background-color: rgb(237 255 229 / var(--tw-bg-opacity));
}
.bg-transparent{
  background-color: transparent;
}
.bg-slate-300{
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity));
}
.bg-slate-400{
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity));
}
.bg-slate-500{
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
}
.bg-slate-600{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}
.bg-slate-700{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
}
.bg-slate-800{
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}
.bg-primary-50{
  --tw-bg-opacity: 1;
  background-color: rgb(246 248 255 / var(--tw-bg-opacity));
}
.bg-primary-100{
  --tw-bg-opacity: 1;
  background-color: rgb(237 240 255 / var(--tw-bg-opacity));
}
.bg-primary-200{
  --tw-bg-opacity: 1;
  background-color: rgb(209 218 254 / var(--tw-bg-opacity));
}
.bg-primary-300{
  --tw-bg-opacity: 1;
  background-color: rgb(180 194 253 / var(--tw-bg-opacity));
}
.bg-primary-400{
  --tw-bg-opacity: 1;
  background-color: rgb(128 146 255 / var(--tw-bg-opacity));
}
.bg-primary-600{
  --tw-bg-opacity: 1;
  background-color: rgb(63 94 223 / var(--tw-bg-opacity));
}
.bg-primary-700{
  --tw-bg-opacity: 1;
  background-color: rgb(42 63 150 / var(--tw-bg-opacity));
}
.bg-primary-800{
  --tw-bg-opacity: 1;
  background-color: rgb(32 48 113 / var(--tw-bg-opacity));
}
.bg-primary-900{
  --tw-bg-opacity: 1;
  background-color: rgb(21 31 73 / var(--tw-bg-opacity));
}
.bg-info-50{
  --tw-bg-opacity: 1;
  background-color: rgb(243 254 255 / var(--tw-bg-opacity));
}
.bg-info-100{
  --tw-bg-opacity: 1;
  background-color: rgb(231 254 255 / var(--tw-bg-opacity));
}
.bg-info-200{
  --tw-bg-opacity: 1;
  background-color: rgb(197 253 255 / var(--tw-bg-opacity));
}
.bg-info-300{
  --tw-bg-opacity: 1;
  background-color: rgb(163 252 255 / var(--tw-bg-opacity));
}
.bg-info-400{
  --tw-bg-opacity: 1;
  background-color: rgb(95 249 255 / var(--tw-bg-opacity));
}
.bg-info-600{
  --tw-bg-opacity: 1;
  background-color: rgb(0 184 212 / var(--tw-bg-opacity));
}
.bg-info-700{
  --tw-bg-opacity: 1;
  background-color: rgb(0 122 141 / var(--tw-bg-opacity));
}
.bg-info-800{
  --tw-bg-opacity: 1;
  background-color: rgb(0 94 103 / var(--tw-bg-opacity));
}
.bg-info-900{
  --tw-bg-opacity: 1;
  background-color: rgb(0 63 66 / var(--tw-bg-opacity));
}
.bg-success-50{
  --tw-bg-opacity: 1;
  background-color: rgb(243 254 248 / var(--tw-bg-opacity));
}
.bg-success-100{
  --tw-bg-opacity: 1;
  background-color: rgb(231 253 241 / var(--tw-bg-opacity));
}
.bg-success-200{
  --tw-bg-opacity: 1;
  background-color: rgb(197 251 227 / var(--tw-bg-opacity));
}
.bg-success-300{
  --tw-bg-opacity: 1;
  background-color: rgb(163 249 213 / var(--tw-bg-opacity));
}
.bg-success-400{
  --tw-bg-opacity: 1;
  background-color: rgb(95 245 177 / var(--tw-bg-opacity));
}
.bg-success-600{
  --tw-bg-opacity: 1;
  background-color: rgb(63 154 122 / var(--tw-bg-opacity));
}
.bg-success-700{
  --tw-bg-opacity: 1;
  background-color: rgb(46 109 97 / var(--tw-bg-opacity));
}
.bg-success-800{
  --tw-bg-opacity: 1;
  background-color: rgb(31 75 71 / var(--tw-bg-opacity));
}
.bg-success-900{
  --tw-bg-opacity: 1;
  background-color: rgb(15 42 46 / var(--tw-bg-opacity));
}
.bg-warning-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 250 248 / var(--tw-bg-opacity));
}
.bg-warning-100{
  --tw-bg-opacity: 1;
  background-color: rgb(255 244 241 / var(--tw-bg-opacity));
}
.bg-warning-200{
  --tw-bg-opacity: 1;
  background-color: rgb(254 228 218 / var(--tw-bg-opacity));
}
.bg-warning-300{
  --tw-bg-opacity: 1;
  background-color: rgb(253 210 195 / var(--tw-bg-opacity));
}
.bg-warning-400{
  --tw-bg-opacity: 1;
  background-color: rgb(252 178 152 / var(--tw-bg-opacity));
}
.bg-warning-600{
  --tw-bg-opacity: 1;
  background-color: rgb(223 130 96 / var(--tw-bg-opacity));
}
.bg-warning-700{
  --tw-bg-opacity: 1;
  background-color: rgb(150 87 65 / var(--tw-bg-opacity));
}
.bg-warning-800{
  --tw-bg-opacity: 1;
  background-color: rgb(113 66 49 / var(--tw-bg-opacity));
}
.bg-warning-900{
  --tw-bg-opacity: 1;
  background-color: rgb(73 43 32 / var(--tw-bg-opacity));
}
.bg-danger-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 247 / var(--tw-bg-opacity));
}
.bg-danger-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 239 239 / var(--tw-bg-opacity));
}
.bg-danger-200{
  --tw-bg-opacity: 1;
  background-color: rgb(252 214 215 / var(--tw-bg-opacity));
}
.bg-danger-300{
  --tw-bg-opacity: 1;
  background-color: rgb(250 187 189 / var(--tw-bg-opacity));
}
.bg-danger-400{
  --tw-bg-opacity: 1;
  background-color: rgb(246 139 141 / var(--tw-bg-opacity));
}
.bg-danger-600{
  --tw-bg-opacity: 1;
  background-color: rgb(215 80 82 / var(--tw-bg-opacity));
}
.bg-danger-700{
  --tw-bg-opacity: 1;
  background-color: rgb(145 54 56 / var(--tw-bg-opacity));
}
.bg-danger-800{
  --tw-bg-opacity: 1;
  background-color: rgb(109 41 42 / var(--tw-bg-opacity));
}
.bg-danger-900{
  --tw-bg-opacity: 1;
  background-color: rgb(70 26 27 / var(--tw-bg-opacity));
}
.bg-secondary-500{
  --tw-bg-opacity: 1;
  background-color: rgb(160 174 192 / var(--tw-bg-opacity));
}
.bg-black-500{
  --tw-bg-opacity: 1;
  background-color: rgb(17 17 18 / var(--tw-bg-opacity));
}
.\!bg-black-500{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(17 17 18 / var(--tw-bg-opacity)) !important;
}
.bg-\[\#E0EAFF\]{
  --tw-bg-opacity: 1;
  background-color: rgb(224 234 255 / var(--tw-bg-opacity));
}
.bg-\[\#FFC155\]{
  --tw-bg-opacity: 1;
  background-color: rgb(255 193 85 / var(--tw-bg-opacity));
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}
.bg-slate-900\/40{
  background-color: rgb(15 23 42 / 0.4);
}
.bg-\[\#FFEDE6\]{
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 230 / var(--tw-bg-opacity));
}
.bg-\[\#EAE6FF\]{
  --tw-bg-opacity: 1;
  background-color: rgb(234 230 255 / var(--tw-bg-opacity));
}
.bg-\[\#FFEDE5\]{
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 229 / var(--tw-bg-opacity));
}
.bg-\[\#EAE5FF\]{
  --tw-bg-opacity: 1;
  background-color: rgb(234 229 255 / var(--tw-bg-opacity));
}
.bg-\[\#1C9CEB\]{
  --tw-bg-opacity: 1;
  background-color: rgb(28 156 235 / var(--tw-bg-opacity));
}
.bg-\[\#395599\]{
  --tw-bg-opacity: 1;
  background-color: rgb(57 85 153 / var(--tw-bg-opacity));
}
.bg-\[\#0A63BC\]{
  --tw-bg-opacity: 1;
  background-color: rgb(10 99 188 / var(--tw-bg-opacity));
}
.bg-\[\#EA4335\]{
  --tw-bg-opacity: 1;
  background-color: rgb(234 67 53 / var(--tw-bg-opacity));
}
.bg-black-900{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
.bg-opacity-\[0\.11\]{
  --tw-bg-opacity: 0.11;
}
.bg-opacity-20{
  --tw-bg-opacity: 0.2;
}
.bg-opacity-50{
  --tw-bg-opacity: 0.5;
}
.bg-opacity-25{
  --tw-bg-opacity: 0.25;
}
.bg-opacity-30{
  --tw-bg-opacity: 0.3;
}
.bg-opacity-\[0\.16\]{
  --tw-bg-opacity: 0.16;
}
.bg-opacity-\[0\.15\]{
  --tw-bg-opacity: 0.15;
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-red-300{
  --tw-gradient-from: #fca5a5;
  --tw-gradient-to: rgb(252 165 165 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#1EABEC\]{
  --tw-gradient-from: #1EABEC;
  --tw-gradient-to: rgb(30 171 236 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#4C33F7\]{
  --tw-gradient-from: #4C33F7;
  --tw-gradient-to: rgb(76 51 247 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#FF9838\]{
  --tw-gradient-from: #FF9838;
  --tw-gradient-to: rgb(255 152 56 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-purple-200{
  --tw-gradient-to: rgb(233 213 255 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), #e9d5ff, var(--tw-gradient-to);
}
.to-blue-400{
  --tw-gradient-to: #60a5fa;
}
.to-primary-500{
  --tw-gradient-to: #4669fa;
}
.to-\[\#801FE0\]{
  --tw-gradient-to: #801FE0;
}
.to-\[\#008773\]{
  --tw-gradient-to: #008773;
}
.bg-cover{
  background-size: cover;
}
.bg-local{
  background-attachment: local;
}
.bg-clip-border{
  background-clip: border-box;
}
.bg-clip-padding{
  background-clip: padding-box;
}
.bg-top{
  background-position: top;
}
.bg-center{
  background-position: center;
}
.bg-no-repeat{
  background-repeat: no-repeat;
}
.fill-current{
  fill: currentColor;
}
.stroke-current{
  stroke: currentColor;
}
.stroke-2{
  stroke-width: 2;
}
.object-contain{
  object-fit: contain;
}
.object-cover{
  object-fit: cover;
}
.object-bottom{
  object-position: bottom;
}
.p-6{
  padding: 1.5rem;
}
.p-\[var\(--app-padding\)\]{
  padding: var(--app-padding);
}
.p-4{
  padding: 1rem;
}
.p-0{
  padding: 0px;
}
.p-5{
  padding: 1.25rem;
}
.p-1\.5{
  padding: 0.375rem;
}
.p-1{
  padding: 0.25rem;
}
.p-3{
  padding: 0.75rem;
}
.p-2{
  padding: 0.5rem;
}
.p-\[10px\]{
  padding: 10px;
}
.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.\!px-12{
  padding-left: 3rem !important;
  padding-right: 3rem !important;
}
.\!py-1{
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}
.\!px-9{
  padding-left: 2.25rem !important;
  padding-right: 2.25rem !important;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.px-0\.5{
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}
.px-0{
  padding-left: 0px;
  padding-right: 0px;
}
.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-20{
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-\[18px\]{
  padding-top: 18px;
  padding-bottom: 18px;
}
.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-\[52px\]{
  padding-top: 52px;
  padding-bottom: 52px;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.py-5{
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-10{
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.px-10{
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-\[35px\]{
  padding-left: 35px;
  padding-right: 35px;
}
.px-7{
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}
.py-\[12px\]{
  padding-top: 12px;
  padding-bottom: 12px;
}
.\!py-\[14px\]{
  padding-top: 14px !important;
  padding-bottom: 14px !important;
}
.px-\[10px\]{
  padding-left: 10px;
  padding-right: 10px;
}
.py-\[2px\]{
  padding-top: 2px;
  padding-bottom: 2px;
}
.py-\[6px\]{
  padding-top: 6px;
  padding-bottom: 6px;
}
.py-\[8px\]{
  padding-top: 8px;
  padding-bottom: 8px;
}
.py-\[10px\]{
  padding-top: 10px;
  padding-bottom: 10px;
}
.pb-5{
  padding-bottom: 1.25rem;
}
.\!pl-12{
  padding-left: 3rem !important;
}
.\!pr-12{
  padding-right: 3rem !important;
}
.\!pr-32{
  padding-right: 8rem !important;
}
.\!pl-56{
  padding-left: 14rem !important;
}
.\!pl-28{
  padding-left: 7rem !important;
}
.pl-28{
  padding-left: 7rem;
}
.\!pl-9{
  padding-left: 2.25rem !important;
}
.\!pr-9{
  padding-right: 2.25rem !important;
}
.\!pl-52{
  padding-left: 13rem !important;
}
.pl-0{
  padding-left: 0px;
}
.pb-2{
  padding-bottom: 0.5rem;
}
.pt-3{
  padding-top: 0.75rem;
}
.pt-2{
  padding-top: 0.5rem;
}
.pt-1{
  padding-top: 0.25rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.pb-3{
  padding-bottom: 0.75rem;
}
.pl-4{
  padding-left: 1rem;
}
.pb-6{
  padding-bottom: 1.5rem;
}
.pl-8{
  padding-left: 2rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pt-20{
  padding-top: 5rem;
}
.pt-6{
  padding-top: 1.5rem;
}
.pb-20{
  padding-bottom: 5rem;
}
.pr-9{
  padding-right: 2.25rem;
}
.pl-2{
  padding-left: 0.5rem;
}
.\!pr-5{
  padding-right: 1.25rem !important;
}
.pb-10{
  padding-bottom: 2.5rem;
}
.pt-10{
  padding-top: 2.5rem;
}
.pt-4{
  padding-top: 1rem;
}
.pb-1{
  padding-bottom: 0.25rem;
}
.pb-8{
  padding-bottom: 2rem;
}
.pr-3{
  padding-right: 0.75rem;
}
.\!pl-10{
  padding-left: 2.5rem !important;
}
.pt-0{
  padding-top: 0px;
}
.pr-14{
  padding-right: 3.5rem;
}
.\!pr-14{
  padding-right: 3.5rem !important;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.text-start{
  text-align: start;
}
.align-middle{
  vertical-align: middle;
}
.font-Inter{
  font-family: Inter, sans-serif;
}
.font-sans{
  font-family: Inter, sans-serif;
}
.\!text-lg{
  font-size: 1.125rem !important;
  line-height: 1.75rem !important;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.\!text-xs{
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-\[2\.23rem\]{
  font-size: 2.23rem;
}
.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-\[22px\]{
  font-size: 22px;
}
.text-\[20px\]{
  font-size: 20px;
}
.text-\[32px\]{
  font-size: 32px;
}
.text-\[10px\]{
  font-size: 10px;
}
.text-\[8px\]{
  font-size: 8px;
}
.text-\[11px\]{
  font-size: 11px;
}
.text-\[15px\]{
  font-size: 15px;
}
.font-medium{
  font-weight: 500;
}
.font-bold{
  font-weight: 700;
}
.font-semibold{
  font-weight: 600;
}
.\!font-bold{
  font-weight: 700 !important;
}
.font-normal{
  font-weight: 400;
}
.\!font-normal{
  font-weight: 400 !important;
}
.font-light{
  font-weight: 300;
}
.uppercase{
  text-transform: uppercase;
}
.lowercase{
  text-transform: lowercase;
}
.capitalize{
  text-transform: capitalize;
}
.italic{
  font-style: italic;
}
.not-italic{
  font-style: normal;
}
.ordinal{
  --tw-ordinal: ordinal;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.tabular-nums{
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.diagonal-fractions{
  --tw-numeric-fraction: diagonal-fractions;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.leading-6{
  line-height: 1.5rem;
}
.leading-tight{
  line-height: 1.25;
}
.leading-relaxed{
  line-height: 1.625;
}
.leading-5{
  line-height: 1.25rem;
}
.leading-none{
  line-height: 1;
}
.leading-\[1\]{
  line-height: 1;
}
.leading-4{
  line-height: 1rem;
}
.leading-10{
  line-height: 2.5rem;
}
.leading-\[12px\]{
  line-height: 12px;
}
.leading-8{
  line-height: 2rem;
}
.leading-\[0\]{
  line-height: 0;
}
.tracking-tight{
  letter-spacing: -0.025em;
}
.tracking-wider{
  letter-spacing: 0.05em;
}
.tracking-\[0\.01em\]{
  letter-spacing: 0.01em;
}
.-tracking-\[1px\]{
  letter-spacing: -1px;
}
.text-slate-900{
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.text-slate-700{
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(104 118 138 / var(--tw-text-opacity));
}
.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}
.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity));
}
.text-indigo-600{
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity));
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.text-indigo-500{
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}
.text-slate-600{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}
.text-success-500{
  --tw-text-opacity: 1;
  color: rgb(80 199 147 / var(--tw-text-opacity));
}
.text-warning-500{
  --tw-text-opacity: 1;
  color: rgb(250 145 107 / var(--tw-text-opacity));
}
.text-danger-500{
  --tw-text-opacity: 1;
  color: rgb(241 89 92 / var(--tw-text-opacity));
}
.text-slate-800{
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity));
}
.text-slate-500{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}
.text-slate-400{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}
.text-slate-300{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.text-slate-50{
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity));
}
.text-secondary-500{
  --tw-text-opacity: 1;
  color: rgb(160 174 192 / var(--tw-text-opacity));
}
.text-primary-500{
  --tw-text-opacity: 1;
  color: #be9539;
}
.text-current{
  color: currentColor;
}
.\!text-white{
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}
.text-slate-100{
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity));
}
.text-primary-600{
  --tw-text-opacity: 1;
  color: rgb(63 94 223 / var(--tw-text-opacity));
}
.text-info-500{
  --tw-text-opacity: 1;
  color: rgb(12 231 250 / var(--tw-text-opacity));
}
.text-\[\#68768A\]{
  --tw-text-opacity: 1;
  color: rgb(104 118 138 / var(--tw-text-opacity));
}
.text-\[\#5743BE\]{
  --tw-text-opacity: 1;
  color: rgb(87 67 190 / var(--tw-text-opacity));
}
.text-opacity-10{
  --tw-text-opacity: 0.1;
}
.text-opacity-80{
  --tw-text-opacity: 0.8;
}
.text-opacity-75{
  --tw-text-opacity: 0.75;
}
.text-opacity-70{
  --tw-text-opacity: 0.7;
}
.underline{
  text-decoration-line: underline;
}
.antialiased{
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.placeholder-green-300::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(134 239 172 / var(--tw-placeholder-opacity));
}
.placeholder-opacity-60::placeholder{
  --tw-placeholder-opacity: 0.6;
}
.accent-black-500{
  accent-color: #111112;
}
.opacity-90{
  opacity: 0.9;
}
.opacity-0{
  opacity: 0;
}
.opacity-100{
  opacity: 1;
}
.opacity-50{
  opacity: 0.5;
}
.shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-base{
  --tw-shadow: 0px 0px 1px rgba(40, 41, 61, 0.08), 0px 0.5px 2px rgba(96, 97, 112, 0.16);
  --tw-shadow-colored: 0px 0px 1px var(--tw-shadow-color), 0px 0.5px 2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-deep{
  --tw-shadow: -2px 0px 8px rgba(0, 0, 0, 0.16);
  --tw-shadow-colored: -2px 0px 8px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-base2{
  --tw-shadow: 0px 2px 4px rgba(40, 41, 61, 0.04), 0px 8px 16px rgba(96, 97, 112, 0.16);
  --tw-shadow-colored: 0px 2px 4px var(--tw-shadow-color), 0px 8px 16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline{
  outline-style: solid;
}
.outline-offset-2{
  outline-offset: 2px;
}
.outline-primary-500{
  outline-color: #4669fa;
}
.ring{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-4{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-1{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-0{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-white{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}
.ring-primary-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(70 105 250 / var(--tw-ring-opacity));
}
.ring-slate-100{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(241 245 249 / var(--tw-ring-opacity));
}
.ring-danger-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(241 89 92 / var(--tw-ring-opacity));
}
.ring-success-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(80 199 147 / var(--tw-ring-opacity));
}
.ring-warning-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 145 107 / var(--tw-ring-opacity));
}
.ring-info-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(12 231 250 / var(--tw-ring-opacity));
}
.ring-secondary-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(160 174 192 / var(--tw-ring-opacity));
}
.ring-\[\#FFC155\]{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 193 85 / var(--tw-ring-opacity));
}
.ring-slate-900{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(15 23 42 / var(--tw-ring-opacity));
}
.ring-opacity-40{
  --tw-ring-opacity: 0.4;
}
.ring-opacity-30{
  --tw-ring-opacity: 0.3;
}
.ring-opacity-25{
  --tw-ring-opacity: 0.25;
}
.ring-opacity-70{
  --tw-ring-opacity: 0.7;
}
.ring-offset-2{
  --tw-ring-offset-width: 2px;
}
.ring-offset-blue-300{
  --tw-ring-offset-color: #93c5fd;
}
.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter-none{
  filter: none;
}
.backdrop-blur-\[40px\]{
  --tw-backdrop-blur: blur(40px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-filter{
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.delay-300{
  transition-delay: 300ms;
}
.duration-150{
  transition-duration: 150ms;
}
.duration-\[2s\]{
  transition-duration: 2s;
}
.duration-\[var\(--app-duration\)\]{
  transition-duration: var(--app-duration);
}
.duration-200{
  transition-duration: 200ms;
}
.duration-100{
  transition-duration: 100ms;
}
.duration-300{
  transition-duration: 300ms;
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
body{
  height: 100%;
  overflow-x: hidden;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}
.dark body{
  --tw-text-opacity: 1;
  color: #be9539;
}

.light body,
.skin-default body{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

html,
body{
  /* height: 100%; */
}

.app-wrapper{
  position: relative;
}

.card-border-b{
  margin-left: -1.5rem;
  margin-right: -1.5rem;
  margin-bottom: 1.5rem;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity));
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-bottom: 1.25rem;
}

.dark .card-border-b{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
}

.icon-list{
  position: relative;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  font-size: 20px;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark .icon-list{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

/* Content layout */
.page-content{
  padding: 15px;
  padding-bottom: 2rem;
}
@media (min-width: 768px){
  .page-content{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 1.5rem;
    padding-bottom: 37px;
  }
}

.layout-boxed .page-content{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 15px;
  padding-left: 15px;
}

@media (min-width: 640px){
  .layout-boxed .page-content{
    max-width: 640px;
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 768px){
  .layout-boxed .page-content{
    max-width: 768px;
  }
}

@media (min-width: 1024px){
  .layout-boxed .page-content{
    max-width: 1024px;
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 1280px){
  .layout-boxed .page-content{
    max-width: 1280px;
    padding-right: 15px;
    padding-left: 15px;
  }
}

.page-min-height {
  min-height: calc(var(--vh, 1vh) * 100 - 132px);
}

/*===========================
  Theme Customization
===========================*/
.settings-modal{
  padding-bottom: 6rem;
}
@media (min-width: 768px){
  .settings-modal{
    padding-bottom: 0px;
  }
}

.settings-modal h3{
  margin-bottom: 0.75rem;
  font-family: Inter, sans-serif;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .settings-modal h3{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.settings-modal .divider{
  height: 1px;
  width: 100%;
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.dark .settings-modal .divider{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}

.themeCustomization-checkInput{
  display: inline-block;
  height: 1rem;
  width: 1rem;
  flex: none;
  cursor: pointer;
  border-radius: 9999px;
  accent-color: #111112;
}

.dark .themeCustomization-checkInput{
  accent-color: #F4F5F7;
}

/*  flex-none c  transition-all duration-150 focus:border-red-500 focus:outline-none focus:ring-0
*/
.themeCustomization-checkInput-label{
  cursor: pointer;
  font-family: Inter, sans-serif;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}
[dir="ltr"] .themeCustomization-checkInput-label{
  padding-left: 0.5rem;
}
[dir="rtl"] .themeCustomization-checkInput-label{
  padding-right: 0.5rem;
}
.dark .themeCustomization-checkInput-label{
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}

/* Default Card Style */
.card-title{
  font-family: Inter, sans-serif;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 24px;
}
.dark .card-title{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
@media (min-width: 768px){
  .card-title{
    font-size: 1.25rem;
    line-height: 1.75rem;
    line-height: 28px;
  }
}

.card-text{
  font-family: Inter, sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .card-text{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.card-title2{
  margin-bottom: 0.5rem;
  font-family: Inter, sans-serif;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .card-title2{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.card-subtitle{
  margin-top: 0.25rem;
  font-family: Inter, sans-serif;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .card-subtitle{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

/*===========================
  Typography Page
=============================*/
/* Order List Style */
.custom-list{
  position: relative;
  margin-left: -0.25rem;
  margin-right: -0.25rem;
  list-style-type: none;
  padding-left: 2rem;
}

.custom-list li{
  position: relative;
}

.custom-list li::before{
  content: var(--tw-content);
  position: absolute;
}

[dir="ltr"] .custom-list li::before{
  content: var(--tw-content);
  left: 0px;
}

[dir="rtl"] .custom-list li::before{
  content: var(--tw-content);
  right: 0px;
}

ol.custom-list ol,
ul.custom-list ul{
  margin-top: 0.75rem;
}

[dir="ltr"] ol.custom-list ol li,[dir="ltr"]
ul.custom-list ul li{
  padding-left: 1.5rem;
}

[dir="rtl"] ol.custom-list ol li,[dir="rtl"]
ul.custom-list ul li{
  padding-right: 1.5rem;
}

.list-by-numbering {
  counter-reset: list;
}

.list-by-numbering li{
  position: relative;
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

[dir="ltr"] .list-by-numbering li{
  padding-left: 1.3em;
}

[dir="rtl"] .list-by-numbering li{
  padding-right: 1.3em;
}

.list-by-numbering li::before {
  counter-increment: list;
  content: counters(list, ".") "." important;
}

.list-by-slash li{
  position: relative;
  padding-left: 1rem;
}

.list-by-slash li::before {
  left: 6px;
  content: "-" important;
}

blockquote{
  border-left-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(104 118 138 / var(--tw-border-opacity));
  padding-left: 1.25rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-style: italic;
}

/*===========================
  Colors Page
  =============================*/
.colors_parent div{
  margin-right: 0.75rem;
  margin-bottom: 0.75rem;
}

/* Badges */
.badge{
  display: inline-flex;
  white-space: nowrap;
  border-radius: .358rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  vertical-align: baseline;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 600;
  text-transform: capitalize;
}
.badge.pill{
  border-radius: 999px;
}

/* Tabs And Accordion */
#tabs-tab .nav-link.active{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(70 105 250 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(70 105 250 / var(--tw-text-opacity));
}
.dark #tabs-tab .nav-link.active{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(255 255 255 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark #pills-tabHorizontal .nav-link.active,.dark  #pills-tabVertical .nav-link.active{
  --tw-bg-opacity: 1;
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
}

.stiped-bar{
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 0px, transparent 50%, rgba(255, 255, 255, 0.15) 0px, rgba(255, 255, 255, 0.15) 75%, transparent 0px, transparent);
  background-size: .857rem .857rem;
  background-repeat: repeat;
}

@keyframes progress-bar-stripes {
  0% {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}
.animated-strip {
  animation: progress-bar-stripes 1s linear infinite;
}

/* pagination */
.p-active{
  --tw-bg-opacity: 1;
  background-color: rgb(17 17 18 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.date-label{
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.dark .date-label{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.date-text{
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .date-text{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.dark .ql-picker-label{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .ql-stroke{
  stroke: #fff;
}

/* Extra */
.light body,
.skin-bordered body{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.dark body{
  --tw-bg-opacity: 1;
  background-color: #ffffff;
}

html.dark #moonIcon,
html.dark .black_logo{
  display: none;
}

html.light #sunIcon,
html.light .white_logo{
  display: none;
}

html.light #moonIcon,
html.light .black_logo{
  display: block;
}

html.dark #sunIcon,
html.dark .white_logo{
  display: block;
}

html.semiDark .white_logo{
  display: block;
}

html.semiDark .black_logo{
  display: none;
}

.simplebar-scrollbar:before{
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity));
}

.dark .simplebar-scrollbar:before{
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity));
}

.margin-0{
  margin-left: 0px;
  margin-right: 0px;
}

.nav-shadow {
  background: linear-gradient(rgb(255, 255, 255) 5%, rgba(255, 255, 255, 0.75) 45%, rgba(255, 255, 255, 0.2) 80%, transparent);
}

.horizontalMenu .sidebar-wrapper,
.horizontalMenu #menuCollapse,
.horizontalMenu #menuHidden,
.horizontalMenu #searchBtn{
  display: none;
}

.horizontalMenu .app-header,
.horizontalMenu .content-wrapper,
.horizontalMenu .site-footer{
  margin-left: 0px;
  margin-right: 0px;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

#headerLogo{
  display: none;
}

.horizontalMenu #headerLogo{
  display: none;
  padding: 0px;
}

@media (min-width: 1280px){
  .horizontalMenu #headerLogo{
    display: block;
  }
}

.horizontalMenu .horizontal_menu{
  display: none;
}

@media (min-width: 1280px){
  .horizontalMenu .horizontal_menu{
    display: block;
  }
}

.slider{
  position: relative;
  overflow: hidden;
}

.slider .owl-nav{
  position: absolute;
  top: 50%;
  left: 50%;
  display: flex;
  width: 98%;
  --tw-translate-y: -50%;
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  justify-content: space-between;
}

.slider .owl-nav button{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  font-size: 24px !important;
}

.slider .owl-dots{
  position: absolute;
  bottom: 1.25rem;
  left: 50%;
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.slider .owl-dots .owl-dot{
  margin-left: 0.25rem;
  margin-right: 0.25rem;
  height: 2px;
  width: 1.25rem;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.4;
}

.slider .owl-dots .owl-dot.active{
  --tw-bg-opacity: 1;
}

.grayScale{
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.offcanvas.show{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/*======================
ToolTip And PopOver
======================*/
/* Primary Color ToolTip */
.tippy-box[data-theme~=primary]{
  --tw-bg-opacity: 1;
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.tippy-box[data-theme~=primary][data-placement^=top] > .tippy-arrow::before {
  border-top-color: #4669fa;
}

/* Secondary Color ToolTip */
.tippy-box[data-theme~=secondary]{
  --tw-bg-opacity: 1;
  background-color: rgb(160 174 192 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.tippy-box[data-theme~=secondary][data-placement^=top] > .tippy-arrow::before {
  border-top-color: #a0aec0;
}

/* success Color ToolTip */
.tippy-box[data-theme~=success]{
  --tw-bg-opacity: 1;
  background-color: rgb(80 199 147 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.tippy-box[data-theme~=success][data-placement^=top] > .tippy-arrow::before {
  border-top-color: #50c793;
}

/* info Color ToolTip */
.tippy-box[data-theme~=info]{
  --tw-bg-opacity: 1;
  background-color: rgb(12 231 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.tippy-box[data-theme~=info][data-placement^=top] > .tippy-arrow::before {
  border-top-color: #0ce7fa;
}

/* warning Color ToolTip */
.tippy-box[data-theme~=warning]{
  --tw-bg-opacity: 1;
  background-color: rgb(250 145 107 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.tippy-box[data-theme~=warning][data-placement^=top] > .tippy-arrow::before {
  border-top-color: #fa916b;
}

/* danger Color ToolTip */
.tippy-box[data-theme~=danger]{
  --tw-bg-opacity: 1;
  background-color: rgb(241 89 92 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.tippy-box[data-theme~=danger][data-placement^=top] > .tippy-arrow::before {
  border-top-color: #f1595c;
}

/* dark Color ToolTip */
.tippy-box[data-theme~=dark]{
  --tw-bg-opacity: 1;
  background-color: rgb(17 17 18 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.tippy-box[data-theme~=dark][data-placement^=top] > .tippy-arrow::before {
  border-top-color: #111112;
}

/* light Color ToolTip */
.tippy-box[data-theme~=light]{
  --tw-bg-opacity: 1;
  background-color: rgb(244 245 247 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(17 17 18 / var(--tw-text-opacity));
}

.tippy-box[data-theme~=light][data-placement^=top] > .tippy-arrow::before {
  border-top-color: #ececec;
}

/* White Color ToolTip */
.tippy-box[data-theme~=white]{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(17 17 18 / var(--tw-text-opacity));
}

.tippy-box[data-theme~=white][data-placement^=top] > .tippy-arrow::before {
  border-top-color: #ffffff;
}

/*Tippy js Scale*/
.tippy-box[data-animation=scale][data-placement^=top] {
  transform-origin: bottom;
}

.tippy-box[data-animation=scale][data-placement^=bottom] {
  transform-origin: top;
}

.tippy-box[data-animation=scale][data-placement^=left] {
  transform-origin: right;
}

.tippy-box[data-animation=scale][data-placement^=right] {
  transform-origin: left;
}

.tippy-box[data-animation=scale][data-state=hidden] {
  transform: scale(0.5);
  opacity: 0;
}

/*   tippy Js shift Away  */
.tippy-box[data-animation=shift-away][data-state=hidden] {
  opacity: 0;
}

.tippy-box[data-animation=shift-away][data-state=hidden][data-placement^=top] {
  transform: translateY(10px);
}

.tippy-box[data-animation=shift-away][data-state=hidden][data-placement^=bottom] {
  transform: translateY(-10px);
}

.tippy-box[data-animation=shift-away][data-state=hidden][data-placement^=left] {
  transform: translateX(10px);
}

.tippy-box[data-animation=shift-away][data-state=hidden][data-placement^=right] {
  transform: translateX(-10px);
}

/*   tippy Js shift Toward  */
.tippy-box[data-animation=shift-toward][data-state=hidden] {
  opacity: 0;
}

.tippy-box[data-animation=shift-toward][data-state=hidden][data-placement^=top] {
  transform: translateY(-10px);
}

.tippy-box[data-animation=shift-toward][data-state=hidden][data-placement^=bottom] {
  transform: translateY(10px);
}

.tippy-box[data-animation=shift-toward][data-state=hidden][data-placement^=left] {
  transform: translateX(-10px);
}

.tippy-box[data-animation=shift-toward][data-state=hidden][data-placement^=right] {
  transform: translateX(10px);
}

/*   tippy Js perspective */
.tippy-box[data-animation=perspective][data-placement^=top] {
  transform-origin: bottom;
}

.tippy-box[data-animation=perspective][data-placement^=top][data-state=visible] {
  transform: perspective(700px);
}

.tippy-box[data-animation=perspective][data-placement^=top][data-state=hidden] {
  transform: perspective(700px) translateY(8px) rotateX(60deg);
}

.tippy-box[data-animation=perspective][data-placement^=bottom] {
  transform-origin: top;
}

.tippy-box[data-animation=perspective][data-placement^=bottom][data-state=visible] {
  transform: perspective(700px);
}

.tippy-box[data-animation=perspective][data-placement^=bottom][data-state=hidden] {
  transform: perspective(700px) translateY(-8px) rotateX(-60deg);
}

.tippy-box[data-animation=perspective][data-placement^=left] {
  transform-origin: right;
}

.tippy-box[data-animation=perspective][data-placement^=left][data-state=visible] {
  transform: perspective(700px);
}

.tippy-box[data-animation=perspective][data-placement^=left][data-state=hidden] {
  transform: perspective(700px) translateX(8px) rotateY(-60deg);
}

.tippy-box[data-animation=perspective][data-placement^=right] {
  transform-origin: left;
}

.tippy-box[data-animation=perspective][data-placement^=right][data-state=visible] {
  transform: perspective(700px);
}

.tippy-box[data-animation=perspective][data-placement^=right][data-state=hidden] {
  transform: perspective(700px) translateX(-8px) rotateY(60deg);
}

.tippy-box[data-animation=perspective][data-state=hidden] {
  opacity: 0;
}

/* Tippy js HTML  */
#templateX .tippy-content {
  padding: 0 important;
}

@media screen and (max-width: 575px) {
  .simplebar-content {
    padding-bottom: 50px !important;
  }
}
.alert{
  border-radius: 0.375rem;
  padding-top: 18px;
  padding-bottom: 18px;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
}

.alert-primary{
  --tw-bg-opacity: 1;
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.alert-primary.light-mode{
  --tw-bg-opacity: 14%;
  --tw-text-opacity: 1;
  color: rgb(70 105 250 / var(--tw-text-opacity));
}

.alert-secondary{
  --tw-bg-opacity: 1;
  background-color: rgb(160 174 192 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.alert-secondary.light-mode{
  --tw-bg-opacity: 14%;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.alert-success{
  --tw-bg-opacity: 1;
  background-color: rgb(80 199 147 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.alert-success.light-mode{
  --tw-bg-opacity: 14%;
  --tw-text-opacity: 1;
  color: rgb(80 199 147 / var(--tw-text-opacity));
}

.alert-danger{
  --tw-bg-opacity: 1;
  background-color: rgb(241 89 92 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.alert-danger.light-mode{
  --tw-bg-opacity: 14%;
  --tw-text-opacity: 1;
  color: rgb(241 89 92 / var(--tw-text-opacity));
}

.alert-warning{
  --tw-bg-opacity: 1;
  background-color: rgb(250 145 107 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.alert-warning.light-mode{
  --tw-bg-opacity: 14%;
  --tw-text-opacity: 1;
  color: rgb(250 145 107 / var(--tw-text-opacity));
}

.alert-info{
  --tw-bg-opacity: 1;
  background-color: rgb(12 231 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.alert-info.light-mode{
  --tw-bg-opacity: 14%;
  --tw-text-opacity: 1;
  color: rgb(12 231 250 / var(--tw-text-opacity));
}

.alert-light{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.alert-light.light-mode{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}

.alert-dark{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.alert-dark.light-mode{
  --tw-bg-opacity: 54%;
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity));
}

.alert-outline-primary{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(70 105 250 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(70 105 250 / var(--tw-text-opacity));
}

.alert-outline-secondary{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(160 174 192 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(160 174 192 / var(--tw-text-opacity));
}

.alert-outline-success{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(80 199 147 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(80 199 147 / var(--tw-text-opacity));
}

.alert-outline-danger{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(241 89 92 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(241 89 92 / var(--tw-text-opacity));
}

.alert-outline-warning{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(250 145 107 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(250 145 107 / var(--tw-text-opacity));
}

.alert-outline-info{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(12 231 250 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(12 231 250 / var(--tw-text-opacity));
}

.alert-outline-light{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.alert-outline-dark{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(15 23 42 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark .alert-outline-dark{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.badge{
  display: inline-flex;
  white-space: nowrap;
  border-radius: .358rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  vertical-align: baseline;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 600;
  text-transform: capitalize;
}
.badge.pill{
  border-radius: 999px;
}

/*===========================
  Buttons Page
  =============================*/
.btn{
  position: relative;
  z-index: 0;
  white-space: normal;
  border-radius: 0.25rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 10px;
  padding-bottom: 10px;
  font-family: Inter, sans-serif;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: capitalize;
  line-height: 1.5rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
@media (min-width: 768px){
  .btn{
    white-space: nowrap;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
}

.btn.btn-xl{
  padding-left: 1.75rem;
  padding-right: 1.75rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 16px;
}

@media (min-width: 768px){
  .btn.btn-xl{
    padding-left: 2.5rem;
    padding-right: 2.5rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

.btn.btn-sm{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.75rem;
  line-height: 1rem;
}

@media (min-width: 768px){
  .btn.btn-sm{
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

.btn.block-btn{
  display: block;
  width: 100%;
  text-align: center;
}

.btn.block-btn span{
  justify-content: center;
}

.btn-group-example{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
}

.btn-group-example button{
  margin-bottom: 0.75rem;
}

[dir="ltr"] .btn-group-example button{
  margin-right: 1.25rem;
}

[dir="rtl"] .btn-group-example button{
  margin-left: 1.25rem;
}

/* Basic Button */
.btn-dark{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(15 23 42 / var(--tw-ring-opacity));
}
.btn-dark:hover{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 0.8;
  --tw-ring-offset-width: 1px;
}
.dark .btn-dark{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
.dark .btn-dark:hover{
  --tw-bg-opacity: 0.7;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 0px;
}

.btn-primary{
  --tw-bg-opacity: 1;
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(70 105 250 / var(--tw-ring-opacity));
}

.btn-primary:hover{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 0.8;
  --tw-ring-offset-width: 1px;
}

.dark .btn-primary:hover{
  --tw-bg-opacity: 0.7;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 0px;
}

.btn-secondary{
  --tw-bg-opacity: 1;
  background-color: rgb(160 174 192 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(160 174 192 / var(--tw-ring-opacity));
}

.btn-secondary:hover{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 0.8;
  --tw-ring-offset-width: 1px;
}

.dark .btn-secondary:hover{
  --tw-bg-opacity: 0.7;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 0px;
}

.btn-success{
  --tw-bg-opacity: 1;
  background-color: rgb(80 199 147 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(80 199 147 / var(--tw-ring-opacity));
}

.btn-success:hover{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 0.8;
  --tw-ring-offset-width: 1px;
}

.dark .btn-success:hover{
  --tw-bg-opacity: 0.7;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 0px;
}

.btn-info{
  --tw-bg-opacity: 1;
  background-color: rgb(12 231 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(12 231 250 / var(--tw-ring-opacity));
}

.btn-info:hover{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 0.8;
  --tw-ring-offset-width: 1px;
}

.dark .btn-info:hover{
  --tw-bg-opacity: 0.7;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 0px;
}

.btn-warning{
  --tw-bg-opacity: 1;
  background-color: rgb(250 145 107 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 145 107 / var(--tw-ring-opacity));
}

.btn-warning:hover{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 0.8;
  --tw-ring-offset-width: 1px;
}

.dark .btn-warning:hover{
  --tw-bg-opacity: 0.7;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 0px;
}

.btn-danger{
  --tw-bg-opacity: 1;
  background-color: rgb(241 89 92 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(241 89 92 / var(--tw-ring-opacity));
}

.btn-danger:hover{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 0.8;
  --tw-ring-offset-width: 1px;
}

.dark .btn-danger:hover{
  --tw-bg-opacity: 0.7;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 0px;
}

.btn-light{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 234 255 / var(--tw-ring-opacity));
}

.btn-light:hover{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 0.8;
  --tw-ring-offset-width: 1px;
}

.dark .btn-light:hover{
  --tw-bg-opacity: 0.7;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 0px;
}

.btn-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 234 255 / var(--tw-ring-opacity));
}

.btn-white:hover{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 0.8;
  --tw-ring-offset-width: 1px;
}

.dark .btn-white:hover{
  --tw-bg-opacity: 0.7;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 0px;
}

/* Outline Button */
.btn-outline-dark{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(17 17 18 / var(--tw-border-opacity));
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.btn-outline-dark:hover{
  --tw-border-opacity: 1;
  border-color: rgb(17 17 18 / var(--tw-border-opacity));
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.05;
}
.dark .btn-outline-dark{
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.btn-outline-dark.active{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dark .btn-outline-dark.active{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.btn-outline-dark .active{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .btn-outline-dark .active{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

[aria-expanded=true] > .btn-outline-dark{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-outline-primary{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(70 105 250 / var(--tw-border-opacity));
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(70 105 250 / var(--tw-text-opacity));
}

.btn-outline-primary:hover{
  --tw-border-opacity: 1;
  border-color: rgb(70 105 250 / var(--tw-border-opacity));
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.05;
}

.btn-outline-primary .active{
  --tw-bg-opacity: 1;
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

[aria-expanded=true] > .btn-outline-primary{
  --tw-bg-opacity: 1;
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-outline-secondary{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(160 174 192 / var(--tw-border-opacity));
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(160 174 192 / var(--tw-text-opacity));
}

.btn-outline-secondary:hover{
  --tw-border-opacity: 1;
  border-color: rgb(160 174 192 / var(--tw-border-opacity));
  background-color: rgb(160 174 192 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.05;
}

.btn-outline-secondary .active{
  --tw-bg-opacity: 1;
  background-color: rgb(160 174 192 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

[aria-expanded=true] > .btn-outline-secondary{
  --tw-bg-opacity: 1;
  background-color: rgb(160 174 192 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-outline-success{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(80 199 147 / var(--tw-border-opacity));
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(80 199 147 / var(--tw-text-opacity));
}

.btn-outline-success:hover{
  --tw-border-opacity: 1;
  border-color: rgb(80 199 147 / var(--tw-border-opacity));
  background-color: rgb(80 199 147 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.05;
}

.btn-outline-success .active{
  --tw-bg-opacity: 1;
  background-color: rgb(80 199 147 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

[aria-expanded=true] > .btn-outline-success{
  --tw-bg-opacity: 1;
  background-color: rgb(80 199 147 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-outline-info{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(12 231 250 / var(--tw-border-opacity));
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(12 231 250 / var(--tw-text-opacity));
}

.btn-outline-info:hover{
  --tw-border-opacity: 1;
  border-color: rgb(12 231 250 / var(--tw-border-opacity));
}

.btn-outline-info .active{
  --tw-bg-opacity: 1;
  background-color: rgb(12 231 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

[aria-expanded=true] > .btn-outline-info{
  --tw-bg-opacity: 1;
  background-color: rgb(12 231 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-outline-warning{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(250 145 107 / var(--tw-border-opacity));
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(250 145 107 / var(--tw-text-opacity));
}

.btn-outline-warning:hover{
  --tw-border-opacity: 1;
  border-color: rgb(250 145 107 / var(--tw-border-opacity));
  background-color: rgb(250 145 107 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.05;
}

.btn-outline-warning .active{
  --tw-bg-opacity: 1;
  background-color: rgb(250 145 107 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

[aria-expanded=true] > .btn-outline-warning{
  --tw-bg-opacity: 1;
  background-color: rgb(250 145 107 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-outline-danger{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(241 89 92 / var(--tw-border-opacity));
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(241 89 92 / var(--tw-text-opacity));
}

.btn-outline-danger:hover{
  --tw-border-opacity: 1;
  border-color: rgb(241 89 92 / var(--tw-border-opacity));
  background-color: rgb(241 89 92 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.05;
}

.btn-outline-danger .active{
  --tw-bg-opacity: 1;
  background-color: rgb(241 89 92 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

[aria-expanded=true] > .btn-outline-danger{
  --tw-bg-opacity: 1;
  background-color: rgb(241 89 92 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-outline-light{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(224 234 255 / var(--tw-border-opacity));
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.btn-outline-light:hover{
  --tw-border-opacity: 1;
  border-color: rgb(224 234 255 / var(--tw-border-opacity));
  background-color: rgb(224 234 255 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.05;
}

.dark .btn-outline-light{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-outline-light .active{
  --tw-bg-opacity: 1;
  background-color: rgb(224 234 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

[aria-expanded=true] > .btn-outline-light{
  --tw-bg-opacity: 1;
  background-color: rgb(224 234 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

/* light color */
.btn.light{
  --tw-bg-opacity: 15%;
  --tw-ring-opacity: 0.3;
}
.dark .btn.light:hover{
  --tw-bg-opacity: 0.1;
}

.btn-primary .light{
  --tw-text-opacity: 1;
  color: rgb(70 105 250 / var(--tw-text-opacity));
}

.dark .btn-primary .light:hover{
  --tw-bg-opacity: 0.1;
}

.btn-secondary.light{
  --tw-text-opacity: 1;
  color: rgb(160 174 192 / var(--tw-text-opacity));
}

.dark .btn-secondary.light:hover{
  --tw-bg-opacity: 0.1;
}

.btn-success.light{
  --tw-text-opacity: 1;
  color: rgb(80 199 147 / var(--tw-text-opacity));
}

.dark .btn-success.light:hover{
  --tw-bg-opacity: 0.1;
}

.btn-info.light{
  --tw-text-opacity: 1;
  color: rgb(12 231 250 / var(--tw-text-opacity));
}

.dark .btn-info.light:hover{
  --tw-bg-opacity: 0.1;
}

.btn-warning.light{
  --tw-text-opacity: 1;
  color: rgb(250 145 107 / var(--tw-text-opacity));
}

.dark .btn-warning.light:hover{
  --tw-bg-opacity: 0.1;
}

.btn-danger.light{
  --tw-text-opacity: 1;
  color: rgb(241 89 92 / var(--tw-text-opacity));
}

.dark .btn-danger.light:hover{
  --tw-bg-opacity: 0.1;
}

.btn-light.light{
  --tw-text-opacity: 0.8;
}

.dark .btn-light.light{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.dark .btn-light.light:hover{
  --tw-bg-opacity: 0.1;
}

/* Group Buttons */
.groupButtons{
  display: inline-flex;
  align-items: center;
  overflow: hidden;
  border-radius: 0.375rem;
}

.groupButtons .btn{
  margin-left: 0px;
  margin-right: 0px;
  border-radius: 0px;
  --tw-bg-opacity: 0.9;
}

.groupButtons .btn:hover{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 0;
  --tw-ring-offset-width: 0px;
}

.dark .groupButtons .btn:hover{
  --tw-bg-opacity: 0.7;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 0px;
}

.groupButtons .btn.active{
  --tw-bg-opacity: 1;
}

.outline-buttons .btn:first-child{
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.outline-buttons .btn:last-child{
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.outline-buttons .btn:hover{
  --tw-bg-opacity: 0.1;
}

.outline-buttons .btn.active{
  --tw-bg-opacity: 1;
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link{
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
  text-decoration-line: underline;
}

.dark .btn-link{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link .white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.action-btn{
  display: flex;
  height: 1.5rem;
  width: 1.5rem;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
}

.dark .action-btn{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
}

.invocie-btn{
  margin-right: 0.75rem;
  margin-bottom: 1rem;
}

.invocie-btn:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity));
}

.dark .invocie-btn:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}

.form-label{
  margin-bottom: 0.5rem;
  display: block;
  width: 100%;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

[dir="rtl"] .form-label{
  display: block;
  text-align: right;
}

.dark .form-label{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.inline-inputLabel{
  position: absolute;
  left: 0px;
  top: 50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.form-control{
  display: block;
  width: 100%;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.form-control::placeholder{
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.form-control:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(71 85 105 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.9;
}

.dark .form-control{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.dark .form-control::placeholder{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.dark .form-control:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(15 23 42 / var(--tw-ring-opacity));
}

.input-description{
  margin-top: 0.5rem;
  display: block;
  font-size: 0.75rem;
  font-weight: 300;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(160 174 192 / var(--tw-text-opacity));
}

.fromGroup{
  position: relative;
}
.fromGroup.has-error .form-control{
  --tw-border-opacity: 1;
  border-color: rgb(241 89 92 / var(--tw-border-opacity));
}
.fromGroup.has-error .form-control:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(241 89 92 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.9;
}
.fromGroup.is-valid .form-control{
  --tw-border-opacity: 1;
  border-color: rgb(80 199 147 / var(--tw-border-opacity));
}
.fromGroup.is-valid .form-control:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(80 199 147 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.9;
}

.form-control[readonly]{
  cursor: pointer;
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.form-control[readonly]::placeholder{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.dark .form-control[readonly]{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}

.form-control[disabled]{
  cursor: not-allowed;
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity));
}

.form-control[disabled]::placeholder{
  --tw-text-opacity: 0.6;
}

.dark .form-control[disabled]{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}

.checkbox-area input:checked + span{
  --tw-bg-opacity: 1;
  background-color: rgb(17 17 18 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 17 18 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.checkbox-area input:checked + span img{
  opacity: 1;
}

.primary-checkbox input:checked + span{
  --tw-bg-opacity: 1;
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(70 105 250 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.secondary-checkbox input:checked + span{
  --tw-bg-opacity: 1;
  background-color: rgb(160 174 192 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(160 174 192 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.info-checkbox input:checked + span{
  --tw-bg-opacity: 1;
  background-color: rgb(12 231 250 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(12 231 250 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.success-checkbox input:checked + span{
  --tw-bg-opacity: 1;
  background-color: rgb(80 199 147 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(80 199 147 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.warning-checkbox input:checked + span{
  --tw-bg-opacity: 1;
  background-color: rgb(250 145 107 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 145 107 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.danger-checkbox input:checked + span{
  --tw-bg-opacity: 1;
  background-color: rgb(241 89 92 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(241 89 92 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.basicRadio input:checked + span{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(70 105 250 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.primary-radio input:checked + span{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(70 105 250 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.secondary-radio input:checked + span{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(160 174 192 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(160 174 192 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.info-radio input:checked + span{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(12 231 250 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(12 231 250 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.success-radio input:checked + span{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(80 199 147 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(80 199 147 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.warning-radio input:checked + span{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(250 145 107 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 145 107 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.danger-radio input:checked + span{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(241 89 92 / var(--tw-bg-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(241 89 92 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 1px;
}

.error{
  margin-top: 0.25rem;
  display: inline-block;
  font-family: Inter, sans-serif;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(241 89 92 / var(--tw-text-opacity));
}

#passwordshow{
  display: none;
}

#tooltipValidation span.error{
  margin-top: 0.25rem;
  display: inline-block;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(241 89 92 / var(--tw-bg-opacity));
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.25rem;
  padding-right: 0.25rem;
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-vtd-primary-500-600 {
  color: #0f172a !important;
}

.bg-vtd-primary-500-500 {
  background-color: #0f172a !important;
}

.text-vtd-primary-500-500 {
  color: #0f172a !important;
}

.dark .text-vtd-primary-500-600 {
  color: #f8fafc !important;
}
.dark .text-vtd-primary-500-500 {
  color: #f8fafc !important;
}
.dark .bg-vtd-primary-500-500 {
  background-color: #334155 !important;
}

.file-control{
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.file-control::placeholder{
  font-weight: 400;
}

.file-control:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(15 23 42 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.9;
}

[dir="ltr"] .file-control{
  padding-left: 0.75rem;
}

[dir="rtl"] .file-control{
  padding-right: 0.75rem;
}

.dark .file-control{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .file-control:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(15 23 42 / var(--tw-ring-opacity));
}

.badge-title{
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 3px;
  padding-bottom: 3px;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.select2-container .select2-selection--single{
  display: flex;
  height: 2.5rem;
  align-items: center;
}

.select2-container .select2-selection--single .select2-selection__arrow b{
  position: relative;
  top: 1.25rem;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 1.5rem;
  font-family: Inter, sans-serif;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  border-left: 1px solid rgba(24, 24, 24, 0.4901960784);
  border-right: unset;
  right: 0px;
  left: auto;
  height: 100%;
  border-radius: 0px;
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity));
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .select2-selection{
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.dark .select2-container--default .select2-selection--single .select2-selection__rendered{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .select2-dropdown.select2-dropdown--below{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

#file-preview{
  display: flex;
  flex-wrap: wrap;
}

#file-preview > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

#file-preview img{
  margin-top: 1rem;
  height: 10rem;
  width: 10rem;
  overflow: hidden;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
  object-fit: contain;
  padding: 0.25rem;
}

.dz-error-message {
  display: none !important;
  opacity: 0 !important;
}

.dz-remove {
  margin-top: 4px !important;
}

.flatpickr-months, .flatpickr-weekdays {
  background-color: #E2E8F0;
}
.flatpickr-months .flatpickr-weekday, .flatpickr-weekdays .flatpickr-weekday {
  font-family: "Inter";
  font-weight: 700;
}

.flatpickr-day{
  font-family: Inter, sans-serif;
}

.flatpickr-day.selected {
  background-color: #202020 !important;
  border-color: #202020 !important;
  color: #ffffff;
}

.flatpickr-day {
  font-family: "Inter";
}

.input-group-control{
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.input-group-control::placeholder{
  font-weight: 300;
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.input-group-control:focus{
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.dark .input-group-control{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .input-group-control::placeholder{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.dark .input-group-control:focus{
  --tw-border-opacity: 1;
  border-color: rgb(15 23 42 / var(--tw-border-opacity));
}

.fromGroup2.has-error .input-group-control{
  --tw-border-opacity: 1;
  border-color: rgb(241 89 92 / var(--tw-border-opacity));
}

.fromGroup2.has-error .input-group-control:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(241 89 92 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.9;
}
.fromGroup2.is-valid .input-group-control{
  --tw-border-opacity: 1;
  border-color: rgb(80 199 147 / var(--tw-border-opacity));
}
.fromGroup2.is-valid .input-group-control:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(80 199 147 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.9;
}

.input-group-control[readonly]{
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.dark .input-group-control[readonly]{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}

.input-group-control[disabled]{
  cursor: not-allowed;
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.input-group-control[disabled]::placeholder{
  --tw-text-opacity: 0.6;
}

.dark .input-group-control[disabled]{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}

.input-group-text{
  display: flex;
  align-items: center;
  justify-content: center;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 300;
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

[dir="ltr"] .input-group-text{
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

[dir="rtl"] .input-group-text{
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.dark .input-group-text{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}

[dir="ltr"] .inputGroup.has-prepend .input-group-control{
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left-width: 0px;
}

[dir="rtl"] .inputGroup.has-prepend .input-group-control{
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right-width: 0px;
}

.inputGroup.has-prepend-slot .input-group-control:focus{
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

[dir="ltr"] .inputGroup.has-prepend-slot .input-group-control{
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left-width: 0px;
}

[dir="rtl"] .inputGroup.has-prepend-slot .input-group-control{
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right-width: 0px;
}

.dark .inputGroup.has-prepend-slot .input-group-control:focus{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
}
.inputGroup.has-append-slot .input-group-control:focus{
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
[dir="ltr"] .inputGroup.has-append-slot .input-group-control{
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right-width: 0px;
}
[dir="rtl"] .inputGroup.has-append-slot .input-group-control{
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left-width: 0px;
}
.dark .inputGroup.has-append-slot .input-group-control:focus{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
}

.inputGroup.has-append .input-group-control{
  border-bottom-right-radius: 0;
}

[dir="ltr"] .inputGroup.has-append .input-group-control{
  border-top-right-radius: 0;
  border-right-width: 0px;
}

[dir="rtl"] .inputGroup.has-append .input-group-control{
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left-width: 0px;
}
[dir="ltr"] .inputGroup.has-append .input-group-addon.right .input-group-text{
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
[dir="rtl"] .inputGroup.has-append .input-group-addon.right .input-group-text{
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.inputGroup:focus-within .input-group-text{
  --tw-border-opacity: 1;
  border-color: rgb(17 17 18 / var(--tw-border-opacity));
}

.dark .inputGroup:focus-within .input-group-text{
  --tw-border-opacity: 1;
  border-color: rgb(15 23 42 / var(--tw-border-opacity));
}

/* .merged .inputGroup:focus-within .input-group-text {
} */
.inputGroup.is-invalid .input-group-text{
  --tw-border-opacity: 1;
  border-color: rgb(241 89 92 / var(--tw-border-opacity));
}
.inputGroup.is-invalid:focus-within .input-group-text{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(241 89 92 / var(--tw-ring-opacity));
}
.inputGroup.is-valid .input-group-text{
  --tw-border-opacity: 1;
  border-color: rgb(80 199 147 / var(--tw-border-opacity));
}
.inputGroup.is-valid:focus-within .input-group-text{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(80 199 147 / var(--tw-ring-opacity));
}

.prepend-slot .btn,
.append-slot .btn{
  margin-left: -0.75rem;
  margin-right: -0.75rem;
  height: 100%;
  align-items: center;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  padding-top: 0px;
  padding-bottom: 0px;
}

.prepend-slot .btn:hover,
.append-slot .btn:hover{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.prepend-slot > div,
.prepend-slot button,
.append-slot > div,
.append-slot button{
  height: 100%;
}

.input-group-addon.right .append-slot .btn{
  margin-left: -0.75rem;
  margin-right: -0.75rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

[dir="ltr"] .merged .input-group-addon .input-group-text{
  border-right-width: 0px;
  padding-right: 0px;
}

[dir="rtl"] .merged .input-group-addon .input-group-text{
  border-left-width: 0px;
  padding-left: 0px;
}
[dir="ltr"] .merged .input-group-addon.right .input-group-text{
  border-left-width: 0px;
  border-right-width: 1px;
  padding-right: 0.75rem;
  padding-left: 0px;
}
[dir="rtl"] .merged .input-group-addon.right .input-group-text{
  border-right-width: 0px;
  border-left-width: 1px;
  padding-left: 0.75rem;
  padding-right: 0px;
}

h1{
  font-size: 3.75rem;
  line-height: 1;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark h1{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

h2{
  font-size: 3rem;
  line-height: 1;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark h2{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

h3{
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark h3{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

h4{
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark h4{
  --tw-text-opacity: 1;
  /* color: rgb(203 213 225 / var(--tw-text-opacity)); */
}

h5{
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark h5{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

h6{
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 500;
  line-height: 20px;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark h6{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.display-1{
  font-size: 70px;
  font-weight: 600;
  line-height: 80px;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark .display-1{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.display-2{
  font-size: 48px;
  font-weight: 600;
  line-height: 58px;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark .display-2{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.display-3{
  font-size: 40px;
  font-weight: 600;
  line-height: 48px;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark .display-3{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.display-4{
  font-size: 40px;
  font-weight: 400;
  line-height: 48px;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark .display-4{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

blockquote{
  border-left-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(104 118 138 / var(--tw-border-opacity));
  padding-left: 1.25rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-style: italic;
}

.gradient-1 {
  background: linear-gradient(96.2deg, #0575e6 0%, #021b79 100%);
}

.gradient-2 {
  background: linear-gradient(96.01deg, #00c9ff 0.29%, #fff94c 100%);
}

.gradient-3 {
  background: linear-gradient(96.01deg, #aaffa9 0.29%, #11ffbd 100%);
}

.custom-list{
  position: relative;
  margin-left: -0.25rem;
  margin-right: -0.25rem;
  padding-left: 1rem;
  list-style: none;
}
.custom-list li{
  position: relative;
}
.custom-list li::before{
  position: absolute;
}
[dir="ltr"] .custom-list li::before{
  left: 0px;
}
[dir="rtl"] .custom-list li::before{
  right: 0px;
}

ol.custom-list ol,
ul.custom-list ul{
  margin-top: 0.75rem;
}
[dir="ltr"] ol.custom-list ol li,[dir="ltr"]
ul.custom-list ul li{
  padding-left: 1.5rem;
}
[dir="rtl"] ol.custom-list ol li,[dir="rtl"]
ul.custom-list ul li{
  padding-right: 1.5rem;
}

.lits-by-numbaring {
  counter-reset: listitem;
}
.lits-by-numbaring li{
  position: relative;
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
[dir="ltr"] .lits-by-numbaring li{
  padding-left: 1.3em;
}
[dir="rtl"] .lits-by-numbaring li{
  padding-right: 1.3em;
}
.lits-by-numbaring li::before {
  counter-increment: listitem;
  content: counters(listitem, ".") ".";
}

.lits-by-slash li{
  position: relative;
  padding-left: 1rem;
}
.lits-by-slash li::before {
  left: 6px;
  content: "-";
}

.pagination{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.pagination > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .pagination > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 1;
}
.pagination li .prev-next-btn:disabled{
  cursor: not-allowed;
  opacity: 0.5;
}
.pagination li a,
.pagination li div,
.pagination li .page-link{
  display: flex;
  height: 1.5rem;
  width: 1.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
  line-height: 16px;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.dark .pagination li a,.dark
.pagination li div,.dark
.pagination li .page-link{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}
.pagination li a.active,
.pagination li div.active,
.pagination li .page-link.active{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dark .pagination li a.active,.dark
.pagination li div.active,.dark
.pagination li .page-link.active{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}
.pagination.bordered{
  border-radius: 3px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(216 222 230 / var(--tw-border-opacity));
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.pagination.bordered li{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}
.pagination.bordered li:first-child button, .pagination.bordered li:last-child button{
  display: flex;
  height: 1.5rem;
  width: 1.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.pagination.bordered li:first-child button:hover, .pagination.bordered li:last-child button:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.pagination.bordered li a,
.pagination.bordered li div,
.pagination.bordered li .page-link{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}
.pagination.bordered li a.active,
.pagination.bordered li div.active,
.pagination.bordered li .page-link.active{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.pagination.border-group > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0px * var(--tw-space-x-reverse));
  margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
}
.pagination.border-group{
  border-radius: 3px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(216 222 230 / var(--tw-border-opacity));
  padding-left: 0px;
  padding-right: 0px;
}
[dir="rtl"] .pagination.border-group > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 1;
}
.pagination.border-group li{
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  border-right-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(216 222 229 / var(--tw-border-opacity));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}
.pagination.border-group li:last-child{
  border-style: none;
}
.pagination.border-group li a,
.pagination.border-group li div,
.pagination.border-group li .page-link{
  height: auto;
  width: auto;
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}
.dark .pagination.border-group li a,.dark
.pagination.border-group li div,.dark
.pagination.border-group li .page-link{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.pagination.border-group li a.active,
.pagination.border-group li div.active,
.pagination.border-group li .page-link.active{
  font-size: 1.125rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.dark .pagination.border-group li a.active,.dark
.pagination.border-group li div.active,.dark
.pagination.border-group li .page-link.active{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.table-th{
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  /* padding-left: 1.5rem; */
  padding-right: 1.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

[dir="ltr"] .table-th{
  text-align: left;
}

[dir="rtl"] .table-th{
  text-align: right;
}

.dark .table-th{
  --tw-text-opacity: 1;
  color: #ffffff;
}

.table-td{
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity));
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
  text-transform: capitalize;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .table-td{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: #ffffff;
}

.table-checkbox{
  position: relative;
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
}

.table-checkbox::before{
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  margin: -0.7px;
  display: flex;
  height: 18px;
  width: 18px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.table-checkbox:checked::before{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  line-height: 10px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 17 18 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 2px;
  --tw-content: url("https://api.iconify.design/heroicons-outline/check.svg?color=white");
  content: var(--tw-content);
}

.dark .table-checkbox::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
}

.dark .table-checkbox:checked::before{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(51 65 85 / var(--tw-ring-opacity));
  content: var(--tw-content);
  --tw-ring-offset-width: 0px;
}

.table-checkbox[type=checkbox]:indeterminate::before{
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  line-height: 10px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 17 18 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 2px;
  --tw-content: url("https://api.iconify.design/heroicons/minus.svg?color=white");
  content: var(--tw-content);
}

.dark .table-checkbox[type=checkbox]:indeterminate::before{
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(51 65 85 / var(--tw-ring-opacity));
  content: var(--tw-content);
  --tw-ring-offset-width: 0px;
}

.dashcode-data-table label{
  display: inline-block;
  width: 100%;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

[dir="rtl"] .dashcode-data-table label{
  display: block;
  text-align: right;
}

.dark .dashcode-data-table label{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.dashcode-data-table select,
.dashcode-data-table input[type=text],
.dashcode-data-table input[type=search]{
  display: inline-block;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.dashcode-data-table select::placeholder,
.dashcode-data-table input[type=text]::placeholder,
.dashcode-data-table input[type=search]::placeholder{
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}
.dashcode-data-table select:focus,
.dashcode-data-table input[type=text]:focus,
.dashcode-data-table input[type=search]:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(71 85 105 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.9;
}
.dark .dashcode-data-table select,.dark
.dashcode-data-table input[type=text],.dark
.dashcode-data-table input[type=search]{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.dark .dashcode-data-table select::placeholder,.dark
.dashcode-data-table input[type=text]::placeholder,.dark
.dashcode-data-table input[type=search]::placeholder{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}
.dark .dashcode-data-table select:focus,.dark
.dashcode-data-table input[type=text]:focus,.dark
.dashcode-data-table input[type=search]:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(15 23 42 / var(--tw-ring-opacity));
}
.dashcode-data-table input[type=text],
.dashcode-data-table input[type=search]{
  margin-left: 0.5rem;
}
.dashcode-data-table .dataTables_empty{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 1.5rem;
}
.dashcode-data-table .dataTables_paginate{
  margin-top: 1.5rem;
  margin-right: 1rem;
}
.dashcode-data-table .dataTables_paginate > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.dashcode-data-table .dataTables_paginate{
  padding-bottom: 1.5rem;
}
.dashcode-data-table .dataTables_paginate .paginate_button{
  margin-right: 0.5rem;
  display: inline-flex;
  height: 1.5rem;
  width: 1.5rem;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
  line-height: 16px;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.dashcode-data-table .dataTables_paginate .paginate_button:last-child{
  margin-right: 0px;
}
.dark .dashcode-data-table .dataTables_paginate .paginate_button{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}
.dashcode-data-table .dataTables_paginate .paginate_button.current{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dark .dashcode-data-table .dataTables_paginate .paginate_button.current{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}
.dashcode-data-table .dataTables_paginate .next, .dashcode-data-table .dataTables_paginate .previous{
  position: relative;
  top: 2px;
}

.loginwrapper{
  display: flex;
  width: 100%;
  align-items: center;
  overflow: hidden;
  min-height: 100vh;
  min-height: calc(var(--vh, 1vh) * 100);
  height: 100vh;
  flex-basis: 100%;
}
.loginwrapper .lg-inner-column {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  overflow-y: auto;
}
.loginwrapper .left-column{
  display: none;
  flex: 1 1 0%;
  overflow: hidden;
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}
.dark .loginwrapper .left-column{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
@media (min-width: 1024px){
  .loginwrapper .left-column{
    display: block;
  }
}
.loginwrapper .left-column h4{
  font-size: 40px;
  line-height: 48px;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}
.dark .loginwrapper .left-column h4{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}
.loginwrapper .right-column{
  flex: 1 1 0%;
}
.loginwrapper .black-500-title{
  font-size: 40px;
  line-height: 48px;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.auth-box{
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  max-width: 524px;
  padding: 1.75rem;
}

@media (min-width: 768px){
  .auth-box{
    padding-left: 42px;
    padding-right: 42px;
    padding-top: 44px;
    padding-bottom: 44px;
  }
}
.auth-box h4{
  margin-bottom: 0.75rem;
  font-size: 1.5rem;
  line-height: 2rem;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.dark .auth-box h4{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.auth-box2{
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  max-width: 524px;
  padding: 1.75rem;
}

@media (min-width: 768px){
  .auth-box2{
    padding-left: 42px;
    padding-right: 42px;
    padding-top: 44px;
    padding-bottom: 44px;
  }
}
.auth-box2 h4{
  margin-bottom: 0.75rem;
  font-size: 1.5rem;
  line-height: 2rem;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.dark .auth-box2 h4{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.auth-box-3 h4{
  margin-bottom: 0.75rem;
  font-size: 1.5rem;
  line-height: 2rem;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark .auth-box-3 h4{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.auth-footer{
  z-index: 999;
  padding-bottom: 2.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(160 174 192 / var(--tw-text-opacity));
}

.dark .auth-footer{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.auth-box-3{
  position: relative;
  margin-right: auto;
  margin-left: auto;
  height: auto;
  width: 100%;
  max-width: 520px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 2.5rem;
}

.dark .auth-box-3{
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

@media (min-width: 768px){
  .auth-box-3{
    border-radius: 0.375rem;
  }
}

@media (min-width: 1024px){
  .auth-box-3{
    margin-right: 150px;
  }
}

.logo-box-3{
  display: flex;
  min-height: 100vh;
  align-items: center;
  justify-content: center;
}

.v3-right-column{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.auth-footer3{
  position: absolute;
  bottom: 0px;
  display: none;
}

@media (min-width: 1024px){
  .auth-footer3{
    display: block;
  }
}

.light .white_logo{
  display: none;
}

.dark .dark_logo{
  display: none;
}

.card{
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  border: 1px solid #016241;
}

.dark .card{
  --tw-bg-opacity: 1;
  background-color: #ebc263;
}

.card-title{
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 24px;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark .card-title{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

@media (min-width: 768px){
  .card-title{
    font-size: 1.25rem;
    line-height: 1.75rem;
    line-height: 28px;
  }
}

.card-subtitle{
  margin-top: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .card-subtitle{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.card-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.card-header:not(.noborder){
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
  padding-bottom: 1.25rem;
}

.dark .card-header:not(.noborder){
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
}

.card-footer{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 1.5rem;
  padding-bottom: 1.25rem;
}

.dark .card-footer{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
}

.card-height-auto .card{
  height: min-content;
}

@media print {
  .invocie-btn {
    display: none;
  }
  .sidebar-wrapper,
  .dashcode-app-header,
  .site-footer,
  .shadow-deep{
    display: none;
  }
  .content-wrapper{
    margin-left: 0px;
    width: 100%;
  }
}
.chat-height {
  height: calc(var(--vh, 1vh) * 100 - 12.1rem);
}

@media (max-width: 768px) {
  .chat-height {
    height: calc(var(--vh, 1vh) * 100 - 10.5rem);
  }
}
.contact-height {
  height: calc(100% - 138px);
}

.msg-height {
  height: calc(100% - 0px);
}

.parent-height {
  height: calc(100% - 200px);
}

.msg-action-btn{
  display: flex;
  height: 1.5rem;
  width: 1.5rem;
  cursor: pointer;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark .msg-action-btn{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

@media (min-width: 768px){
  .msg-action-btn{
    height: 2rem;
    width: 2rem;
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.info-500-list{
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}
.info-500-list li{
  display: flex;
}
.info-500-list li > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.info-500-list li span:nth-child(1){
  flex: none;
  text-align: right;
  font-weight: 500;
}
.info-500-list li span:nth-child(2){
  flex: 1 1 0%;
  text-align: right;
}

.nav-pills .active{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .nav-pills .active{
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.select2-container {
  width: 100% !important;
}

.dashcode-app .fc-toolbar-chunk button {
  height: 50px;
}
.dashcode-app .fc-toolbar-chunk button.fc-prev-button:after {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.dashcode-app .fc-toolbar-chunk button.fc-next-button:after {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.dashcode-app .fc-button {
  font-family: 'cairo';
  font-size: 16px !important;
  line-height: 16px !important;
  font-weight: bold;
  height: auto !important;
  text-transform: capitalize !important;
  padding: 12px 20px 12px 20px !important;
}
.dashcode-app .fc .fc-button-primary {
  background: transparent !important;
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.dark .dashcode-app .fc .fc-button-primary{
  --tw-text-opacity: 1;
  color: #016241;
}
.dashcode-app .fc .fc-button-primary:not(:disabled):active,
.dashcode-app .fc .fc-button-primary:not(:disabled).fc-button-active,
.dashcode-app .fc .fc-button-primary:hover {
  background: #111112 !important;
  color: #fff !important;
}
.dashcode-app .fc .fc-button-primary:disabled {
  background: #a0aec0 !important;
  border-color: #a0aec0 !important;
  cursor: not-allowed;
}
.dashcode-app .fc .fc-daygrid-day.fc-day-today {
  background: rgba(95, 99, 242, 0.04) !important;
}
.dashcode-app .fc .fc-button-primary:focus {
  box-shadow: none !important;
}
.dashcode-app .fc-theme-standard .fc-scrollgrid {
  border-color: #eef1f9 !important;
}
.dashcode-app .fc-theme-standard td,
.dashcode-app .fc-theme-standard th{
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity));
}
.dark .dashcode-app .fc-theme-standard td,.dark
.dashcode-app .fc-theme-standard th{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
}
.dashcode-app .fc-col-header-cell .fc-scrollgrid-sync-inner{
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}
.dark .dashcode-app .fc-col-header-cell .fc-scrollgrid-sync-inner{
  --tw-bg-opacity: 1;
  background-color: #016241;
  --tw-text-opacity: 1;
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
}
.dashcode-app .fc-daygrid-day-top{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.dark .dashcode-app .fc-daygrid-day-top{
  --tw-text-opacity: 1;
  color: #016241;
}
.dashcode-app .fc-h-event .fc-event-main-frame{
  margin-left: auto;
  margin-right: auto;
  width: max-content;
  justify-content: center;
  text-align: center;
}
.dashcode-app .fc-h-event .fc-event-main-frame .fc-event-time{
  flex: none;
  font-weight: 400;
}
.dashcode-app .fc-event-time{
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
}
.dashcode-app .fc-event-title {
  font-size: 14px !important;
  font-weight: 300 !important;
}
.dashcode-app .fc .fc-toolbar-title{
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}
.dark .dashcode-app .fc .fc-toolbar-title{
  --tw-text-opacity: 1;
  color: #016241;
}
.dashcode-app .fc-daygrid-event-dot{
  display: none;
}
@media (max-width: 981px) {
  .dashcode-app .fc-button-group,
  .dashcode-app .fc .fc-toolbar {
    display: block !important;
  }
  .dashcode-app .fc .fc-toolbar > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }
  .dashcode-app .fc-toolbar-chunk > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }
  .dashcode-app .fc .fc-button {
    padding: 0.4em 0.65em !important;
  }
}
.dark .dashcode-app .fc .fc-timegrid-axis-cushion,.dark
.dashcode-app .fc .fc-timegrid-slot-label-cushion{
  --tw-text-opacity: 1;
  color: #016241;
}
.dashcode-app .fc .fc-list-event:hover td{
  background-color: inherit;
}
.dashcode-app .fc .fc-list-event-dot{
  display: none;
}
.dashcode-app .fc-direction-ltr .fc-list-day-text,
.dashcode-app .fc-direction-rtl .fc-list-day-side-text,
.dashcode-app .fc-direction-ltr .fc-list-day-side-text,
.dashcode-app .fc-direction-rtl .fc-list-day-text {
  font-size: 16px;
  font-weight: 500;
}

.dark .fc-col-header-cell .fc-scrollgrid-sync-inner{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.dark .fc-daygrid-day-top{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.dark .fc .fc-day-other .fc-daygrid-day-top{
  opacity: 0.7;
}
.dark .fc .fc-button-primary{
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.dark .fc-theme-standard td,
.dark .fc-theme-standard th{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
}
.dark .fc .fc-toolbar-title{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.dark .fc .fc-button-primary:not(:disabled):active,
.dark .fc .fc-button-primary:not(:disabled).fc-button-active,
.dark .fc .fc-button-primary:hover {
  background: #016241 !important;
}
.dark .fc .fc-button-primary:disabled {
  background: #334155 !important;
  border-color: #334155 !important;
}
/* .dark .fc .fc-daygrid-day.fc-day-today {
  background: #334155 !important;
} */
.dark .fc-theme-standard .fc-scrollgrid {
  border-color: #334155 !important;
}

.dashcode-calender .primary{
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(70 105 250 / var(--tw-bg-opacity));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dashcode-calender .secondary{
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(160 174 192 / var(--tw-bg-opacity));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dashcode-calender .danger{
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(241 89 92 / var(--tw-bg-opacity));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dashcode-calender .info{
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(12 231 250 / var(--tw-bg-opacity));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dashcode-calender .warning{
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(250 145 107 / var(--tw-bg-opacity));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dashcode-calender .success{
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(80 199 147 / var(--tw-bg-opacity));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dashcode-calender .dark{
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(34 34 34 / var(--tw-bg-opacity));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.addmodal-wrapper{
  position: relative;
  z-index: -1;
}
.addmodal-wrapper .modal-overlay{
  visibility: hidden;
  position: fixed;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  background-color: rgb(15 23 42 / 0.5);
  opacity: 0;
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.addmodal-wrapper .modal-content{
  visibility: hidden;
  position: fixed;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  overflow-y: auto;
  opacity: 0;
}
.addmodal-wrapper.open-add-modal{
  z-index: 9999;
}
.addmodal-wrapper.open-add-modal .modal-overlay{
  visibility: visible;
  opacity: 1;
}
.addmodal-wrapper.open-add-modal .modal-content{
  visibility: visible;
  opacity: 1;
}

.calender-checkbox{
  position: relative;
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
}

.calender-checkbox::before{
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  margin: -0.7px;
  display: flex;
  height: 18px;
  width: 18px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.calender-checkbox:checked::before{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  line-height: 10px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 17 18 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 2px;
  --tw-content: url("https://api.iconify.design/heroicons-outline/check.svg?color=white");
  content: var(--tw-content);
}

.dark .calender-checkbox::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
}

.dark .calender-checkbox:checked::before{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(51 65 85 / var(--tw-ring-opacity));
  content: var(--tw-content);
  --tw-ring-offset-width: 0px;
}

#dashcode-mini-calendar .zabuto-calendar__navigation__item--header__title{
  margin-top: 1rem;
  margin-bottom: 1rem;
  display: block;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark #dashcode-mini-calendar .zabuto-calendar__navigation__item--header__title{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
#dashcode-mini-calendar .zabuto-calendar__navigation__item--prev{
  font-size: 1.25rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.dark #dashcode-mini-calendar .zabuto-calendar__navigation__item--prev{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
#dashcode-mini-calendar .zabuto-calendar__navigation__item--next{
  font-size: 1.25rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.dark #dashcode-mini-calendar .zabuto-calendar__navigation__item--next{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
#dashcode-mini-calendar .zabuto-calendar__days-of-week__item{
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 400;
  text-transform: capitalize;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}
.dark #dashcode-mini-calendar .zabuto-calendar__days-of-week__item{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
#dashcode-mini-calendar .zabuto-calendar__day{
  padding: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.dark #dashcode-mini-calendar .zabuto-calendar__day{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
#dashcode-mini-calendar table tbody td:nth-child(n+6){
  --tw-text-opacity: 1;
  color: rgb(241 89 92 / var(--tw-text-opacity));
}
.dark #dashcode-mini-calendar table tbody td:nth-child(n+6){
  --tw-text-opacity: 1;
  color: rgb(241 89 92 / var(--tw-text-opacity));
}

.dashcode-app .ql-editor {
  min-height: 120px;
}
.dashcode-app .ql-toolbar.ql-snow{
  margin-bottom: 0.5rem;
  border-style: none;
  padding: 0px;
}
.dashcode-app .ql-container.ql-snow{
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(251 251 251 / var(--tw-bg-opacity));
  font-size: 1rem;
  line-height: 1.5rem;
}
.dashcode-app .ql-editor{
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
  font-size: 1rem;
  line-height: 1.5rem;
}
.dark .dashcode-app .ql-editor{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
}

.dark .ql-editor{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.dark .ql-editor::placeholder{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.dark .ql-editor.ql-blank::before{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}
.dark .ql-snow .ql-stroke{
  stroke: #cbd5e1;
}
.dark .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
}
.dark .ql-snow.ql-toolbar button:hover,
.dark .ql-snow .ql-toolbar button:hover,
.dark .ql-snow.ql-toolbar button:focus,
.dark .ql-snow .ql-toolbar button:focus,
.dark .ql-snow.ql-toolbar .ql-picker-label:hover,
.dark .ql-snow .ql-toolbar .ql-picker-label:hover,
.dark .ql-snow.ql-toolbar .ql-picker-item:hover,
.dark .ql-snow .ql-toolbar .ql-picker-item:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
}
.dark .ql-picker-label{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.dark .ql-snow .ql-picker.ql-expanded .ql-picker-label{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity));
}

.wizard-step .number-box{
  position: relative;
  z-index: 66;
  display: flex;
  height: 1.75rem;
  width: 1.75rem;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

@media (min-width: 768px){
  .wizard-step .number-box{
    height: 3rem;
    width: 3rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}
.wizard-step .bar-line{
  position: absolute;
  top: 50%;
  height: 2px;
  width: 100%;
}
.wizard-step .bar-line2{
  position: absolute;
  top: 0px;
  left: 50%;
  height: 100%;
  width: 2px;
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.wizard-step .circle-box{
  position: absolute;
  top: 100%;
  margin-top: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  opacity: 0;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.group:hover .wizard-step .circle-box{
  opacity: 1;
}
@media (min-width: 768px){
  .wizard-step .circle-box{
    line-height: 1.5rem;
    opacity: 1;
  }
}
.wizard-step:not(.active) .number-box{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  color: rgb(15 23 42 / var(--tw-text-opacity));
  --tw-text-opacity: 0.7;
  --tw-ring-color: rgb(15 23 42 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.7;
}
.dark .wizard-step:not(.active) .number-box{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(71 85 105 / var(--tw-ring-opacity));
}
.wizard-step:not(.active) .bar-line{
  --tw-bg-opacity: 1;
  background-color: rgb(224 234 255 / var(--tw-bg-opacity));
}
.dark .wizard-step:not(.active) .bar-line{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
}
.wizard-step:not(.active) .circle-box{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}
.dark .wizard-step:not(.active) .circle-box{
  color: rgb(203 213 225 / var(--tw-text-opacity));
  --tw-text-opacity: 0.4;
}
.wizard-step:not(.passed) .number-box .number{
  display: block;
}
.wizard-step:not(.passed) .number-box .no-icon{
  display: none;
}
.wizard-step:not(.passed) .bar-line2{
  --tw-bg-opacity: 1;
  background-color: rgb(224 234 255 / var(--tw-bg-opacity));
}
.dark .wizard-step:not(.passed) .bar-line2{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}
.wizard-step.passed .number-box .number{
  display: none;
}
.wizard-step.passed .number-box .no-icon{
  display: block;
}
.wizard-step.active .number-box, .wizard-step.passed .number-box{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(15 23 42 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 2px;
}
.dark .wizard-step.active .number-box,.dark  .wizard-step.passed .number-box{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(15 23 42 / var(--tw-ring-opacity));
  --tw-ring-offset-color: #64748b;
}
.wizard-step.active .bar-line, .wizard-step.passed .bar-line{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
.dark .wizard-step.active .bar-line,.dark  .wizard-step.passed .bar-line{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
.wizard-step.active .circle-box, .wizard-step.passed .circle-box{
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.dark .wizard-step.active .circle-box,.dark  .wizard-step.passed .circle-box{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.wizard-step.active .bar-line2, .wizard-step.passed .bar-line2{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
.dark .wizard-step.active .bar-line2,.dark  .wizard-step.passed .bar-line2{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}

.wizard-form-step{
  display: none;
}

.wizard-form-step.active{
  display: block;
}

.chat-contact-bar{
  width: 200px;
  flex: none;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

@media (min-width: 1024px){
  .chat-contact-bar{
    width: 260px;
  }
}
.chat-contact-bar.enter-lg{
  position: absolute;
  top: 0px;
  left: -100%;
  z-index: 999;
  height: 100%;
  width: 260px;
}
.chat-contact-bar.enter-lg.active{
  left: 0px;
}

.chat-overlay{
  visibility: hidden;
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  z-index: -99;
  width: 100%;
  flex: 1 1 0%;
  border-radius: 0.375rem;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.6;
  opacity: 0;
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.dark .chat-overlay{
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.6;
}
.chat-overlay.active{
  visibility: visible;
  z-index: 99;
  opacity: 1;
}

.app_height {
  height: calc(var(--vh, 1vh) * 100 - 12.1rem);
}

.email-categorie-list label{
  display: flex;
  cursor: pointer;
  align-items: center;
  border-radius: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .email-categorie-list label{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.email-categorie-list .bar-c{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.email-categorie-list.active label{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.dark .email-categorie-list.active label{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}
.email-categorie-list.active .bar-c{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.email-list li:not(.opened) .read-unread-name{
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark .email-list li:not(.opened) .read-unread-name{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.dark .email-list li.opened{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.email-icon{
  display: flex;
  height: 2rem;
  width: 2rem;
  cursor: pointer;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .email-icon{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}

[data-stared=true] .email-fav{
  --tw-text-opacity: 1;
  color: rgb(255 206 48 / var(--tw-text-opacity));
}

[data-stared=false] .email-fav{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.email-fav{
  cursor: pointer;
}

.email-sidebar{
  width: 200px;
  flex: none;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

@media (min-width: 1024px){
  .email-sidebar{
    width: 260px;
  }
}
.email-sidebar.enter-lg{
  position: absolute;
  top: 0px;
  left: -100%;
  z-index: 999;
  height: 100%;
  width: 200px;
}
@media (min-width: 768px){
  .email-sidebar.enter-lg{
    width: 260px;
  }
}
.email-sidebar.enter-lg.active{
  left: 0px;
}

.email-overlay{
  visibility: hidden;
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  z-index: -99;
  width: 100%;
  flex: 1 1 0%;
  border-radius: 0.375rem;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.6;
  opacity: 0;
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.dark .email-overlay{
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.6;
}
.email-overlay.active{
  visibility: visible;
  z-index: 99;
  opacity: 1;
}

.todo-categorie-list label{
  display: flex;
  cursor: pointer;
  align-items: center;
  border-radius: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .todo-categorie-list label{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.todo-categorie-list .bar-c{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.todo-categorie-list.active label{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.dark .todo-categorie-list.active label{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}
.todo-categorie-list.active .bar-c{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.todo-list li:not(.opened) .read-unread-name{
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark .todo-list li:not(.opened) .read-unread-name{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.dark .todo-list li.opened{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.todo-icon{
  display: flex;
  height: 2rem;
  width: 2rem;
  cursor: pointer;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .todo-icon{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}

[data-stared=true] .todo-fav{
  --tw-text-opacity: 1;
  color: rgb(255 206 48 / var(--tw-text-opacity));
}

[data-stared=false] .todo-fav{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.todo-fav{
  cursor: pointer;
}

.todo-sidebar{
  width: 200px;
  flex: none;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

@media (min-width: 1024px){
  .todo-sidebar{
    width: 260px;
  }
}
.todo-sidebar.enter-lg{
  position: absolute;
  top: 0px;
  left: -100%;
  z-index: 999;
  height: 100%;
  width: 200px;
}
@media (min-width: 768px){
  .todo-sidebar.enter-lg{
    width: 260px;
  }
}
.todo-sidebar.enter-lg.active{
  left: 0px;
}

.todo-overlay{
  visibility: hidden;
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  z-index: -99;
  width: 100%;
  flex: 1 1 0%;
  border-radius: 0.375rem;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.6;
  opacity: 0;
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.dark .todo-overlay{
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.6;
}
.todo-overlay.active{
  visibility: visible;
  z-index: 99;
  opacity: 1;
}

.app_height {
  height: calc(var(--vh, 1vh) * 100 - 12.1rem);
}

.email-categorie-list label{
  display: flex;
  cursor: pointer;
  align-items: center;
  border-radius: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .email-categorie-list label{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.email-categorie-list .bar-c{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.email-categorie-list.active label{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.dark .email-categorie-list.active label{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}
.email-categorie-list.active .bar-c{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.email-list li:not(.opened) .read-unread-name{
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark .email-list li:not(.opened) .read-unread-name{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.dark .email-list li.opened{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.email-icon{
  display: flex;
  height: 2rem;
  width: 2rem;
  cursor: pointer;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.dark .email-icon{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}

[data-stared=true] .email-fav{
  --tw-text-opacity: 1;
  color: rgb(255 206 48 / var(--tw-text-opacity));
}

[data-stared=false] .email-fav{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.email-fav{
  cursor: pointer;
}

.email-sidebar{
  width: 200px;
  flex: none;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

@media (min-width: 1024px){
  .email-sidebar{
    width: 260px;
  }
}
.email-sidebar.enter-lg{
  position: absolute;
  top: 0px;
  left: -100%;
  z-index: 999;
  height: 100%;
  width: 200px;
}
@media (min-width: 768px){
  .email-sidebar.enter-lg{
    width: 260px;
  }
}
.email-sidebar.enter-lg.active{
  left: 0px;
}

.email-overlay{
  visibility: hidden;
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  z-index: -99;
  width: 100%;
  flex: 1 1 0%;
  border-radius: 0.375rem;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.6;
  opacity: 0;
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.dark .email-overlay{
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.6;
}
.email-overlay.active{
  visibility: visible;
  z-index: 99;
  opacity: 1;
}

/* Header Changing area */
.app-header{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 15px;
  padding-right: 15px;
}
@media (min-width: 768px){
  .app-header{
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

.nav-floating #app_header .app-header{
  border-radius: 0.5rem;
}

.nav-sticky #app_header{
  position: sticky;
  top: 0px;
}

.nav-hidden #app_header{
  display: none;
}

.nav-floating #app_header{
  position: sticky;
  top: 1rem;
  margin-left: 1rem;
  margin-right: 1rem;
  margin-top: 1rem;
}

.nav-floating #app_header::after{
  position: absolute;
  z-index: -10;
  --tw-backdrop-blur: blur(12px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  --tw-content: "";
  content: var(--tw-content);
  background: linear-gradient(180deg, rgba(var(--v-theme-background), 70%) 44%, rgba(var(--v-theme-background), 43%) 73%, rgba(var(--v-theme-background), 0%));
  background-repeat: repeat;
  block-size: 5.5rem;
  inset-block-start: -1rem;
  inset-inline-end: 0;
  inset-inline-start: 0;
  -webkit-mask: linear-gradient(black, black 18%, transparent 100%);
  mask: linear-gradient(black, black 18%, transparent 100%);
}

.vertical-box{
  display: flex;
}

.horizental-box{
  display: none;
}

.main-menu{
  display: none;
}

.horizontalMenu .vertical-box{
  display: none;
}
.horizontalMenu .horizental-box{
  display: flex;
}
.horizontalMenu .main-menu{
  display: none;
}
@media (min-width: 1280px){
  .horizontalMenu .main-menu{
    display: block;
  }
}
.horizontalMenu .app-header{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
@media (min-width: 1280px){
  .horizontalMenu .app-header{
    padding-top: 0px;
    padding-bottom: 0px;
  }
}

.modal{
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.main-menu > ul > li{
  position: relative;
  display: inline-block;
}
.main-menu > ul > li > a{
  position: relative;
  display: flex;
  align-items: flex-start;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.dark .main-menu > ul > li > a{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
@media (min-width: 1280px){
  .main-menu > ul > li > a{
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }
}
@media (min-width: 1536px){
  .main-menu > ul > li > a{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
.main-menu > ul > li > a .icon-box{
  font-size: 1.125rem;
  line-height: 1.75rem;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.dark .main-menu > ul > li > a .icon-box{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.main-menu > ul > li:hover > a{
  --tw-text-opacity: 1;
  color: rgb(70 105 250 / var(--tw-text-opacity));
}
.main-menu > ul > li:hover > a .icon-box{
  --tw-text-opacity: 1;
  color: rgb(70 105 250 / var(--tw-text-opacity));
}
.main-menu > ul > li.has-megamenu{
  position: static;
}

.main-menu > ul > li.menu-item-has-children > ul.sub-menu,
.main-menu > ul > li.menu-item-has-children > .rt-mega-menu{
  visibility: hidden;
  position: absolute;
  left: 0px;
  top: 110%;
  z-index: 999;
  width: max-content;
  min-width: 178px;
  border-radius: 4px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  opacity: 0;
  --tw-shadow: 0px 2px 4px rgba(40, 41, 61, 0.04), 0px 8px 16px rgba(96, 97, 112, 0.16);
  --tw-shadow-colored: 0px 2px 4px var(--tw-shadow-color), 0px 8px 16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.dark .main-menu > ul > li.menu-item-has-children > ul.sub-menu,.dark
.main-menu > ul > li.menu-item-has-children > .rt-mega-menu{
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.main-menu > ul > li.menu-item-has-children > .rt-mega-menu{
  left: 50%;
  max-width: 1170px;
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  width: 100%;
}

.main-menu > ul > li.menu-item-has-children:hover > ul.sub-menu,
.main-menu > ul > li.menu-item-has-children:hover > .rt-mega-menu{
  visibility: visible;
  top: 100%;
  opacity: 1;
}

.main-menu > ul > li.menu-item-has-children > ul.sub-menu li{
  position: relative;
  padding-bottom: 0.5rem;
}

.main-menu > ul > li.menu-item-has-children > ul.sub-menu li:last-child{
  padding-bottom: 0px;
}

.main-menu > ul > li.menu-item-has-children > ul.sub-menu li a{
  display: block;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
  text-transform: capitalize;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.main-menu > ul > li.menu-item-has-children > ul.sub-menu li a:last-child{
  padding-bottom: 0px;
}

.main-menu > ul > li.menu-item-has-children > ul.sub-menu li a:hover{
  --tw-text-opacity: 1;
  color: rgb(70 105 250 / var(--tw-text-opacity));
}

.dark .main-menu > ul > li.menu-item-has-children > ul.sub-menu li a{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.dark .main-menu > ul > li.menu-item-has-children > ul.sub-menu li a:hover{
  --tw-text-opacity: 1;
  color: rgb(70 105 250 / var(--tw-text-opacity));
}

.rt-mega-menu a{
  display: block;
  padding-top: 6px;
  padding-bottom: 6px;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.dark .rt-mega-menu a{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.dark .rt-mega-menu a:hover{
  --tw-text-opacity: 1;
  color: rgb(70 105 250 / var(--tw-text-opacity));
}

/* Sidebar Wrapper Area */
.sidebar-wrapper{
  position: fixed;
  top: 0px;
  z-index: 999;
  height: 100vh;
  width: 248px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0px 0px 1px rgba(40, 41, 61, 0.08), 0px 0.5px 2px rgba(96, 97, 112, 0.16);
  --tw-shadow-colored: 0px 0px 1px var(--tw-shadow-color), 0px 0.5px 2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark .sidebar-wrapper{
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.app-wrapper .sidebar-open{
  display: block;
}

.app-wrapper .menu-hide{
  display: none;
}

.logo-segment{
  position: sticky;
  top: 0px;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow-x: hidden;
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

.dark .logo-segment{
  --tw-bg-opacity: 1;
  background-color: #be9539;
}

.sidebar-menu{
  background-color: transparent;
}

.sidebar-menu > li{
  font-family: Inter, sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity));
}

.dark .sidebar-menu > li{
  --tw-text-opacity: 1;
  color: #ffffff;
}

.sidebar-menu .sidebar-menu-title{
  margin-top: 1rem;
  margin-bottom: 1rem;
  font-family: Inter, sans-serif;
  font-weight: 600;
}

.dark .sidebar-menu .sidebar-menu-title{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.semiDark .sidebar-menu .sidebar-menu-title{
  margin-top: 1rem;
  margin-bottom: 1rem;
  font-family: Inter, sans-serif;
  font-weight: 600;
}

.dark .semiDark .sidebar-menu .sidebar-menu-title{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.semiDark .sidebar-menu .sidebar-menu-title{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.semiDark .navItem.active{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
}

.semiDark .icon-arrow{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.semiDark .navItem{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.navItem{
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.dark .navItem{
  --tw-text-opacity: 1;
  color: #ffffff;
}

.navItem {
  display: flex !important;
}

.navItem .nav-icon{
  font-size: 18px;
}

[dir="ltr"] .navItem .nav-icon{
  margin-right: 0.75rem;
}

[dir="rtl"] .navItem .nav-icon{
  margin-left: 0.75rem;
}

.navItem.active{
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .navItem.active{
  --tw-bg-opacity: 1;
  background-color: #016241;
}

.icon-arrow{
  display: flex;
  height: 1.25rem;
  width: 1.25rem;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  background-color: rgb(160 174 192 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.3;
  font-size: 14px;
  color: rgb(71 85 105 / var(--tw-text-opacity));
  --tw-text-opacity: 0.7;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

[dir="rtl"] .icon-arrow{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark .icon-arrow{
  --tw-bg-opacity: 1;
  background-color: #016241;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.sidebar-menu > li > a{
  border-radius: 0.375rem;
  padding-left: 8px;
  padding-right: 10px;
}

.sidebar-menu > li.active > a{
  display: flex;
  cursor: pointer;
  border-radius: 4px;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.5;
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  text-transform: capitalize;
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity));
}

.dark .sidebar-menu > li.active > a{
  background-color: #016241;
  --tw-bg-opacity: 0.2;
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}

.sidebar-menu .sidebar-submenu{
  margin-top: 9px;
  background-color: transparent;
}

.sidebar-menu .sidebar-submenu > li > a{
  position: relative;
  white-space: nowrap;
  background-color: transparent;
  padding-top: 7px;
  padding-bottom: 7px;
  /* font-size: 0.875rem; */
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
}

.sidebar-menu .sidebar-submenu > li > a::before{
  position: absolute;
  top: 14px;
  height: 8px;
  width: 8px;
  border-radius: 9999px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 17 18 / var(--tw-ring-opacity));
  content: var(--tw-content);
  --tw-ring-opacity: 15%;
}

.sidebar-menu .sidebar-submenu > li > a:hover{
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

[dir="ltr"] .sidebar-menu .sidebar-submenu > li > a{
  padding-left: 2rem;
}

[dir="ltr"] .sidebar-menu .sidebar-submenu > li > a::before{
  content: var(--tw-content);
  left: 0.75rem;
}

[dir="rtl"] .sidebar-menu .sidebar-submenu > li > a{
  padding-right: 2rem;
}

[dir="rtl"] .sidebar-menu .sidebar-submenu > li > a::before{
  content: var(--tw-content);
  right: 0.75rem;
}

.dark .sidebar-menu .sidebar-submenu > li > a{
  --tw-text-opacity: 1;
  color: #ffffff;
}

.dark .sidebar-menu .sidebar-submenu > li > a::before{
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity));
  --tw-ring-color: rgb(203 213 225 / var(--tw-ring-opacity));
  content: var(--tw-content);
  --tw-ring-opacity: 0.2;
}

.dark .sidebar-menu .sidebar-submenu > li > a:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.sidebar-menu .sidebar-submenu > li > a.active{
  --tw-text-opacity: 1;
  color: rgb(17 17 18 / var(--tw-text-opacity));
}

.sidebar-menu .sidebar-submenu > li > a.active::before{
  --tw-bg-opacity: 1;
  background-color: rgb(17 17 18 / var(--tw-bg-opacity));
  content: var(--tw-content);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.dark .sidebar-menu .sidebar-submenu > li > a.active{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .sidebar-menu .sidebar-submenu > li > a.active::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity));
}

.sidebar-menu > li.active .icon-arrow{
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-color: rgb(160 174 192 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.3;
  color: rgb(71 85 105 / var(--tw-text-opacity));
  --tw-text-opacity: 0.7;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.dark .sidebar-menu > li.active .icon-arrow{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.sidebar-menu > li.active-withOutChild > a{
  --tw-bg-opacity: 1;
  background-color: rgb(17 17 18 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

/* For Sidebar Type  */
.app-wrapper.collapsed .collapsed-icon{
  display: none;
}

.app-wrapper.extend .extend-icon{
  display: block;
}

.app-wrapper.collapsed .extend-icon{
  display: none;
}

.app-wrapper.extend .collapsed-icon{
  display: none;
}

.sidebarDotIcon.collapsed-icon{
  display: none;
}

.app-wrapper.collapsed .sidebarOpenButton {
  display: inline-flex !important;
}

.app-wrapper.extend .sidebarOpenButton {
  display: none !important;
}

/* Collapsed button */
.app-header,
.content-wrapper,
.site-footer{
  margin-left: 0px;
}
[dir="rtl"] .app-header,[dir="rtl"]
.content-wrapper,[dir="rtl"]
.site-footer{
  margin-right: 0px;
}
@media (min-width: 1280px){
  [dir="ltr"] .app-header,[dir="ltr"]
.content-wrapper,[dir="ltr"]
.site-footer{
    margin-left: 248px;
  }
  [dir="rtl"] .app-header,[dir="rtl"]
.content-wrapper,[dir="rtl"]
.site-footer{
    margin-right: 248px;
  }
}

[dir="ltr"] .collapsed .app-header,[dir="ltr"]
.collapsed .content-wrapper,[dir="ltr"]
.collapsed .site-footer{
  margin-left: 72px;
}

[dir="rtl"] .collapsed .app-header,[dir="rtl"]
.collapsed .content-wrapper,[dir="rtl"]
.collapsed .site-footer{
  margin-right: 72px;
}

.collapsed .sidebar-wrapper{
  width: 72px;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.collapsed .sidebar-wrapper:hover{
  width: 248px;
}

.collapsed .sidebar-wrapper .sidebar-menus .sidebar-menu-title{
  display: none;
}

.collapsed .sidebar-wrapper .navItem span span{
  visibility: hidden;
}

.collapsed .sidebar-wrapper .sidebar-menu li.active > .sidebar-submenu {
  display: none !important;
}

/* Collapsed Hover */
.collapsed .sidebar-wrapper:hover .sidebar-menu li.active > .sidebar-submenu {
  display: block !important;
}

.app-wrapper.collapsed:hover .collapsed-icon{
  display: block;
}

.collapsed .sidebar-wrapper .logo-segment a span,
.collapsed .sidebar-wrapper .logo-segment #sidebar_type {
  display: none !important;
}

.collapsed .sidebar-wrapper:hover .logo-segment a span,
.collapsed .sidebar-wrapper:hover .logo-segment #sidebar_type {
  display: block !important;
}

.collapsed .sidebar-wrapper:hover .navItem span span{
  visibility: visible;
}

.app-wrapper.collapsed #sidebar_bottom_wizard{
  display: none;
}

.semiDark .sidebar-wrapper{
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.semiDark .logo-segment{
  background-color: transparent;
}
.semiDark .logo-segment .sidebarDotIcon,
.semiDark .logo-segment span{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.semiDark #sidebar_menus{
  background-color: transparent;
}
.semiDark .sidebar-menu > li.active > a{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}
.semiDark .sidebar-menu > li.active .icon-arrow{
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}
.semiDark .sidebar-menu .sidebar-submenu > li > a{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.semiDark .sidebar-menu .sidebar-submenu > li > a::before{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity));
}
.semiDark .sidebar-menu .sidebar-submenu > li > a.active{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.semiDark .sidebar-menu .sidebar-submenu > li > a.active::before{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-ring-color: rgb(248 250 252 / var(--tw-ring-opacity));
  content: var(--tw-content);
  --tw-ring-opacity: 0.1;
}

.margin-0 {
  margin-left: 0px !important;
  margin-right: 0 !important;
}

.social-link{
  display: flex;
  height: 2rem;
  width: 2rem;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(17 17 18 / var(--tw-border-opacity));
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.social-link:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .social-link{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}

.dark .social-link:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
}

.leading-0 {
  line-height: 0;
}

.icon-lists li {
  margin-right: 12px;
  margin-bottom: 12px;
}

.completed .img-active{
  opacity: 0.2;
}
.completed .bar-active{
  text-decoration-line: line-through;
}
.dark .completed .bar-active{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.legend-ring .apexcharts-legend-series:nth-child(1) .apexcharts-legend-marker{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(70 105 250 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.3;
}

[dir="rtl"] .legend-ring .apexcharts-legend-series:nth-child(1) .apexcharts-legend-marker{
  margin-left: 1rem;
}
.legend-ring .apexcharts-legend-series:nth-child(2) .apexcharts-legend-marker{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(12 231 250 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.3;
}
[dir="rtl"] .legend-ring .apexcharts-legend-series:nth-child(2) .apexcharts-legend-marker{
  margin-left: 1rem;
}
.legend-ring .apexcharts-legend-series:nth-child(3) .apexcharts-legend-marker{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(250 145 107 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.3;
}
[dir="rtl"] .legend-ring .apexcharts-legend-series:nth-child(3) .apexcharts-legend-marker{
  margin-left: 1rem;
}

.legend-ring2 .apexcharts-legend-series:nth-child(1) .apexcharts-legend-marker{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(12 231 250 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.3;
}

[dir="rtl"] .legend-ring2 .apexcharts-legend-series:nth-child(1) .apexcharts-legend-marker{
  margin-left: 1rem;
}
.legend-ring2 .apexcharts-legend-series:nth-child(2) .apexcharts-legend-marker{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(250 145 107 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.3;
}
[dir="rtl"] .legend-ring2 .apexcharts-legend-series:nth-child(2) .apexcharts-legend-marker{
  margin-left: 1rem;
}

.legend-ring3 .apexcharts-legend-series:nth-child(1) .apexcharts-legend-marker{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(80 199 147 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.3;
}

[dir="rtl"] .legend-ring3 .apexcharts-legend-series:nth-child(1) .apexcharts-legend-marker{
  margin-left: 1rem;
}
.legend-ring3 .apexcharts-legend-series:nth-child(2) .apexcharts-legend-marker{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(250 145 107 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.3;
}
[dir="rtl"] .legend-ring3 .apexcharts-legend-series:nth-child(2) .apexcharts-legend-marker{
  margin-left: 1rem;
}
.legend-ring3 .apexcharts-legend-series:nth-child(3) .apexcharts-legend-marker{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(160 174 192 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.3;
}
[dir="rtl"] .legend-ring3 .apexcharts-legend-series:nth-child(3) .apexcharts-legend-marker{
  margin-left: 1rem;
}

.legend-ring4 .apexcharts-legend-series:nth-child(1) .apexcharts-legend-marker{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(70 105 250 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.3;
}

[dir="rtl"] .legend-ring4 .apexcharts-legend-series:nth-child(1) .apexcharts-legend-marker{
  margin-left: 1rem;
}
.legend-ring4 .apexcharts-legend-series:nth-child(2) .apexcharts-legend-marker{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(250 145 107 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.3;
}
[dir="rtl"] .legend-ring4 .apexcharts-legend-series:nth-child(2) .apexcharts-legend-marker{
  margin-left: 1rem;
}

.dashcode-app .leaflet-control {
  z-index: 0 !important;
}
.dashcode-app .leaflet-control-container {
  z-index: 555 !important;
  position: relative;
}
.dashcode-app .leaflet-container.leaflet-touch-drag.leaflet-touch-zoom {
  z-index: 0 !important;
}
.placeholder\:font-medium::placeholder{
  font-weight: 500;
}
.placeholder\:font-normal::placeholder{
  font-weight: 400;
}
.placeholder\:text-slate-400::placeholder{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}
.placeholder\:text-secondary-500::placeholder{
  --tw-text-opacity: 1;
  color: rgb(160 174 192 / var(--tw-text-opacity));
}
.before\:absolute::before{
  content: var(--tw-content);
  position: absolute;
}
.before\:top-\[0px\]::before{
  content: var(--tw-content);
  top: 0px;
}
.before\:left-0::before{
  content: var(--tw-content);
  left: 0px;
}
.before\:top-\[60px\]::before{
  content: var(--tw-content);
  top: 60px;
}
.before\:top-0::before{
  content: var(--tw-content);
  top: 0px;
}
.before\:z-\[-1\]::before{
  content: var(--tw-content);
  z-index: -1;
}
.before\:h-4::before{
  content: var(--tw-content);
  height: 1rem;
}
.before\:h-\[calc\(100\%-60px\)\]::before{
  content: var(--tw-content);
  height: calc(100% - 60px);
}
.before\:h-full::before{
  content: var(--tw-content);
  height: 100%;
}
.before\:w-4::before{
  content: var(--tw-content);
  width: 1rem;
}
.before\:w-full::before{
  content: var(--tw-content);
  width: 100%;
}
.before\:rounded-full::before{
  content: var(--tw-content);
  border-radius: 9999px;
}
.before\:rounded::before{
  content: var(--tw-content);
  border-radius: 0.25rem;
}
.before\:bg-slate-900::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
.before\:bg-info-500::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(12 231 250 / var(--tw-bg-opacity));
}
.before\:bg-warning-500::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(250 145 107 / var(--tw-bg-opacity));
}
.before\:bg-success-500::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(80 199 147 / var(--tw-bg-opacity));
}
.before\:bg-opacity-\[0\.1\]::before{
  content: var(--tw-content);
  --tw-bg-opacity: 0.1;
}
.before\:leading-\[2px\]::before{
  content: var(--tw-content);
  line-height: 2px;
}
.before\:content-\[url\(\'\.\.\/images\/all-img\/ck\.svg\'\)\]::before{
  --tw-content: url('../images/all-img/ck.svg');
  content: var(--tw-content);
}
.after\:absolute::after{
  content: var(--tw-content);
  position: absolute;
}
.after\:top-\[2px\]::after{
  content: var(--tw-content);
  top: 2px;
}
.after\:left-\[2px\]::after{
  content: var(--tw-content);
  left: 2px;
}
.after\:z-10::after{
  content: var(--tw-content);
  z-index: 10;
}
.after\:h-5::after{
  content: var(--tw-content);
  height: 1.25rem;
}
.after\:w-5::after{
  content: var(--tw-content);
  width: 1.25rem;
}
.after\:rounded-full::after{
  content: var(--tw-content);
  border-radius: 9999px;
}
.after\:border::after{
  content: var(--tw-content);
  border-width: 1px;
}
.after\:border-gray-300::after{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(210 214 220 / var(--tw-border-opacity));
}
.after\:bg-white::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.after\:transition-all::after{
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.after\:content-\[\'\'\]::after{
  --tw-content: '';
  content: var(--tw-content);
}
.first\:rounded-t:first-child{
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.first\:pt-0:first-child{
  padding-top: 0px;
}
.first\:text-xs:first-child{
  font-size: 0.75rem;
  line-height: 1rem;
}
.first\:uppercase:first-child{
  text-transform: uppercase;
}
.first\:text-slate-600:first-child{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}
.first\:shadow-md:first-child{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.last\:mb-0:last-child{
  margin-bottom: 0px;
}
.last\:flex-none:last-child{
  flex: none;
}
.last\:rounded-b:last-child{
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.last\:border-b-0:last-child{
  border-bottom-width: 0px;
}
.last\:border-none:last-child{
  border-style: none;
}
.last\:pb-0:last-child{
  padding-bottom: 0px;
}
.last\:shadow-md:last-child{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.odd\:shadow-md:nth-child(odd){
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.even\:bg-slate-50:nth-child(even){
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity));
}
.even\:shadow-md:nth-child(even){
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.visited\:shadow-md:visited{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.checked\:bg-black-500:checked{
  --tw-bg-opacity: 1;
  background-color: rgb(17 17 18 / var(--tw-bg-opacity));
}
.checked\:shadow-md:checked{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.checked\:ring-black-500:checked{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 17 18 / var(--tw-ring-opacity));
}
.checked\:ring-offset-2:checked{
  --tw-ring-offset-width: 2px;
}
.focus-within\:shadow-md:focus-within{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:container:hover{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 15px;
  padding-left: 15px;
}
@media (min-width: 640px){
  .hover\:container:hover{
    max-width: 640px;
    padding-right: 15px;
    padding-left: 15px;
  }
}
@media (min-width: 768px){
  .hover\:container:hover{
    max-width: 768px;
  }
}
@media (min-width: 1024px){
  .hover\:container:hover{
    max-width: 1024px;
    padding-right: 15px;
    padding-left: 15px;
  }
}
@media (min-width: 1280px){
  .hover\:container:hover{
    max-width: 1280px;
    padding-right: 15px;
    padding-left: 15px;
  }
}
.hover\:-translate-y-1:hover{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-75:hover{
  --tw-scale-x: .75;
  --tw-scale-y: .75;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:border-transparent:hover{
  border-color: transparent;
}
.hover\:border-white:hover{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.hover\:bg-blue-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}
.hover\:bg-slate-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}
.hover\:bg-slate-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity));
}
.hover\:bg-slate-900:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
.hover\:bg-black-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(17 17 18 / var(--tw-bg-opacity));
}
.hover\:bg-slate-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity));
}
.hover\:bg-slate-300:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity));
}
.hover\:bg-opacity-80:hover{
  --tw-bg-opacity: 0.8;
}
.hover\:bg-opacity-100:hover{
  --tw-bg-opacity: 1;
}
.hover\:\!text-center:hover{
  text-align: center !important;
}
.hover\:font-bold:hover{
  font-weight: 700;
}
.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.hover\:text-slate-900:hover{
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.hover\:text-danger-500:hover{
  --tw-text-opacity: 1;
  color: rgb(241 89 92 / var(--tw-text-opacity));
}
.hover\:text-slate-800:hover{
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity));
}
.hover\:text-\[\#68768A\]:hover{
  --tw-text-opacity: 1;
  color: rgb(104 118 138 / var(--tw-text-opacity));
}
.hover\:underline:hover{
  text-decoration-line: underline;
}
.hover\:no-underline:hover{
  text-decoration-line: none;
}
.hover\:opacity-75:hover{
  opacity: 0.75;
}
.hover\:shadow-lg:hover{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-md:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-sm:hover{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-todo:hover{
  --tw-shadow: rgba(235 233 241, 0.6) 0px 3px 10px 0px;
  --tw-shadow-colored: 0px 3px 10px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:border-none:focus{
  border-style: none;
}
.focus\:border-transparent:focus{
  border-color: transparent;
}
.focus\:border-indigo-300:focus{
  --tw-border-opacity: 1;
  border-color: rgb(165 180 252 / var(--tw-border-opacity));
}
.focus\:border-gray-300:focus{
  --tw-border-opacity: 1;
  border-color: rgb(210 214 220 / var(--tw-border-opacity));
}
.focus\:border-gray-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(104 118 138 / var(--tw-border-opacity));
}
.focus\:\!border-slate-300:focus{
  --tw-border-opacity: 1 !important;
  border-color: rgb(203 213 225 / var(--tw-border-opacity)) !important;
}
.focus\:bg-white:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.focus\:bg-gray-200:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.focus\:bg-blue-700:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}
.focus\:font-normal:focus{
  font-weight: 400;
}
.focus\:opacity-100:focus{
  opacity: 1;
}
.focus\:shadow-lg:focus{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:shadow-md:focus{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:\!shadow-none:focus{
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.focus\:shadow-none:focus{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:\!outline-none:focus{
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}
.focus\:\!outline-1:focus{
  outline-width: 1px !important;
}
.focus\:outline-0:focus{
  outline-width: 0px;
}
.focus\:\!outline-transparent:focus{
  outline-color: transparent !important;
}
.focus\:ring-0:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-1:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:\!ring-0:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
}
.focus\:ring-indigo-200:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(199 210 254 / var(--tw-ring-opacity));
}
.focus\:ring-gray-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(104 118 138 / var(--tw-ring-opacity));
}
.focus\:ring-blue-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));
}
.focus\:\!ring-transparent:focus{
  --tw-ring-color: transparent !important;
}
.focus\:ring-opacity-50:focus{
  --tw-ring-opacity: 0.5;
}
.focus\:ring-offset-0:focus{
  --tw-ring-offset-width: 0px;
}
.focus\:ring-offset-2:focus{
  --tw-ring-offset-width: 2px;
}
.focus\:hover\:font-light:hover:focus{
  font-weight: 300;
}
.focus\:hover\:shadow-md:hover:focus{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus-visible\:shadow-md:focus-visible{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.active\:bg-blue-800:active{
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity));
}
.active\:shadow-lg:active{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.active\:shadow-md:active{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.disabled\:font-bold:disabled{
  font-weight: 700;
}
.disabled\:shadow-md:disabled{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:first-child .group-first\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:last-child .group-last\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:nth-child(odd) .group-odd\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:nth-child(even) .group-even\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:visited .group-visited\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:checked .group-checked\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:focus-within .group-focus-within\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:hover .group-hover\:visible{
  visibility: visible;
}
.group:hover .group-hover\:bg-slate-100{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}
.group:hover .group-hover\:opacity-100{
  opacity: 1;
}
.group:hover .group-hover\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:hover .group-hover\:focus-within\:text-left:focus-within{
  text-align: left;
}
.group:focus .group-focus\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:focus-visible .group-focus-visible\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:active .group-active\:opacity-10{
  opacity: 0.1;
}
.group:active .group-active\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:disabled .group-disabled\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.peer:checked ~ .peer-checked\:bg-black-500{
  --tw-bg-opacity: 1;
  background-color: rgb(17 17 18 / var(--tw-bg-opacity));
}
.peer:checked ~ .peer-checked\:bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}
.peer:checked ~ .peer-checked\:bg-black-600{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}
.peer:checked ~ .peer-checked\:opacity-100{
  opacity: 1;
}
.peer:checked ~ .peer-checked\:opacity-0{
  opacity: 0;
}
.peer:checked ~ .peer-checked\:after\:translate-x-full::after{
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.peer:checked ~ .peer-checked\:after\:border-white::after{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.peer:focus ~ .peer-focus\:outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
[dir="ltr"] .ltr\:right-\[14px\]{
  right: 14px;
}
[dir="ltr"] .ltr\:right-6{
  right: 1.5rem;
}
[dir="ltr"] .ltr\:right-5{
  right: 1.25rem;
}
[dir="ltr"] .ltr\:-right-\[43px\]{
  right: -43px;
}
[dir="ltr"] .ltr\:right-0{
  right: 0px;
}
[dir="ltr"] .ltr\:left-full{
  left: 100%;
}
[dir="ltr"] .ltr\:mr-3{
  margin-right: 0.75rem;
}
[dir="ltr"] .ltr\:mr-2{
  margin-right: 0.5rem;
}
[dir="ltr"] .ltr\:mr-6{
  margin-right: 1.5rem;
}
[dir="ltr"] .ltr\:ml-2{
  margin-left: 0.5rem;
}
[dir="ltr"] .ltr\:ml-0{
  margin-left: 0px;
}
[dir="ltr"] .ltr\:ml-\[248px\]{
  margin-left: 248px;
}
[dir="ltr"] .ltr\:ml-3{
  margin-left: 0.75rem;
}
[dir="ltr"] .ltr\:ml-auto{
  margin-left: auto;
}
[dir="ltr"] .ltr\:ml-1{
  margin-left: 0.25rem;
}
[dir="ltr"] .ltr\:mr-1{
  margin-right: 0.25rem;
}
[dir="ltr"] .ltr\:mr-\[10px\]{
  margin-right: 10px;
}
[dir="ltr"] .ltr\:rotate-\[45deg\]{
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
[dir="ltr"] .ltr\:rounded-b{
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
[dir="ltr"] .ltr\:border-l-2{
  border-left-width: 2px;
}
[dir="ltr"] .ltr\:border-l{
  border-left-width: 1px;
}
[dir="ltr"] .ltr\:pr-4{
  padding-right: 1rem;
}
[dir="ltr"] .ltr\:pl-20{
  padding-left: 5rem;
}
[dir="ltr"] .ltr\:pl-28{
  padding-left: 7rem;
}
[dir="ltr"] .ltr\:pl-2{
  padding-left: 0.5rem;
}
[dir="ltr"] .ltr\:pl-\[22px\]{
  padding-left: 22px;
}
[dir="ltr"] .ltr\:pl-10{
  padding-left: 2.5rem;
}
[dir="ltr"] .ltr\:pl-4{
  padding-left: 1rem;
}
[dir="ltr"] .ltr\:text-left{
  text-align: left;
}
[dir="ltr"] .ltr\:text-right{
  text-align: right;
}
[dir="ltr"] .ltr\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
[dir="ltr"] .ltr\:before\:left-\[-8px\]::before{
  content: var(--tw-content);
  left: -8px;
}
[dir="ltr"] .ltr\:last\:text-right:last-child{
  text-align: right;
}
[dir="rtl"] .rtl\:left-\[14px\]{
  left: 14px;
}
[dir="rtl"] .rtl\:left-6{
  left: 1.5rem;
}
[dir="rtl"] .rtl\:left-5{
  left: 1.25rem;
}
[dir="rtl"] .rtl\:-left-\[43px\]{
  left: -43px;
}
[dir="rtl"] .rtl\:left-0{
  left: 0px;
}
[dir="rtl"] .rtl\:right-full{
  right: 100%;
}
[dir="rtl"] .rtl\:ml-3{
  margin-left: 0.75rem;
}
[dir="rtl"] .rtl\:ml-2{
  margin-left: 0.5rem;
}
[dir="rtl"] .rtl\:ml-6{
  margin-left: 1.5rem;
}
[dir="rtl"] .rtl\:mr-2{
  margin-right: 0.5rem;
}
[dir="rtl"] .rtl\:mr-0{
  margin-right: 0px;
}
[dir="rtl"] .rtl\:mr-\[248px\]{
  margin-right: 248px;
}
[dir="rtl"] .rtl\:mr-3{
  margin-right: 0.75rem;
}
[dir="rtl"] .rtl\:mr-auto{
  margin-right: auto;
}
[dir="rtl"] .rtl\:mr-1{
  margin-right: 0.25rem;
}
[dir="rtl"] .rtl\:ml-1{
  margin-left: 0.25rem;
}
[dir="rtl"] .rtl\:ml-\[10px\]{
  margin-left: 10px;
}
[dir="rtl"] .rtl\:mr-\[10px\]{
  margin-right: 10px;
}
[dir="rtl"] .rtl\:origin-top-left{
  transform-origin: top left;
}
[dir="rtl"] .rtl\:-rotate-45{
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
[dir="rtl"] .rtl\:rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
[dir="rtl"] .rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 1;
}
[dir="rtl"] .rtl\:rounded-t{
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
[dir="rtl"] .rtl\:border-r-2{
  border-right-width: 2px;
}
[dir="rtl"] .rtl\:border-r{
  border-right-width: 1px;
}
[dir="rtl"] .rtl\:pl-4{
  padding-left: 1rem;
}
[dir="rtl"] .rtl\:pr-20{
  padding-right: 5rem;
}
[dir="rtl"] .rtl\:pr-28{
  padding-right: 7rem;
}
[dir="rtl"] .rtl\:pr-2{
  padding-right: 0.5rem;
}
[dir="rtl"] .rtl\:pr-\[22px\]{
  padding-right: 22px;
}
[dir="rtl"] .rtl\:pr-10{
  padding-right: 2.5rem;
}
[dir="rtl"] .rtl\:pr-4{
  padding-right: 1rem;
}
[dir="rtl"] .rtl\:text-left{
  text-align: left;
}
[dir="rtl"] .rtl\:text-right{
  text-align: right;
}
[dir="rtl"] .rtl\:text-end{
  text-align: end;
}
[dir="rtl"] .rtl\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
[dir="rtl"] .rtl\:before\:-right-2::before{
  content: var(--tw-content);
  right: -0.5rem;
}
[dir="rtl"] .rtl\:last\:text-left:last-child{
  text-align: left;
}
[dir="rtl"] .rtl\:active\:text-center:active{
  text-align: center;
}
@media (prefers-reduced-motion: no-preference){
  .motion-safe\:shadow-md{
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  .motion-safe\:transition{
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
  .motion-safe\:hover\:text-center:hover{
    text-align: center;
  }
}
@media (prefers-reduced-motion: reduce){
  .motion-reduce\:shadow-md{
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  .motion-reduce\:transition{
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
}
.dark .dark\:block{
  display: block;
}
.dark .dark\:inline-block{
  display: inline-block;
}
.dark .dark\:hidden{
  display: none;
}
.dark .dark\:divide-slate-700 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-divide-opacity));
}
.dark .dark\:divide-slate-900 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(15 23 42 / var(--tw-divide-opacity));
}
.dark .dark\:border{
  border-width: 1px;
}
.dark .dark\:border-b{
  border-bottom-width: 1px;
}
.dark .dark\:border-slate-700{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
}
.dark .dark\:border-slate-800{
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity));
}
.dark .dark\:border-slate-600{
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity));
}
.dark .dark\:\!border-slate-600{
  --tw-border-opacity: 1 !important;
  border-color: rgb(71 85 105 / var(--tw-border-opacity)) !important;
}
.dark .dark\:\!border-slate-900{
  --tw-border-opacity: 1 !important;
  border-color: rgb(15 23 42 / var(--tw-border-opacity)) !important;
}
.dark .dark\:border-slate-400{
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity));
}
.dark .dark\:border-gray-600{
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity));
}
.dark .dark\:border-slate-900{
  --tw-border-opacity: 1;
  border-color: rgb(15 23 42 / var(--tw-border-opacity));
}
.dark .dark\:border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.dark .dark\:border-r-slate-700{
  --tw-border-opacity: 1;
  border-right-color: rgb(51 65 85 / var(--tw-border-opacity));
}
.dark .dark\:border-l-slate-700{
  --tw-border-opacity: 1;
  border-left-color: rgb(51 65 85 / var(--tw-border-opacity));
}
.dark .dark\:border-l-slate-800{
  --tw-border-opacity: 1;
  border-left-color: rgb(30 41 59 / var(--tw-border-opacity));
}
.dark .dark\:border-b-slate-900{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(15 23 42 / var(--tw-border-opacity));
}
.dark .dark\:bg-slate-900{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
.dark .dark\:bg-slate-800{
  --tw-bg-opacity: 1;
  background-color: #be9539;
}
.dark .dark\:bg-slate-700{
  --tw-bg-opacity: 1;
  background-color: #016241;
}
.dark .dark\:bg-slate-600{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}
.dark .dark\:bg-slate-500{
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
}
.dark .dark\:bg-gray-900{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
.dark .dark\:bg-slate-400{
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity));
}
.dark .dark\:bg-gray-700{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
}
.dark .dark\:bg-opacity-\[0\.36\]{
  --tw-bg-opacity: 0.36;
}
.dark .dark\:bg-opacity-25{
  --tw-bg-opacity: 0.25;
}
.dark .dark\:bg-opacity-50{
  --tw-bg-opacity: 0.5;
}
.dark .dark\:bg-opacity-70{
  --tw-bg-opacity: 0.7;
}
.dark .dark\:text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dark .dark\:text-slate-50{
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity));
}
.dark .dark\:text-slate-300{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.dark .dark\:text-gray-200{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}
.dark .dark\:text-slate-400{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}
.dark .dark\:text-slate-800{
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity));
}
.dark .dark\:text-slate-500{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}
.dark .dark\:text-slate-100{
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity));
}
.dark .dark\:text-slate-200{
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}
.dark .dark\:text-\[\#eee\]{
  --tw-text-opacity: 1;
  color: rgb(238 238 238 / var(--tw-text-opacity));
}
.dark .dark\:text-opacity-40{
  --tw-text-opacity: 0.4;
}
.dark .dark\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark .dark\:shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark .dark\:shadow-slate-700{
  --tw-shadow-color: #334155;
  --tw-shadow: var(--tw-shadow-colored);
}
.dark .dark\:ring-slate-700{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(51 65 85 / var(--tw-ring-opacity));
}
.dark .dark\:placeholder\:text-slate-400::placeholder{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}
.dark .dark\:before\:bg-slate-600::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}
.dark .dark\:even\:bg-slate-700:nth-child(even){
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
}
.dark .dark\:hover\:bg-slate-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}
.dark .dark\:hover\:bg-slate-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
}
.dark .dark\:hover\:bg-black-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(17 17 18 / var(--tw-bg-opacity));
}
.dark .dark\:hover\:bg-transparent:hover{
  background-color: transparent;
}
.dark .dark\:hover\:bg-opacity-50:hover{
  --tw-bg-opacity: 0.5;
}
.dark .dark\:hover\:bg-opacity-70:hover{
  --tw-bg-opacity: 0.7;
}
.dark .dark\:hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dark .dark\:focus\:text-left:focus{
  text-align: left;
}
.dark .group:hover .dark\:group-hover\:bg-slate-800{
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}
@media (min-width: 640px){
  .sm\:container{
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    padding-right: 15px;
    padding-left: 15px;
  }
  @media (min-width: 640px){
    .sm\:container{
      max-width: 640px;
      padding-right: 15px;
      padding-left: 15px;
    }
  }
  @media (min-width: 768px){
    .sm\:container{
      max-width: 768px;
    }
  }
  @media (min-width: 1024px){
    .sm\:container{
      max-width: 1024px;
      padding-right: 15px;
      padding-left: 15px;
    }
  }
  @media (min-width: 1280px){
    .sm\:container{
      max-width: 1280px;
      padding-right: 15px;
      padding-left: 15px;
    }
  }
  .sm\:mx-1{
    margin-left: 0.25rem;
    margin-right: 0.25rem;
  }
  .sm\:mb-0{
    margin-bottom: 0px;
  }
  .sm\:mr-4{
    margin-right: 1rem;
  }
  .sm\:flex{
    display: flex;
  }
  .sm\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .sm\:justify-end{
    justify-content: flex-end;
  }
  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .sm\:text-center{
    text-align: center;
  }
  .sm\:font-bold{
    font-weight: 700;
  }
  .sm\:tabular-nums{
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
  }
  .sm\:shadow-md{
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  .sm\:active\:shadow-md:active{
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  @media (prefers-reduced-motion: no-preference){
    .group:active .sm\:motion-safe\:group-active\:focus\:opacity-10:focus{
      opacity: 0.1;
    }
  }
}
@media (min-width: 768px){
  @media (min-width: 640px){
    .md\:sm\:text-center{
      text-align: center;
    }
  }
  .md\:container{
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    padding-right: 15px;
    padding-left: 15px;
  }
  @media (min-width: 640px){
    .md\:container{
      max-width: 640px;
      padding-right: 15px;
      padding-left: 15px;
    }
  }
  @media (min-width: 768px){
    .md\:container{
      max-width: 768px;
    }
  }
  @media (min-width: 1024px){
    .md\:container{
      max-width: 1024px;
      padding-right: 15px;
      padding-left: 15px;
    }
  }
  @media (min-width: 1280px){
    .md\:container{
      max-width: 1280px;
      padding-right: 15px;
      padding-left: 15px;
    }
  }
  .md\:top-\[140px\]{
    top: 140px;
  }
  .md\:col-span-2{
    grid-column: span 2 / span 2;
  }
  .md\:my-0{
    margin-top: 0px;
    margin-bottom: 0px;
  }
  .md\:mx-8{
    margin-left: 2rem;
    margin-right: 2rem;
  }
  .md\:ml-0{
    margin-left: 0px;
  }
  .md\:mr-0{
    margin-right: 0px;
  }
  .md\:mb-0{
    margin-bottom: 0px;
  }
  .md\:mt-3{
    margin-top: 0.75rem;
  }
  .md\:mr-2{
    margin-right: 0.5rem;
  }
  .md\:block{
    display: block;
  }
  .md\:inline-block{
    display: inline-block;
  }
  .md\:flex{
    display: flex;
  }
  .md\:hidden{
    display: none;
  }
  .md\:h-auto{
    height: auto;
  }
  .md\:h-1\/2{
    height: 50%;
  }
  .md\:h-\[186px\]{
    height: 186px;
  }
  .md\:h-8{
    height: 2rem;
  }
  .md\:h-12{
    height: 3rem;
  }
  .md\:min-h-\[300px\]{
    min-height: 300px;
  }
  .md\:w-\[calc\(100\%-320px\)\]{
    width: calc(100% - 320px);
  }
  .md\:w-\[186px\]{
    width: 186px;
  }
  .md\:w-8{
    width: 2rem;
  }
  .md\:w-12{
    width: 3rem;
  }
  .md\:max-w-4xl{
    max-width: 56rem;
  }
  .md\:max-w-\[345px\]{
    max-width: 345px;
  }
  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .md\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .md\:flex-row{
    flex-direction: row;
  }
  .md\:justify-start{
    justify-content: flex-start;
  }
  .md\:gap-5{
    gap: 1.25rem;
  }
  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
  .md\:space-x-5 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1.25rem * var(--tw-space-x-reverse));
    margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .md\:space-x-6 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .md\:space-x-3 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .md\:space-x-4 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .md\:space-x-10 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(2.5rem * var(--tw-space-x-reverse));
    margin-left: calc(2.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .md\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .md\:py-6{
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
  .md\:pt-\[84px\]{
    padding-top: 84px;
  }
  .md\:pt-6{
    padding-top: 1.5rem;
  }
  .md\:pr-0{
    padding-right: 0px;
  }
  .md\:text-center{
    text-align: center;
  }
  .md\:text-start{
    text-align: start;
  }
  .md\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }
  .md\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }
  .md\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
  .md\:font-bold{
    font-weight: 700;
  }
  .md\:opacity-50{
    opacity: 0.5;
  }
  .md\:shadow-sm{
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  .md\:shadow-md{
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  .md\:hover\:text-center:hover{
    text-align: center;
  }
  .md\:hover\:text-right:hover{
    text-align: right;
  }
  .md\:hover\:opacity-20:hover{
    opacity: 0.2;
  }
  .group:focus .md\:group-focus\:shadow-md{
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  [dir="ltr"] .ltr\:md\:right-\[-29px\]{
    right: -29px;
  }
  [dir="ltr"] .ltr\:md\:text-right{
    text-align: right;
  }
  [dir="ltr"] .ltr\:md\:text-start{
    text-align: start;
  }
  [dir="rtl"] .rtl\:md\:left-\[-29px\]{
    left: -29px;
  }
  [dir="rtl"] .rtl\:md\:text-right{
    text-align: right;
  }
  [dir="rtl"] .rtl\:md\:text-end{
    text-align: end;
  }
  @media (prefers-reduced-motion: no-preference){
    .md\:motion-safe\:hover\:transition:hover{
      transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 150ms;
    }
  }
}
@media (min-width: 1024px){
  .lg\:top-0{
    top: 0px;
  }
  .lg\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .lg\:col-span-8{
    grid-column: span 8 / span 8;
  }
  .lg\:col-span-2{
    grid-column: span 2 / span 2;
  }
  .lg\:col-span-3{
    grid-column: span 3 / span 3;
  }
  .lg\:col-span-9{
    grid-column: span 9 / span 9;
  }
  .lg\:col-span-7{
    grid-column: span 7 / span 7;
  }
  .lg\:col-span-5{
    grid-column: span 5 / span 5;
  }
  .lg\:block{
    display: block;
  }
  .lg\:inline-block{
    display: inline-block;
  }
  .lg\:flex{
    display: flex;
  }
  .lg\:hidden{
    display: none;
  }
  .lg\:h-full{
    height: 100%;
  }
  .lg\:h-\[32px\]{
    height: 32px;
  }
  .lg\:h-8{
    height: 2rem;
  }
  .lg\:min-h-full{
    min-height: 100%;
  }
  .lg\:w-\[576px\]{
    width: 576px;
  }
  .lg\:w-1\/2{
    width: 50%;
  }
  .lg\:w-\[32px\]{
    width: 32px;
  }
  .lg\:w-8{
    width: 2rem;
  }
  .lg\:max-w-\[360px\]{
    max-width: 360px;
  }
  .lg\:grid-cols-\[200px\2c repeat\(auto-fill\2c minmax\(15\%\2c 100px\)\)\2c 300px\]{
    grid-template-columns: 200px repeat(auto-fill,minmax(15%,100px)) 300px;
  }
  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .lg\:grid-cols-6{
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .lg\:grid-cols-5{
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  .lg\:justify-end{
    justify-content: flex-end;
  }
  .lg\:space-x-5 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1.25rem * var(--tw-space-x-reverse));
    margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .lg\:space-y-0 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
  .lg\:space-x-3 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .lg\:bg-slate-100{
    --tw-bg-opacity: 1;
    background-color: rgb(241 245 249 / var(--tw-bg-opacity));
  }
  .lg\:bg-slate-50{
    --tw-bg-opacity: 1;
    background-color: rgb(248 250 252 / var(--tw-bg-opacity));
  }
  .lg\:pt-0{
    padding-top: 0px;
  }
  .lg\:text-center{
    text-align: center;
  }
  .lg\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }
  .lg\:font-bold{
    font-weight: 700;
  }
  .lg\:\!opacity-50{
    opacity: 0.5 !important;
  }
  .lg\:shadow-md{
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  [dir="rtl"] .lg\:rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 1;
  }
  .dark .lg\:dark\:bg-slate-900{
    --tw-bg-opacity: 1;
    background-color: rgb(15 23 42 / var(--tw-bg-opacity));
  }
  .dark .lg\:dark\:shadow-md{
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}
@media (min-width: 1280px){
  .xl\:fixed{
    position: fixed;
  }
  .xl\:absolute{
    position: absolute;
  }
  .xl\:col-span-2{
    grid-column: span 2 / span 2;
  }
  .xl\:col-span-8{
    grid-column: span 8 / span 8;
  }
  .xl\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .xl\:col-span-6{
    grid-column: span 6 / span 6;
  }
  .xl\:col-span-3{
    grid-column: span 3 / span 3;
  }
  .xl\:col-span-5{
    grid-column: span 5 / span 5;
  }
  .xl\:mr-8{
    margin-right: 2rem;
  }
  .xl\:block{
    display: block;
  }
  .xl\:inline-block{
    display: inline-block;
  }
  .xl\:flex{
    display: flex;
  }
  .xl\:hidden{
    display: none;
  }
  .xl\:w-\[248px\]{
    width: 248px;
  }
  .xl\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .xl\:grid-cols-1{
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .xl\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .xl\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .xl\:grid-cols-6{
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
  .xl\:space-y-2 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
  }
  .xl\:text-\[70px\]{
    font-size: 70px;
  }
  .xl\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  .xl\:leading-\[70px\]{
    line-height: 70px;
  }
  .xl\:text-slate-400{
    --tw-text-opacity: 1;
    color: rgb(148 163 184 / var(--tw-text-opacity));
  }
  .xl\:shadow-md{
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  .xl\:focus\:disabled\:\!float-right:disabled:focus{
    float: right !important;
  }
  [dir="ltr"] .xl\:ltr\:ml-\[248px\]{
    margin-left: 248px;
  }
  [dir="rtl"] .xl\:rtl\:mr-\[248px\]{
    margin-right: 248px;
  }
}
@media (min-width: 1536px){
  .\32xl\:bottom-\[-160px\]{
    bottom: -160px;
  }
  .\32xl\:col-span-3{
    grid-column: span 3 / span 3;
  }
  .\32xl\:col-span-9{
    grid-column: span 9 / span 9;
  }
  .\32xl\:col-span-8{
    grid-column: span 8 / span 8;
  }
  .\32xl\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .\32xl\:mb-10{
    margin-bottom: 2.5rem;
  }
  .\32xl\:mt-12{
    margin-top: 3rem;
  }
  .\32xl\:w-\[107px\]{
    width: 107px;
  }
  .\32xl\:shadow-md{
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  @media (prefers-reduced-motion: no-preference){
    .dark .\32xl\:dark\:motion-safe\:focus-within\:shadow-md:focus-within{
      --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
      --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
      box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    }
  }
}
@media (orientation: portrait){
  .portrait\:text-center{
    text-align: center;
  }
}
