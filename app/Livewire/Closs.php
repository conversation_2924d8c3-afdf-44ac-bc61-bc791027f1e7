<?php

namespace App\Livewire;

use App\Mail\ClassStarted;
use App\Models\Clss;
use App\Models\ClssReport;
use App\Models\Notification;
use App\Models\Ratings;
use App\Models\SalesReport;
use App\Models\Settings;
use App\Models\Student;
use App\Models\SuperVisorReport;
use App\Models\TeacherQualifications;
use App\Models\TeacherReport;
use App\Models\User;
use App\Models\ZoomApp;
use Carbon\Carbon;
use DateTime;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;
use App\Traits\UseZoom;
use App\Traits\UseFilter;
use App\Traits\UseGoogle;
use App\Traits\UseDefault;
use App\Traits\UseSMS;
use Illuminate\Support\Facades\Mail;

class Closs extends Component
{
    use WithPagination, UseZoom, UseFilter, UseDefault, UseSMS;
    //use UseGoogle;

// props of class model
    public $class_id = '';
    public $student_id = '';
    public $student_name = '';
    public $teacher_id = '';
    public $teacher_name = '';
    public $qualifi_id;
    public $qualifi_name;
    public $date_time = '';
    public $start_meeting = '';
    public $join_meeting = '';
    public $supreport_id = '';
    public $teachreport_id = '';
    public $clssreport_id = [];
    public $studentgenderpref = '';
    public $defaulttimezone = '';
    public $revision_eval = '';
    public $recite_eval = '';
    public $lastupdateuser_id;
    public $lastupdatedat;

// teacher / supervisor reports
    public $reprevision = 'مراجعة: ';
    public $reprecite = 'تسميع: ';
    public $internet;
    public $camera;
    public $smartboard;
    public $audio;
    public $teachbackground;
    public $teachenvironment;
    public $teachrelationship;
    public $teacheducational;
    public $notes = 'اكتب ملاحظاتك هنا ...';
    public $class_rate;
    public $class_rated = false;

    public $classinweek;
    public $classesdaytime = [
        ['classweekdays' => '', 'classweektime' => '09:00', 'classweektimepmam' => 'AM'],
    ];
    public $classesdaytimenum = 1;

// selected / select all
    public $selectedclasses = [];
    public $selectedsupreports = [];
    public $selectedteachreports = [];
    public $selectedclssreports = [];
    public $selectedclassesmeetings = [];
    public $selectall = false;

    // who is actice in the class
    public $whoctive = '';

// default properties
    public $students;
    // public $teachers;
    public $classses;
    public $qualifications;

// create method
    public function save()
    {
        $validated = $this->validate([
            'student_id' => 'required|numeric',
            'teacher_id' => 'required|numeric',
            'qualifi_id' => 'required|numeric',
            'date_time' => 'required',
            'start_meeting' => '',
            'join_meeting' => '',
        ]);
        $student = User::find($this->student_id);
        $teacher = User::find($this->teacher_id);
        $teacherzoomapp = ZoomApp::where('user_id', $this->teacher_id)->first();
        if (!empty($teacherzoomapp)) {
            $this->generateZoomAccessToken($teacher);

            $this->createMeeting($teacher->zoominfo->zoom_user_id, $student->name, $validated['date_time']);
            // dd($this->responseData);

            $class = Clss::create([
                'student_id' => $validated['student_id'],
                'teacher_id' => $validated['teacher_id'],
                'qualifi_id' => $validated['qualifi_id'],
                'date_time' =>  $validated['date_time'],
                'zmeeting_id' => $this->responseData['id'],
                'start_meeting' => $this->responseData['start_url'],
                'join_meeting' => $this->responseData['join_url'],
                'lastupdateuser_id' => auth()->user()->id,
                'is_active' => 'not',
            ]);
            $studentbalance = $student->numofclasses;
            $student->student->update([
                'numofclasses' => $studentbalance+1,
            ]);

            $to = '201004743833';
            $txtmsg = 'تم انشاء حلقة جديدة للطالب ' . $class->student->name;
            $this->sendSMS($to, $txtmsg);

            $notifiedusers = User::whereIn('role', ['superadmin','admin','moderator', 'supervisor'])->pluck('id')->push($this->student_id, $this->teacher_id);
            foreach ($notifiedusers as $notifieduser) {
                Notification::create([
                    'user_id' => $notifieduser,
                    'title' => 'new class',
                    'url' => 'class?q=' . $student->name . '&te=' . $teacher->id . '&stdate=' . Carbon::parse($validated['date_time'])->format('Y-m-d') . '&endate=' . Carbon::parse($validated['date_time'])->format('Y-m-d'),
                    'message' => 'تم اضافة حلقة جديدة للطالب"' . $student->name . '" والمعلم "' . $teacher->name . '" بواسطة "' . auth()->user()->name . '".',
                ]);
            }
            $this->reset('accessToken');
            $this->closemodal('add');
            $this->dispatch('flashsaved');
        }else{
            $this->errormessage('no-zoomapp');
        }
    }

// add schedule method
    public function addschedule()
    {
        $validated = $this->validate([
            'student_id' => 'required|numeric',
            'teacher_id' => 'required|numeric',
            'qualifi_id' => 'required|numeric',
            'date_time' => 'required',
            'start_meeting' => '',
            'join_meeting' => '',
        ]);
        $student = User::find($this->student_id);
        $teacher = User::find($this->teacher_id);
        $teacherzoomapp = ZoomApp::where('user_id', $this->teacher_id)->first();
        if (!empty($teacherzoomapp)) {

    // 12/7=1.7
            $min = floor($this->classinweek / count($this->classesdaytime)); // 1
            $max = ceil($this->classinweek / count($this->classesdaytime));  // 2
            $module = $this->classinweek % count($this->classesdaytime);     // 5
            $days = ['Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'];

            $this->generateZoomAccessToken($teacher);


            // dd($max, $min);
            foreach ($this->classesdaytime as $key => $daytime) {
                $classtime = Carbon::parse($validated['date_time'] . ' ' . $daytime['classweektime'] . ' ' . $daytime['classweektimepmam']);
                if ($key == 0) {
                    for ($i=0; $i < $max; $i++) {
                        // dd($classtime->format('Y-m-d\TH:i:00'));
                        $this->createMeeting($teacher->zoominfo->zoom_user_id, $student->name, $classtime->format('Y-m-d\TH:i:00'));

                        Clss::create([
                                'student_id' => $validated['student_id'],
                                'teacher_id' => $validated['teacher_id'],
                                'qualifi_id' => $validated['qualifi_id'],
                                'date_time' => $classtime->format('Y-m-d H:i:00'),
                                'zmeeting_id' => $this->responseData['id'],
                                'start_meeting' => $this->responseData['start_url'],
                                'join_meeting' => $this->responseData['join_url'],
                                'lastupdateuser_id' => auth()->user()->id,
                            ]);

                        $classtime = $classtime->addDays(7);
                    }
                    $module--;
                } else {
                    if ($module > 0) {
                    $currentWeekday = $classtime->format('l');
                    $desiredWeekday = $days[$daytime['classweekdays']];
                    // Days difference calculation
                    $daysToAdd = ($desiredWeekday === $currentWeekday) ? 0 : (array_search($desiredWeekday, $days) - array_search($currentWeekday, $days) + 7) % 7;
                    $classtime = Carbon::createFromFormat('Y-m-d h:i A', $validated['date_time'] . ' ' . $daytime['classweektime'] . ' ' . $daytime['classweektimepmam'])->addDays($daysToAdd);
                        for ($i=0; $i < $max; $i++) {
                        $this->createMeeting($teacher->zoominfo->zoom_user_id, $student->name, $classtime->format('Y-m-d\TH:i:00'));

                            Clss::create([
                                    'student_id' => $validated['student_id'],
                                    'teacher_id' => $validated['teacher_id'],
                                    'qualifi_id' => $validated['qualifi_id'],
                                    'date_time' => $classtime->format('Y-m-d H:i:00'),
                                    'zmeeting_id' => $this->responseData['id'],
                                    'start_meeting' => $this->responseData['start_url'],
                                    'join_meeting' => $this->responseData['join_url'],
                                    'lastupdateuser_id' => auth()->user()->id,
                                ]);

                            $classtime = $classtime->addDays(7);
                        }
                            $module--;
                    } else {
                        $currentWeekday = $classtime->format('l');
                        $desiredWeekday = $days[$daytime['classweekdays']];
                        // Days difference calculation
                        $daysToAdd = ($desiredWeekday === $currentWeekday) ? 0 : (array_search($desiredWeekday, $days) - array_search($currentWeekday, $days) + 7) % 7;
                        $classtime = Carbon::createFromFormat('Y-m-d h:i A', $this->date_time . ' ' . $daytime['classweektime'] . ' ' . $daytime['classweektimepmam'])->addDays($daysToAdd);
                        for ($i=0; $i < $min; $i++) {
                        $this->createMeeting($teacher->zoominfo->zoom_user_id, $student->name, $classtime->format('Y-m-d\TH:i:00'));

                            Clss::create([
                                'student_id' => $validated['student_id'],
                                'teacher_id' => $validated['teacher_id'],
                                'qualifi_id' => $validated['qualifi_id'],
                                'date_time' => $classtime->format('Y-m-d H:i:00'),
                                'zmeeting_id' => $this->responseData['id'],
                                'start_meeting' => $this->responseData['start_url'],
                                'join_meeting' => $this->responseData['join_url'],
                                'lastupdateuser_id' => auth()->user()->id,
                            ]);
                            $classtime = $classtime->addDays(7);
                        }
                    }
                }
            }

            $notifiedusers = User::whereIn('role', ['superadmin','admin','moderator', 'supervisor'])->pluck('id')->push($this->student_id, $this->teacher_id);
            foreach ($notifiedusers as $notifieduser) {
                Notification::create([
                    'user_id' => $notifieduser,
                    'title' => 'new schadule',
                    'url' => 'class?q=' . $student->name . '&te=' . $teacher->id . '&stdate=' . Carbon::parse($validated['date_time'])->format('Y-m-d'),
                    'message' => 'تم اضافة جدول جديد للطالب"' . $student->name . '" والمعلم "' . $teacher->name . '" بداية من يوم ' . $validated['date_time'] . '" بواسطة "' . auth()->user()->name . '".',
                ]);
            }

            $this->closemodal('addschedule');
            $this->dispatch('flashsaved');
        }else{
            $this->errormessage('no-zoomapp');
        }
    }
    // updated method for add schadule
        #[Computed]
        public function updateddatetime()
        {
            if(DateTime::createFromFormat('Y-m-d', $this->date_time) !== false)
            {
                if (!empty($this->date_time)) {
                $startday = Carbon::createFromFormat('Y-m-d', $this->date_time)->dayOfWeek;
            } else {
                $startday = '';
            }
            $this->classesdaytime = [
                ['classweekdays' => $startday, 'classweektime' => '9:00', 'classweektimepmam' => 'AM'],
            ];
            $this->reset('classesdaytimenum');
            }
        }

        public function addclassday($index)
        {
            $this->classesdaytimenum++;
            $this->classesdaytime[$index] = ['classweekdays' => '', 'classweektime' => '09:00', 'classweektimepmam' => 'AM'];
        }
        public function removeclassday($index)
        {
            $this->classesdaytimenum--;
            unset($this->classesdaytime[$index]);
            $startday = $this->classesdaytime[0];
            $slicedarr = array_slice($this->classesdaytime, 1);
            ksort($slicedarr);
            $this->classesdaytime = array_merge([$startday], $slicedarr);
            array_values($this->classesdaytime);
        }

// prepairing for update moethod
    public function showedit(Clss $class)
    {
        $this->class_id = $class->id;
        $this->student_id = $class->student_id;
        $this->student_name = $class->student->name;
        $this->teacher_id = $class->teacher_id;
        $this->teacher_name= $class->teacher->name;
        $this->qualifi_id= $class->qualifi_id;
        $this->date_time = $class->date_time;
        $this->start_meeting = $class->start_meeting;
        $this->join_meeting = $class->join_meeting;
        $this->lastupdateuser_id = $class->user->name;
        $this->lastupdatedat = Carbon::parse($class->updated_at)->format('Y-m-d || A g:i:s');

        $this->openmodal('edit');
    }

// update method
    public function update()
    {
        $validated = $this->validate([
            'student_id' => 'required|numeric',
            'teacher_id' => 'required|numeric',
            'date_time' => 'required',
            'start_meeting' => 'required|url',
            'join_meeting' => 'required|url',
            'lastupdateuser_id' => '',
        ]);
        $validated['lastupdateuser_id'] = auth()->user()->id;

        $teacher = User::find($this->teacher_id);
        $student = User::find($this->student_id);
        $class = Clss::find($this->class_id);
        $teacherzoomapp = ZoomApp::where('user_id', $this->teacher_id)->first();
        if (!empty($teacherzoomapp)) {
            $this->updateMeeting($class->zmeeting_id, $validated['date_time']);
            $class->update($validated);

            $notifiedusers = User::whereIn('role', ['superadmin','admin','moderator', 'supervisor'])->pluck('id')->push($this->student_id, $this->teacher_id);
            foreach ($notifiedusers as $notifieduser) {
                Notification::create([
                    'user_id' => $notifieduser,
                    'title' => 'class updated',
                    'url' => 'class?q=' . $student->name . '&te=' . $teacher->id . '&stdate=' . Carbon::parse($validated['date_time'])->format('Y-m-d') . '&endate=' . Carbon::parse($validated['date_time'])->format('Y-m-d'),
                    'message' => 'تم تعديل حلقة للطالب"' . $student->name . '" والمعلم "' . $teacher->name . '" بواسطة "' . auth()->user()->name . '".',
                ]);
            }

            $this->closemodal('edit');
            $this->dispatch('flashupdated');
        }else{
            $this->errormessage('no-zoomapp');
        }
    }

// prepairing for delete
    public function showdelete(Clss $class)
    {
        $this->class_id = $class->id;
        $this->supreport_id = !empty($class->supreport->id) ? $class->supreport->id : '';
        $this->teachreport_id = !empty($class->teachreport->id) ? $class->teachreport->id : '';
        $this->openmodal('delete');
    }

// single delete moethod
    public function delete()
    {
        $class = Clss::find($this->class_id);
        $this->student_id = $class->student_id;
        $this->teacher_id = $class->teacher_id;
        $student = User::find($this->student_id);
        $teacher = User::find($this->teacher_id);

        $class->delete();

        $this->deleteMeeting($class->zmeeting_id);

        $notifiedusers = User::whereIn('role', ['superadmin','admin','moderator', 'supervisor'])->pluck('id')->push($this->student_id, $this->teacher_id);
        foreach ($notifiedusers as $notifieduser) {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'class deleted',
                'url' => 'class?q=' . $student->name . '&te=' . $teacher->id,
                'message' => 'تم حذف حلقة للطالب"' . $student->name . '" والمعلم "' . $teacher->name . '"وكان موعدها ' . Carbon::parse($class->date_time)->format('Y-m-d') . ' بواسطة "' . auth()->user()->name . '".',
            ]);
        }

        if (!empty($this->supreport_id)) {
            SuperVisorReport::find($this->supreport_id)->delete();
        }
        if (!empty($this->teachreport_id)) {
            TeacherReport::find($this->teachreport_id)->delete();
        }
        if (!empty($this->class_id)) {
            ClssReport::query()->where('class_id',  $this->class_id)->delete();
        }

        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }

// prepairing for multi delete moethod
    #[Computed]
    public function updatedselectedclasses()
    {
        $this->selectedsupreports = SuperVisorReport::query()->whereIn('class_id',  $this->selectedclasses)->pluck('id');
        $this->selectedteachreports = TeacherReport::query()->whereIn('class_id',  $this->selectedclasses)->pluck('id');
        $this->selectedclssreports = ClssReport::query()->whereIn('class_id',  $this->selectedclasses)->pluck('id');
        $this->selectedclassesmeetings = Clss::query()->whereIn('id',  $this->selectedclasses)->pluck('zmeeting_id');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }

// multi delete moethod
    public function deleteselected()
    {
        Clss::query()->whereIn('id', $this->selectedclasses)->delete();
        SuperVisorReport::query()->whereIn('id', $this->selectedsupreports)->delete();
        TeacherReport::query()->whereIn('id', $this->selectedteachreports)->delete();
        ClssReport::query()->whereIn('id', $this->selectedclssreports)->delete();

        foreach ($this->selectedclassesmeetings as $meeting) {
            $this->deleteMeeting($meeting);
        }

        $this->selectedclasses = [];
        $this->selectedsupreports = [];
        $this->selectedteachreports = [];
        $this->selectedclssreports = [];
        $this->selectall = false;

        $this->closemodal('deleteselected');
        $this->dispatch('flashdeleted');
    }

// select all method
    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedclasses = $this->perpage === 'all' ? Clss::filterdate($this->filterdatestart, $this->filterdateend)
            ->search($this->search)
            ->filterteacher($this->filterteacher)
            ->filterstudent($this->filterstudent)
            ->filteractive($this->filteractive)
            ->orderBy($this->sortBy, $this->sortDir)
            ->pluck('id') :
            Clss::filterdate($this->filterdatestart, $this->filterdateend)
            ->search($this->search)
            ->filterteacher($this->filterteacher)
            ->filterstudent($this->filterstudent)
            ->filteractive($this->filteractive)
            ->orderBy($this->sortBy, $this->sortDir)
            ->paginate($this->perpage)
            ->pluck('id');
            $this->selectedsupreports = SuperVisorReport::paginate($this->perpage)->pluck('id');
            $this->selectedteachreports = TeacherReport::paginate($this->perpage)->pluck('id');
            $this->selectedclssreports = ClssReport::paginate($this->perpage)->pluck('id');
            $this->selectedclassesmeetings = Clss::paginate($this->perpage)->pluck('zmeeting_id');
    } else {
            $this->selectedclasses = [];
            $this->selectedsupreports = [];
            $this->selectedteachreports = [];
            $this->selectedclssreports = [];
            $this->selectedclassesmeetings = [];
        }
    }

// reports adding methods
    public function suprepadd(Clss $class)
    {
        $this->class_id = $class->id;
        $this->openmodal('add-supervisor-report');
    }
    public function suprepview(Clss $class)
    {
        $this->class_id = $class->id;
        $this->internet = $class->supreport->internet;
        $this->camera = $class->supreport->camera;
        $this->smartboard = $class->supreport->smartboard;
        $this->audio = $class->supreport->audio;
        $this->teachbackground = $class->supreport->teachbackground;
        $this->teachenvironment = $class->supreport->teachenvironment;
        $this->teachrelationship = $class->supreport->teachrelationship;
        $this->teacheducational = $class->supreport->teacheducational;
        $this->notes = $class->supreport->notes;
        $this->openmodal('view-supervisor-report');
    }
    public function tearepadd(Clss $class)
    {
        $this->class_id = $class->id;
        $this->teacher_id = $class->teacher_id;
        $this->openmodal('add-teacher-report');
    }
    public function tearepview(Clss $class)
    {
        $this->class_id = $class->id;
        $this->teacher_id = $class->teacher_id;
        $this->revision_eval = $class->teachreport->revision_eval;
        $this->recite_eval = $class->teachreport->recite_eval;
        $this->reprevision = $class->teachreport->revision;
        $this->reprecite = $class->teachreport->recite;
        $this->class_rate = Ratings::where('class_id', $class->id)->first()->rate ?? '';
        if(!empty($this->class_rate))
        {
            $this->class_rated = true;
        }
        $this->openmodal('view-teacher-report');
    }
    public function addrate($userid, $classid, $teacherid)
    {
        if(!empty($this->class_rate))
        {
            $validatedrate = $this->validate([
                'class_rate' => 'string|nullable|sometimes',
            ]);
            Ratings::create([
                'class_id' => $classid,
                'rater_id' => $userid,
                'ratee_id' => $teacherid,
                'rate' => $validatedrate['class_rate'],
            ]);
        }
        $this->closemodal('view-teacher-report');
    }
    public function reportadd($report)
    {
        if($report == 'teacherreport')
        {
            $validated = $this->validate([
                'reprevision' => 'required|string|max:1000',
                'reprecite' => 'required|string|max:1000',
                'recite_eval' => 'required|string|max:20',
                'revision_eval' => 'required|string|max:20',
            ]);

            TeacherReport::create([
                'class_id' => $this->class_id,
                'user_id' => auth()->user()->id,
                'recite_eval' => $validated['recite_eval'],
                'revision_eval' => $validated['revision_eval'],
                'revision' => $validated['reprevision'],
                'recite' => $validated['reprecite'],
            ]);
            $class = Clss::find($this->class_id);
            $notifiedusers = User::where('role', 'admin')->pluck('id');
            foreach ($notifiedusers as $notifieduser) {
                Notification::create([
                    'user_id' => $notifieduser,
                    'title' => 'new report',
                    'url' => 'class?q=' . $class->student->name . '&te=' . $class->teacher->id . '&stdate=' . Carbon::parse($class->date_time)->format('Y-m-d') . '&endate=' . Carbon::parse($class->date_time)->format('Y-m-d'),
                    'message' => 'تم اضافة تقرير لحلقة للطالب"' . $class->student->name . '" والمعلم "' . $class->teacher->name . '" بواسطة "' . auth()->user()->name . '".',
                ]);
            }
            $this->closemodal('add-teacher-report');
        } elseif ($report == 'supervisorreport')
        {
            $validated = $this->validate([
                'internet' => 'required|numeric|max:11',
                'camera' => 'required|numeric|max:11',
                'smartboard' => 'required|numeric|max:11',
                'audio' => 'required|numeric|max:11',
                'teachbackground' => 'required|numeric|max:11',
                'teachenvironment' => 'required|numeric|max:11',
                'teachrelationship' => 'required|numeric|max:11',
                'teacheducational' => 'required|numeric|max:11',
                'notes' => 'string|max:2000',
            ]);
            SuperVisorReport::create([
                'class_id' => $this->class_id,
                'user_id' => auth()->user()->id,
                'internet' => $validated['internet'],
                'camera' => $validated['camera'],
                'smartboard' => $validated['smartboard'],
                'audio' => $validated['audio'],
                'teachbackground' => $validated['teachbackground'],
                'teachenvironment' => $validated['teachenvironment'],
                'teachrelationship' => $validated['teachrelationship'],
                'teacheducational' => $validated['teacheducational'],
                'notes' => $validated['notes'],
            ]);
            $class = Clss::find($this->class_id);
            $notifiedusers = User::where('role', 'admin')->pluck('id');
            foreach ($notifiedusers as $notifieduser) {
                Notification::create([
                    'user_id' => $notifieduser,
                    'title' => 'new report',
                    'url' => 'class?q=' . $class->student->name . '&te=' . $class->teacher->id . '&stdate=' . Carbon::parse($class->date_time)->format('Y-m-d') . '&endate=' . Carbon::parse($class->date_time)->format('Y-m-d'),
                    'message' => 'تم اضافة تقرير لحلقة للطالب"' . $class->student->name . '" والمعلم "' . $class->teacher->name . '" بواسطة "' . auth()->user()->name . '".',
                ]);
            }
            $this->closemodal('add-supervisor-report');
        }

        $this->dispatch('flashsaved');
    }
    public function reportupdate($report)
    {
        if($report === 'teacherreport')
        {
            $validated = $this->validate([
                'reprevision' => 'required|string|max:1000',
                'reprecite' => 'required|string|max:1000',
                'recite_eval' => 'required|string|max:20',
                'revision_eval' => 'required|string|max:20',
            ]);

            TeacherReport::where('class_id', $this->class_id)->update([
                'class_id' => $this->class_id,
                'user_id' => auth()->user()->id,
                'recite_eval' => $validated['recite_eval'],
                'revision_eval' => $validated['revision_eval'],
                'revision' => $validated['reprevision'],
                'recite' => $validated['reprecite'],
            ]);
            $class = Clss::find($this->class_id);
            $notifiedusers = User::where('role', 'admin')->pluck('id');
            foreach ($notifiedusers as $notifieduser) {
                Notification::create([
                    'user_id' => $notifieduser,
                    'title' => 'new report',
                    'url' => 'class?q=' . $class->student->name . '&te=' . $class->teacher->id . '&stdate=' . Carbon::parse($class->date_time)->format('Y-m-d') . '&endate=' . Carbon::parse($class->date_time)->format('Y-m-d'),
                    'message' => 'تم تعديل تقرير لحلقة للطالب"' . $class->student->name . '" والمعلم "' . $class->teacher->name . '" بواسطة "' . auth()->user()->name . '".',
                ]);
            }
            $this->closemodal('view-teacher-report');
        } elseif ($report == 'supervisorreport')
        {
            $validated = $this->validate([
                'internet' => 'required|numeric|max:11',
                'camera' => 'required|numeric|max:11',
                'smartboard' => 'required|numeric|max:11',
                'audio' => 'required|numeric|max:11',
                'teachbackground' => 'required|numeric|max:11',
                'teachenvironment' => 'required|numeric|max:11',
                'teachrelationship' => 'required|numeric|max:11',
                'teacheducational' => 'required|numeric|max:11',
                'notes' => 'string|max:2000',
            ]);
            SuperVisorReport::where('class_id', $this->class_id)->update([
                'class_id' => $this->class_id,
                'user_id' => auth()->user()->id,
                'internet' => $validated['internet'],
                'camera' => $validated['camera'],
                'smartboard' => $validated['smartboard'],
                'audio' => $validated['audio'],
                'teachbackground' => $validated['teachbackground'],
                'teachenvironment' => $validated['teachenvironment'],
                'teachrelationship' => $validated['teachrelationship'],
                'teacheducational' => $validated['teacheducational'],
                'notes' => $validated['notes'],
            ]);
            $class = Clss::find($this->class_id);
            $notifiedusers = User::where('role', 'admin')->pluck('id');
            foreach ($notifiedusers as $notifieduser) {
                Notification::create([
                    'user_id' => $notifieduser,
                    'title' => 'new report',
                    'url' => 'class?q=' . $class->student->name . '&te=' . $class->teacher->id . '&stdate=' . Carbon::parse($class->date_time)->format('Y-m-d') . '&endate=' . Carbon::parse($class->date_time)->format('Y-m-d'),
                    'message' => 'تم تعديل تقرير لحلقة للطالب"' . $class->student->name . '" والمعلم "' . $class->teacher->name . '" بواسطة "' . auth()->user()->name . '".',
                ]);
            }
        $this->closemodal('view-supervisor-report');
        }

        $this->dispatch('flashupdated');
    }

    public function totalevaluation()
    {
        return (($this->internet + $this->camera + $this->smartboard + $this->audio + $this->teachbackground + $this->teachenvironment + $this->teachrelationship + $this->teacheducational) / 80 ) * 100;
    }

// methods for alpinejs modals
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'class_id',
            'student_id',
            'student_name',
            'teacher_id',
            'teacher_name',
            'qualifi_id',
            'qualifi_name',
            'date_time',
            'start_meeting',
            'join_meeting',
            'supreport_id',
            'teachreport_id',
            'clssreport_id',
            'studentgenderpref',
            'defaulttimezone',
            'revision_eval',
            'recite_eval',
            'lastupdateuser_id',
            'lastupdatedat',
            'reprevision',
            'reprecite',
            'internet',
            'camera',
            'smartboard',
            'audio',
            'teachbackground',
            'teachenvironment',
            'teachrelationship',
            'teacheducational',
            'notes',
            'class_rate',
            'class_rated',
            'classinweek',
            'classesdaytime',
            'classesdaytimenum',
            'selectedclasses',
            'selectedsupreports',
            'selectedteachreports',
            'selectedclssreports',
            'selectedclassesmeetings',
            'selectall',
            'whoctive',
            'responseData',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function redirectToUrl($url)
    {
        $this->dispatch('open-new-window', $url);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }

//teacher and student login to classes
    public function goteacher($start_meeting, $class_id, $teacher_id, $student_id, $user_id, $user_role)
    {
        if (!empty($start_meeting)) {
            $class = Clss::find($class_id);
            ClssReport::create([
                'class_id' => $class_id,
                'teacher_id' => $teacher_id,
                'student_id' => $student_id,
                'qualifi_id' => $class->qualifi_id,
                'loggedin_user' => $user_id,
                'loggedin_user_role' => $user_role,
                'loggedin_time' => Carbon::createFromFormat('Y-m-d H:i:s', now())->format('Y-m-d H:i:s'),
            ]);
            $class->update([
                'is_active' => 'active',
            ]);
            $user = User::find($user_id);
            $user->update([
                'is_active' => true,
            ]);
            $student = User::find($student_id);
            $teacher = User::find($teacher_id);

            Mail::to($user->email)->send(new ClassStarted($class));

            $notifiedusers = User::whereIn('role', ['superadmin','admin','supervisor', 'moderator'])->pluck('id')->push($student_id, $teacher_id);
            foreach ($notifiedusers as $notifieduser) {
                Notification::create([
                    'user_id' => $notifieduser,
                    'title' => 'active class',
                    'url' => 'class?q=' . $student->name . '&te=' . $teacher->id . '&stdate=' . Carbon::parse($class->date_time)->format('Y-m-d') . '&endate=' . Carbon::parse($class->date_time)->format('Y-m-d'),
                    'message' => 'تم انعقاد حلقة للطالب"' . $student->name . '" والمعلم "' . $teacher->name . '" بواسطة "' . auth()->user()->name . '".',
                ]);
            }
            $this->redirectToUrl($start_meeting);
        }else {
            $this->openmodal('warning-teacher');
        }
    }
    public function gostudent($start_meeting, $class_id, $teacher_id, $student_id, $user_id, $user_role)
    {
        if (!empty($start_meeting)) {
            $class = Clss::find($class_id);
            ClssReport::create([
                'class_id' => $class_id,
                'teacher_id' => $teacher_id,
                'student_id' => $student_id,
                'qualifi_id' => $class->qualifi_id,
                'loggedin_user' => $user_id,
                'loggedin_user_role' => $user_role,
                'loggedin_time' => Carbon::createFromFormat('Y-m-d H:i:s', now())->format('Y-m-d H:i:s'),
            ]);
            $class->update([
                'is_active' => 'active',
            ]);
            $user = User::find($user_id);
            $user->update([
                'is_active' => true,
            ]);
            $student = User::find($student_id);
            $teacher = User::find($teacher_id);
            $studentbalance = $student->student->numofclasses;
            if($studentbalance > 1)
            {
                $student->student->update([
                    'numofclasses' => $studentbalance-1,
                ]);

            } elseif ($studentbalance == 1) {
                $student->student->update([
                    'numofclasses' => $studentbalance-1,
                    'subscription' => 'suspended',
                ]);
            }
            $notifiedusers = User::whereIn('role', ['superadmin','admin','supervisor'])->pluck('id')->push($student_id, $teacher_id);
            foreach ($notifiedusers as $notifieduser) {
                Notification::create([
                    'user_id' => $notifieduser,
                    'title' => 'active class',
                    'url' => 'class?q=' . $student->name . '&te=' . $teacher->id . '&stdate=' . Carbon::parse($class->date_time)->format('Y-m-d') . '&endate=' . Carbon::parse($class->date_time)->format('Y-m-d'),
                    'message' => 'تم دخول "' . $user->name . '" حلقة للطالب "' . $student->name . '" والمعلم "' . $teacher->name . '" بواسطة "' . auth()->user()->name . '".',
                ]);
            }
            $this->redirectToUrl($start_meeting);
        } else {
            $this->openmodal('warning-teacher');
        }
    }
    public function stopteacher($class_id, $teacher_id, $student_id, $user_id)
    {
        // dd($class_id, $teacher_id, $user_id);
        $classreport = ClssReport::where('class_id', $class_id)
        ->where('teacher_id', $teacher_id)
        ->where('loggedin_user', $user_id)
        ->first();
        if(empty($classreport))
        {
            $loggedin_time = Carbon::now();
        }else{
            $loggedin_time = Carbon::parse($classreport->loggedin_time);
        }
        $loggedinduration = $loggedin_time->diffInMinutes(Carbon::createFromFormat('Y-m-d H:i:s', now())->format('Y-m-d H:i:s')) ?? 'not available';
        if ($loggedinduration >= 35)
        {
            $loggedinduration = 35;
        }
        // dd(Carbon::parse($classreport->loggedin_time)->diffInMinutes(Carbon::createFromFormat('Y-m-d H:i:s', now())));
        // dd($classreport->quali->rateperhour);
        $amount_due = 0;
        if(auth()->user()->id === $classreport->teacher_id)
        {
            $amount_due = round(($loggedinduration / 60) * $classreport->quali->rateperhour);
        }
        $classreport->update([
            'loggedout_time' => Carbon::createFromFormat('Y-m-d H:i:s', now())->format('Y-m-d H:i:s'),
            'loggedinduration' => $loggedinduration,
            'amount_due' => $amount_due,
        ]);
        $class = Clss::find($class_id);
        $class->update([
            'is_active' => 'ended',
        ]);
        $teacher = User::find($teacher_id);
        $teacher->update([
            'is_active' => false,
        ]);
        $student = User::find($student_id);
        $student->update([
            'is_active' => false,
        ]);
        $notifiedusers = User::whereIn('role', ['superadmin','admin','supervisor', 'moderator'])->pluck('id')->push($student_id, $teacher_id);
        foreach ($notifiedusers as $notifieduser)
        {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'active class',
                'url' => 'class?q=' . $student->name . '&te=' . $teacher->id . '&stdate=' . Carbon::parse($class->date_time)->format('Y-m-d') . '&endate=' . Carbon::parse($class->date_time)->format('Y-m-d'),
                'message' => 'تم إنهاء حلقة للطالب"' . $student->name . '" والمعلم "' . $teacher->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }

    }
    public function stopstudent($class_id, $teacher_id, $student_id, $user_id)
    {
        $classreport = ClssReport::where('class_id', $class_id)
        ->where('teacher_id', $teacher_id)
        ->where('loggedin_user', $user_id)
        ->first();

        $classreport->update([
            'loggedout_time' => Carbon::createFromFormat('Y-m-d H:i:s', now())->format('Y-m-d H:i:s'),
            'loggedinduration' => Carbon::parse($classreport->loggedin_time)->diffInMinutes(Carbon::createFromFormat('Y-m-d H:i:s', now())->format('Y-m-d H:i:s')),
        ]);
        $user = User::find($user_id);
            $user->update([
            'is_active' => false,
        ]);
        $class = Clss::find($class_id);
        $student = User::find($student_id);
        $teacher = User::find($teacher_id);
        $notifiedusers = User::whereIn('role', ['superadmin','admin','supervisor', 'moderator'])->pluck('id')->push($student_id, $teacher_id);
        foreach ($notifiedusers as $notifieduser)
        {
            Notification::create([
                'user_id' => $notifieduser,
                'title' => 'active class',
                'url' => 'class?q=' . $student->name . '&te=' . $teacher->id . '&stdate=' . Carbon::parse($class->date_time)->format('Y-m-d') . '&endate=' . Carbon::parse($class->date_time)->format('Y-m-d'),
                'message' => 'تم خروج "' . $user->name . '" حلقة للطالب "' . $student->name . '" والمعلم "' . $teacher->name . '" بواسطة "' . auth()->user()->name . '".',
            ]);
        }
    }
    public function checkwhoactive($teacherid, $studentid)
    {
        $student = User::find($studentid);
        $teacher = User::find($teacherid);
        if ($teacher->is_active == true && $student->is_active != true)
        {
            return 'teacher';
        }
        elseif ($student->is_active == true)
        {
            return 'both';
        }
    }
    public function checkloggedin($value)
    {
        $loggedin = '';
        $checkuser = ClssReport::where('loggedin_user', auth()->user()->id)
        ->where('class_id', $value)
        ->exists();
        if ($checkuser)
        {
            $loggedin = true;
        } else {
            $loggedin = false;
        }
        return $loggedin;
    }
    public function checkloggedout($value)
    {
        $loggedout = '';
        $checkuser = ClssReport::where('loggedin_user', auth()->user()->id)
        ->where('class_id', $value)
        ->whereNotNull('loggedout_time')
        ->exists();
        if ($checkuser)
        {
            $loggedout = true;
        } else {
            $loggedout = false;
        }
        return $loggedout;
    }
    public function studentnumofclasses()
    {
        if(auth()->user()->role == 'student')
        {
            $numofclasses = SalesReport::where('student_id', auth()->user()->id)->first()->numofclasses ?? 0;
        } else {
            $numofclasses = '';
        }
        return $numofclasses;
    }
    public function studentbalance()
    {
        if(auth()->user()->role == 'student')
        {
            $studentbalance = Student::where('user_id', auth()->user()->id)->first()->numofclasses;
        } else {
            $studentbalance = '';
        }
        return $studentbalance;
    }

    public function gettimezone($datetime, $userid)
    {
        return  Carbon::createFromformat('Y-m-d H:i:s', $datetime, 'Africa/Cairo')->setTimezone($this->usertimezone($userid))->format('Y-m-d h:i A');
    }
    public function usertimezone($userId)
    {
        $usercountry = User::find($userId)->country;
        switch ($usercountry) {
            case 'المملكة العربية السعودية':
                $this->defaulttimezone = '';
                return 'Asia/Riyadh';
                break;

            case 'الإمارات العربية المتحدة':
                $this->defaulttimezone = '';
                return 'Asia/Dubai';
                break;

            case 'الكويت':
                $this->defaulttimezone = '';
                return 'Asia/Kuwait';
                break;

            case 'قطر':
                $this->defaulttimezone = '';
                return 'Asia/Doha';
                break;

            case 'عمان':
                $this->defaulttimezone = '';
                return 'Asia/Muscat';
                break;

            case 'العراق':
                $this->defaulttimezone = '';
                return 'Asia/Baghdad';
                break;

            case 'المغرب':
                $this->defaulttimezone = '';
                return 'Africa/Casablanca';
                break;

            case 'الجزائر':
                $this->defaulttimezone = '';
                return 'Africa/Algiers';
                break;

            case 'تونس':
                $this->defaulttimezone = '';
                return 'Africa/Tunis';
                break;

            case 'السودان':
                $this->defaulttimezone = '';
                return 'Africa/Khartoum';
                break;

            default:
                $this->defaulttimezone = '* جميع مواعيد الحلقات حسب التوقيت الرسمي لمدينة القاهرة .. يرجى مراعاة فروق التوقيت.';
                return 'Africa/Cairo';
                break;
        }

    }
    public function errormessage($case)
    {
        switch ($case) {
            case 'no-zoomapp':
                $this->errormessage = '.. هذا المعلم ليس لدية بيانات لتطبيق زوم .. ';
                break;

                default:
                $this->errormessage = '';
                break;
        }
        // dd($this->errormessage);
        $this->openmodal('show-error-message');
    }
    public function updated($prop)
    {
        if($prop == 'student_id')
        {
            if(!empty($this->student_id))
            {
                $student = User::where('id', $this->student_id)->first();
                $this->studentgenderpref = $student->student->gender_prefer;
                $this->classinweek = $student->student->numofclasses;
            } else {
                $this->studentgenderpref = '';
                $this->classinweek;
            }
        }
        if($prop == 'qualifi_id')
        {
            $this->qualifi_name = $this ->qualifi_id;

            // dd($this->teachers, $this->filterqualifications, $this ->qualifi_id);
        }
    }
    public function mount()
    {
        $this->students = User::filterrole('student')->get();

        $this->qualifications = TeacherQualifications::all();

        if(empty($this->filterdatestart))
        {
            $this->filterdatestart = Carbon::today();
        }
        $this->perpage = Settings::find(1)->perpage;
        $this->classperiod = Settings::find(1)->classperiod;
        $this->sortBy = 'date_time';
        $this->sortDir = 'ASC';
    }

    public function render()
    {
        if (auth()->user()->role == 'teacher')
        {
            $this->filterteacher = auth()->user()->id;
        } elseif (auth()->user()->role == 'student')
        {
            $this->filterstudent = auth()->user()->id;
        }

        $classses = Clss::filterdate($this->filterdatestart, $this->filterdateend)
                    ->search($this->search)
                    ->filterteacher($this->filterteacher)
                    ->filterstudent($this->filterstudent)
                    ->filterclass($this->filterclass)
                    ->filteractive($this->filteractive)
                    ->orderBy($this->sortBy, $this->sortDir);

        $events = [];
        foreach ($classses->get() as $closs) {
            $clossteacher = $closs->teacher ? $closs->teacher->name : '';
            $clossstudent = $closs->student ? $closs->student->name : '';
            $events[] = [
                'title' => 'الطالب: ' . $clossstudent . '  // --- \\\ والمعلم: ' . $clossteacher,
                'start' => Carbon::createFromFormat('Y-m-d H:i:s', $closs->date_time, 'Africa/Cairo')->setTimezone($this->usertimezone(auth()->user()->id))->format('Y-m-d H:i:s'),
                'end' => Carbon::createFromFormat('Y-m-d H:i:s', $closs->date_time, 'Africa/Cairo')->setTimezone($this->usertimezone(auth()->user()->id))->addMinutes($this->classperiod ?? 30)->format('Y-m-d H:i:s'),
            ];
        }
        $classsess = $this->perpage === 'all' ? $classses : $classses->paginate($this->perpage);
        $this->showall = $classsess->count();

        return view('livewire.closs', [
            'events' => $events,
            'classes' => $classsess,
            'teachers' => User::filterrole('teacher')
            ->filtergender($this->studentgenderpref)
            ->filterqualifications($this->qualifi_name)
            ->get(),
        ]);
    }
}
