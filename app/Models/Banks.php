<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Banks extends Model
{
    use HasFactory;

    protected $table = 'banks';

    protected $fillable = [
        'country',
        'name',
        'accountuser',
        'balance',
        'notes',
    ];

    public function scopeFiltercountry($query, $value)
    {
        $query->when($value, function($query, $value) {
            return $query->where('country', $value );
        });
    }

    public function scopesearch($query, $value)
    {
        $query->when($value, function($query, $value) {
            return $query->where('accountuser', 'like', '%' . $value . '%')
            ->orWhere('name', 'like', '%' . $value . '%');
        });
    }
}
