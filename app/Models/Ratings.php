<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ratings extends Model
{
    use HasFactory;

    protected $table = 'ratings';

    protected $fillable = [
        'class_id',
        'rater_id',
        'ratee_id',
        'rate',
        'notes',
    ];

    public function class()
    {
        return $this->belongsTo(Clss::class, 'class_id', 'id');
    }
    public function rater()
    {
        return $this->belongsTo(User::class, 'rater_id', 'id');
    }
    public function ratee()
    {
        return $this->belongsTo(User::class, 'ratee_id', 'id');
    }

    public function scopeFilterteacher($query, $value)
    {
        return $query->when($value, function($query, $value) {
            return $query->where('ratee_id', $value );
        });
    }
    public function scopeFilterdate($query, $startdate, $enddate)
    {
        return $query->when($startdate, function ($query, $startdate){
            return $query->whereDate('created_at', '>=', $startdate);
        })
        ->when($enddate, function ($query, $enddate){
            return $query->whereDate('created_at', '<=', $enddate);
        });
    }
    public function scopeSearch($query, $value)
    {
        return $query->whereHas('rater', function ($query) use ($value) {
            return $query->when($value, function($query, $value) {
                return $query->where('name', 'like', '%' . $value . '%');
            });
        })
        ->orWhereHas('ratee', function ($query) use ($value) {
            return $query->when($value, function($query, $value) {
                return $query->where('name', 'like', '%' . $value . '%');
            });
        });
    }
}
