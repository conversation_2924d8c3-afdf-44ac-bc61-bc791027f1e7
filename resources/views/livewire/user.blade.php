<div>
    {{-- add modal --}}
    <x-g-modal name="add">
        <div class="bg-white dark:bg-blue-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">إضافة مستخدم جديد</h1>
                <button type="button" wire:click.prevent="closemodal('add')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="add-user"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم:</label>
                    <input type="text" wire:model.live="name" id="name" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسم العضو" required>
                    @error('name')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم المستخدم:</label>
                    <input type="text" wire:model.live="username" id="username" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب اسم المستخدم" required>
                    @error('username')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الالكتروني</label>
                    <input type="email" wire:model.live="email" id="email" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب بريدك الالكتروني"  required>
                    @error('email')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كلمة السر</label>
                    <input type="{{ $showpass ? 'text' : 'password' }}" wire:model.live="password" id="password" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="ادخل كلمة السر" required><br/>
                    <label for="showpass" class="flex place-items-center mt-2">
                        <input wire:model.live="showpass" id="showpass" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="showpass">
                        <span class="mx-2 text-sm text-white">{{ __('اظهار الباسورد') }}</span>
                    </label>
                    @error('password')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم التليفون:</label>
                    <input type="text" wire:model.live="phone" id="phone" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="اكتب رقم التليفون" required>
                    @error('phone')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البلد:</label>
                    <select wire:model.live="country" id="role" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  selected>اختر البلد</option>
                        @foreach ( $listcountries as $key => $listcountry)
                            <option style="direction:rtl;" value="{{ $listcountry }}">{{ $listcountry }}</option>
                        @endforeach
                    </select>
                    @error('country')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4 font-cairo">
                    <label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المهام</label>
                    <select wire:model.live="role" id="role" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;" selected>اختر المهام</option>
                        <option style="direction:ltr;" value="superadmin">ادارة عليا</option>
                        <option style="direction:ltr;" value="admin">ادارة</option>
                        <option style="direction:ltr;" value="moderator">دعم فني</option>
                        <option style="direction:ltr;" value="supervisor">اشراف</option>
                        <option style="direction:ltr;" value="accountant">مالية</option>
                        <option style="direction:ltr;" value="sales">ايرادات</option>
                        <option style="direction:ltr;" value="teacher">تدريس</option>
                        <option style="direction:ltr;" value="student">طلاب</option>
                    </select>
                    @error('role')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="save" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="save"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('add')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- edit modal --}}
    <x-g-modal name="edit">
        <div class="bg-white dark:bg-yellow-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">تعدبل بيانات "{{ $name }}" </h1>
                <button type="button" wire:click.prevent="closemodal('edit')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="edit-user"
                x-data="{
                    init() {
                        Livewire.hook('commit', ({ succeed }) => {
                            succeed(() => {
                                this.$nextTick(() => {
                                    const firstErrorMessage = document.querySelector('.validation-error')

                                    if (firstErrorMessage !== null) {
                                        firstErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
                                    }
                                })
                            })
                        })
                    }
                }"
            >
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم:</label>
                    <input type="text" wire:model.live="name" id="name" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" value="{{ $name }}" placeholder="اكتب اسم العضو" required>
                    @error('name')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم المستخدم:</label>
                    <input type="text" wire:model.live="username" id="username" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" value="{{ $username }}" placeholder="اكتب اسم المستخدم" required>
                    @error('username')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الالكتروني</label>
                    <input type="email" wire:model.live="email" id="email" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" value="{{ $email }}" placeholder="اكتب بريدك الالكتروني"  required>
                    @error('email')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كلمة السر</label>
                    <input type="{{ $showpass ? 'text' : 'password' }}" wire:model.live="password" id="password" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required><br/>
                    <span class="text-sm font-bold text-black-500">* ادخل كلمة سر جديدة اذا كنت تريد تغييرها </span>
                    <label for="showpass" class="flex place-items-center mt-2">
                        <input wire:model.live="showpass" id="showpass" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="showpass">
                        <span class="mx-2 text-sm text-white">{{ __('اظهار الباسورد') }}</span>
                    </label>
                    @error('password')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم التليفون:</label>
                    <input type="text" wire:model.live="phone" id="phone" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" value="{{ $phone }}" placeholder="اكتب رقم التليفون" required>
                    @error('phone')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البلد:</label>
                    <select wire:model.live="country" id="role" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:rtl;"  selected>اختر البلد</option>
                        @foreach ( $listcountries as $key => $listcountry)
                            <option style="direction:rtl;" value="{{ $listcountry }}">{{ $listcountry }}</option>
                        @endforeach
                    </select>
                    @error('country')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المهام</label>
                    <select wire:model.live="role" id="role" style="direction:ltr;" class="shadow-sm font-cairo rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option style="direction:ltr;" value="superadmin" {{ $role == 'superadmin' ? ' selected' : ''}}>ادارة عليا</option>
                        <option style="direction:ltr;" value="admin" {{ $role == 'admin' ? ' selected' : ''}}>ادارة</option>
                        <option style="direction:ltr;" value="moderator" {{ $role == 'moderator' ? ' selected' : ''}}>دعم فني</option>
                        <option style="direction:ltr;" value="supervisor" {{ $role == 'supervisor' ? ' selected' : ''}}>اشراف</option>
                        <option style="direction:ltr;" value="accountant" {{ $role == 'accountant' ? ' selected' : ''}}>مالية</option>
                        <option style="direction:ltr;" value="sales" {{ $role == 'sales' ? ' selected' : ''}}>ايرادات</option>
                        <option style="direction:ltr;" value="teacher" {{ $role == 'teacher' ? ' selected' : ''}}>تدريس</option>
                        <option style="direction:ltr;" value="student" {{ $role == 'student' ? ' selected' : ''}}>طلاب</option>
                    </select>
                    @error('role')
                        <div class="validation-error mt-1 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                </div>
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="update" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حفظ</button>

                    <x-loading method="update"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('edit')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- delete modal --}}
    <x-g-modal name="delete">
        <div class="bg-white dark:bg-red-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">حذف المستخدم "{{ $name }}"</h1>
                <button type="button" wire:click.prevent="closemodal('delete')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="delete-user">
                <div class="mb-4">
                    <div class="block text-right text-lg font-bold text-white">هل انت متأكد من حذف المستخدم "{{ $name }}"؟!</div>
                </div>
                <div class="flex justify-end">
                    <button type="button" wire:loading.remove wire:click.prevent="delete" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>

                    <x-loading method="delete"></x-loading>

                    <button type="button" wire:click.prevent="closemodal('delete')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                </div>
            </form>
        </div>
    </x-g-modal>
    {{-- delete selected modal --}}
    <x-g-modal name="deleteselected">
        <div class="bg-white dark:bg-gray-900 shadow-md rounded-lg px-8 py-6 w-full">
            <div class="flex justify-between">
                <h1 class="text-2xl font-bold text-center mb-4 dark:text-gray-200">حذف المستخدمين</h1>
                <button type="button" wire:click.prevent="closemodal('deleteselected')" class="text-slate-400 bg-transparent hover:text-slate-900 rounded-lg text-sm w-8 h-8
                        dark:hover:bg-slate-600 dark:hover:text-white">
                    <svg class="w-4 h-4 mx-2 my-2" fill="#ffffff" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10
                                11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form action="delete-selectedusers">
                @if (empty($selectedusers))
                    <div class="mb-4">
                        <div class="block text-right text-lg font-bold text-white">لابد من اختيار عناصر حتى يتم حذفهم!</div>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" disabled wire:click.prevent="deleteselected" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 h focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>
                        <button type="button" wire:click.prevent="closemodal('deleteselected')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                    </div>
                @else
                    <div class="mb-4">
                        <div class="block text-right text-lg font-bold text-white">هل انت متأكد من حذف المستخدمين التاليين؟!</div>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" wire:loading.remove wire:click.prevent="deleteselected" class="w-24 py-2 px-4 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">حذف</button>

                        <x-loading method="deleteselected"></x-loading>

                        <button type="button" wire:click.prevent="closemodal('deleteselected')" class="w-24 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">إلغاء</button>
                    </div>
                @endif
            </form>
        </div>
    </x-g-modal>

    <!-- flash save successful message -->
    <x-flash-message on="flashsaved">
        <div class="flex items-center bg-green-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تسجيل العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    <!-- flash update successful message -->
    <x-flash-message on="flashupdated">
        <div class="flex items-center bg-yellow-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم تعديل العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    <!-- flash delete successful message -->
    <x-flash-message on="flashdeleted">
        <div class="flex items-center bg-red-500 font-bold my-4 px-4 py-3 rounded-lg " role="alert">
            <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z"/></svg>
            <p>تم حذف العنصر بنجاح.</p>
        </div>
    </x-flash-message>

    {{-- data tables --}}
    <div class="card">
        <header class=" block card-header noborder">
          <h4 class="text-primary-500 font-bold text-3xl">إدارة الأعضاء</h4>
        </header>
        <div x-data="{open: false }" class="card-body px-6 pb-6">
            <div class="sm:grid sm:my-2 md:flex sm:place-content-center md:justify-between w-auto mr-0">
                <div class="grid sm:grid-cols-1 lg:grid-cols-6 gap-3">
                    {{-- add modal button --}}
                    <button type="button" x-data="" x-on:click.prevent="$dispatch('open-modal', 'add')" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div class="flex place-items-center">
                        <iconify-icon icon="heroicons:plus-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="اضافة"></iconify-icon>
                        اضافة عضو جديد
                        </div>
                    </button>
                    {{-- delete selected modal button --}}
                    <button type="button" wire:click.prevent="showdeleteselected" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div class="flex place-items-center">
                        <iconify-icon icon="heroicons:trash-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="حذف"></iconify-icon>
                        حذف المحددين
                        </div>
                    </button>
                    {{-- advanced section modal button --}}
                    <button type="button" x-on:click="open = ! open" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                        <div x-show="! open" class="flex place-items-center">
                            <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                            اظهار البحث الــمتقدم
                        </div>
                        <div x-show="open" class="flex place-items-center">
                            <iconify-icon icon="heroicons:adjustments-horizontal-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                            اخفاء البحث الــمتقدم
                        </div>
                    </button>
                    @if (!empty($filtercountry) || !empty($filterrole))
                        <button wire:click.prevent="resetallfilter" class="bg-[#016241] hover:bg-[#be9539] text-white hover:text-[#016241] font-bold py-4 px-4 rounded-lg">
                            <div class="flex place-items-center">
                                <iconify-icon icon="heroicons:arrow-uturn-down-20-solid" style="font-size: 24px;color:white;font-weight: bold;" class="ml-2" alt="متقدم"></iconify-icon>
                                إعادة تعيين التصفية
                            </div>
                        </button>
                    @endif
                </div>
                <div class="my-3 sm:w-auto sm:mt-0 sm:ml-auto md:ml-0">
                    <div class="relative flex w-80 text-gray-700 mr-auto my-auto place-items-center">
                        @if (!empty($search))
                            <button type="button" wire:click.prevent="resetsearch" class="absolute inset-y-0 left-0 pl-3 flex items-center z-1">
                                <iconify-icon icon="heroicons:x-mark-20-solid" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                            </button>
                        @endif
                        <iconify-icon icon="heroicons:magnifying-glass-solid" style="font-size: 24px;color:#016241;" alt="تعديل" class="ml-2"></iconify-icon>
                        <input wire:model.live.debounce.500="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-lg rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="بحث..." wire:ignore>
                    </div>
                </div>
            </div>
            {{-- advanced filter by status and teacher --}}
            <div x-show="open" class="sm:grid sm:my-2 md:flex sm:place-content-center md:justify-between place-items-center">
                <div class="flex lg:justify-start place-items-center w-full my-2">
                    <label for="filtercountry" class="block text-lg font-medium text-gray-700 dark:text-[#016241] ml-2">البلد: </label>
                    <select name="filtercountry" wire:model.live="filtercountry" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option style="direction:rtl;" value="" selected>اختر البلد</option>
                            @foreach ( $listcountries as $key => $listcountry)
                                <option style="direction:rtl;" value="{{ $listcountry }}">{{ $listcountry }}</option>
                            @endforeach
                    </select>
                    @if (!empty($filtercountry))
                        <button type="button" wire:click.prevent="resetfilter('filtercountry')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                            <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                        </button>
                    @endif
                </div>
                <div class="flex lg:justify-start place-items-center w-full my-2">
                    <label for="filterrole" class="block text-lg font-medium text-gray-700 dark:text-[#016241] mx-2">المهام:</label>
                    <select name="filterrole" wire:model.live="filterrole" style="direction:ltr;" class="shadow-sm rounded-md w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option style="direction:rtl;" value="">اختر المهام</option>
                        <option style="direction:ltr;" value="superadmin">ادارة عليا</option>
                        <option style="direction:ltr;" value="admin">ادارة</option>
                        <option style="direction:ltr;" value="moderator">دعم فني</option>
                        <option style="direction:ltr;" value="supervisor">اشراف</option>
                        <option style="direction:ltr;" value="accountant">مالية</option>
                        <option style="direction:ltr;" value="sales">ايرادات</option>
                        <option style="direction:ltr;" value="teacher">تدريس</option>
                        <option style="direction:ltr;" value="student">طلاب</option>
                    </select>
                    @if (!empty($filterrole))
                        <button type="button" wire:click.prevent="resetfilter('filterrole')" class="relative flex z-1 text-white bg-slate-300 rounded-lg text-sm w-8 h-8 dark:hover:bg-slate-600 dark:hover:text-white mr-2">
                            <iconify-icon icon="heroicons:x-mark-20-solid" class=" my-auto mx-auto" style="font-size: 24px;color:#202020;font-weight: bold;" alt="حذف"></iconify-icon>
                        </button>
                    @endif
                </div>
            </div>
            <div class="overflow-x-auto -mx-6 dashcode-data-table">
                <div class="inline-block min-w-full align-middle">
                    <div class="overflow-hidden">
                        @unless ($users->count() == 0)
                            <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700 text-xl">
                                <thead class=" bg-slate-200 dark:bg-slate-700 text-xl">
                                    <tr>
                                    <th scope="col" class=" table-th text-xl font-medium"><input type="checkbox" wire:model.live="selectall" class="border-gray-300 rounded bg-gray-50 borded ml-1"> الكل </th>
                                    <th scope="col" class=" table-th text-xl font-medium w-8"></th>
                                    <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('name')">الإسم</a></th>
                                    <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('username')">اسم المستخدم</a></th>
                                    <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('email')">البريد الالكتروني</a></th>
                                    <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('phone')">رقم التليفون</a></th>
                                    <th scope="col" class=" table-th text-xl font-medium"><a href="#" wire:click.prevent="sortfields('country')">البلد</a></th>
                                    <th scope="col" class="text-center text-[#cbd5e1] text-xl font-medium ">المهام</th>
                                    <th scope="col" class=" table-th text-xl font-medium ">أجراءات</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                    @foreach ($users as $user)
                                    <tr wire:key="user-{{ $user->id }}">
                                    <td class="table-td text-xl font-normal"><input type="checkbox" wire:model.live="selectedusers" value="{{ $user->id }}" class="border-gray-300 rounded bg-gray-50 borded"></td>
                                    <td class="table-td text-xl text-right font-normal w-8">
                                            <div class="lg:h-8 lg:w-8 h-8 w-8 rounded-full flex-1">
                                                <img src="{{ asset('assets/images/avatar/'). '/' . $user->role }}.png" alt="user" class="block w-8 h-8 object-cover rounded-full bg-gray-300">
                                            </div>
                                    </td>
                                    <td class="table-td text-xl font-normal">
                                            <span class=" text-slate-600 dark:text-white capitalize">{{ $user->name }}</span>
                                    </td>
                                    <td class="table-td text-xl font-normal">
                                        <span class=" text-slate-600 dark:text-white capitalize">{{ $user->username }}</span>
                                    </td>
                                    <td class="table-td text-xl font-normal">
                                        <span class=" text-slate-600 dark:text-white capitalize">{{ $user->email }}</span>
                                    </td>
                                    <td class="table-td text-xl font-normal">
                                        <span class=" text-slate-600 dark:text-white capitalize">{{ $user->phone }}</span>
                                    </td>
                                    <td class="table-td text-xl font-normal">
                                        <span class=" text-slate-600 dark:text-white capitalize">{{ $user->country }}</span>
                                    </td>
                                    <td class="table-td text-center text-xl font-normal">
                                        @if ($user->role === 'superadmin')
                                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white border-[#016241] border bg-danger-500">
                                                ادمن
                                        @elseif ($user->role === 'admin')
                                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white border-[#016241] border bg-warning-500">
                                                مدير
                                        @elseif ($user->role === 'moderator')
                                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white border-[#016241] border bg-warning-500">
                                                مراقب
                                        @elseif ($user->role === 'supervisor')
                                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white border-[#016241] border bg-success-500">
                                                مشرف
                                        @elseif ($user->role === 'sales')
                                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white border-[#016241] border bg-secondary-500">
                                                بائع
                                        @elseif ($user->role === 'accountant')
                                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white border-[#016241] border bg-black">
                                                محاسب
                                        @elseif ($user->role === 'teacher')
                                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white border-[#016241] border bg-primary-500">
                                                مدرس
                                        @elseif ($user->role === 'student')
                                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-white border-[#016241] border bg-info-500">
                                                طالب
                                        @endif
                                        </div>
                                    </td>
                                    <td class="table-td  text-xl font-normal">
                                        <div class="flex space-x-3 rtl:space-x-reverse">
                                        <button type="button" wire:click.prevent="showedit({{ $user }})">
                                            <iconify-icon icon="heroicons:pencil-square" style="font-size: 24px;color:#016241;font-weight:bold;" alt="تعديل"></iconify-icon>
                                        </button>
                                        <button type="button" wire:click.prevent="showdelete({{ $user }})">
                                            <iconify-icon icon="heroicons:trash" style="font-size: 24px;color:#016241;font-weight:bold;" alt="حذف"></iconify-icon>
                                        </button>
                                        </div>
                                    </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        @else
                        <div class="text-center text-xl font-bold text-[#016241]">لا يوجد اعضاء حتى الآن.</div>
                        @endunless
                    </div>
                </div>
          </div>
        </div>
    </div>
{{-- tailwind pagination --}}
    <div class="flex my-4">
        <div class="flex justify-start w-[50%] items-center">
            <label for="perpage" class="text-lg font-medium text-gray-700 dark:text-[#be9539]">عدد النتائج في كل صفحة:</label>
            <select wire:model.live="perpage" id="role" style="direction:ltr;" class="w-20 shadow-sm rounded-md mx-4 px-5 py-1 border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                @foreach ($perpageoptions as $option)
                    <option style="direction:rtl;" value="{{ $option }}">{{ $option === 'all' ? 'الكل' : $option }}</option>
                @endforeach
            </select>
        </div>
        @if ($perpage === 'all')

        @else
        <div class="w-[100%] mr-4">{{ $users->links('vendor.livewire.tailwind') }}</div>
        @endif
    </div>
</div>
