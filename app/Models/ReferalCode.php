<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReferalCode extends Model
{
    use HasFactory;

    protected $table = 'referal_codes';

    protected $fillable = [
        'name',
        'short_code',
        'discount',
        'used_by',
        'notes',
    ];
    protected $cast = [
        'used_by' => 'array',
    ];

    //scope and filter methods
    public function scopeSearch($query, $value)
    {
        return $query->where('name', 'like', '%' . $value . '%')
        ->orWhere('short_code', 'like', '%' . $value . '%')
        ->orWhere('discount', 'like', '%' . $value . '%');
    }
    public function scopegetdiscount($query, $value)
    {
        $query->when($value, function($query, $value) {
            return $query->where('short_code', '=', $value );
        });
    }
}
