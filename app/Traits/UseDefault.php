<?php

namespace App\Traits;


trait UseDefault
{

    public $errormessage = '';

    // list of countries
    public $listcountries = [
        "SAR" => 'المملكة العربية السعودية',
        "EGP" => 'مصر',
        "AED" => 'الإمارات العربية المتحدة',
        "KWD" => 'الكويت',
        "QAR" => 'قطر',
        "OMR" => 'عمان',
        "IQ" => 'العراق',
        "MA" => 'المغرب',
        "DZ" => 'الجزائر',
        "TN" => 'تونس',
        "SD" => 'السودان',
        "PS" => 'السلطة الفلسطينية',
        "ET" => 'إثيوبيا',
        "AZ" => 'أذربيجان',
        "AM" => 'أرمينيا',
        "AW" => 'أروبا',
        "ER" => 'إريتريا',
        "ES" => 'أسبانيا',
        "AU" => 'أستراليا',
        "EE" => 'إستونيا',
        "AF" => 'أفغانستان',
        "IO" => 'إقليم المحيط الهندي البريطاني',
        "EC" => 'إكوادور',
        "AR" => 'الأرجنتين',
        "JO" => 'الأردن',
        "AL" => 'ألبانيا',
        "BR" => 'البرازيل',
        "PT" => 'البرتغال',
        "BA" => 'البوسنة والهرسك',
        "GA" => 'الجابون',
        "DK" => 'الدانمارك',
        "CV" => 'الرأس الأخضر',
        "SV" => 'السلفادور',
        "SN" => 'السنغال',
        "SE" => 'السويد',
        "SO" => 'الصومال',
        "CN" => 'الصين',
        "PH" => 'الفلبين',
        "CM" => 'الكاميرون',
        "CG" => 'الكونغو',
        "CD" => 'الكونغو (جمهورية الكونغو الديمقراطية)',
        "DE" => 'ألمانيا',
        "HU" => 'المجر',
        "MX" => 'المكسيك',
        "UK" => 'المملكة المتحدة',
        "TF" => 'المناطق الفرنسية الجنوبية ومناطق انتراكتيكا',
        "NO" => 'النرويج',
        "AT" => 'النمسا',
        "NE" => 'النيجر',
        "IN" => 'الهند',
        "USD" => 'الولايات المتحدة',
        "JP" => 'اليابان',
        "YE" => 'اليمن',
        "GR" => 'اليونان',
        "AQ" => 'أنتاركتيكا',
        "AG" => 'أنتيغوا وبربودا',
        "AD" => 'أندورا',
        "ID" => 'إندونيسيا',
        "AO" => 'أنغولا',
        "AI" => 'أنغويلا',
        "UY" => 'أوروجواي',
        "UZ" => 'أوزبكستان',
        "UG" => 'أوغندا',
        "UA" => 'أوكرانيا',
        "IR" => 'إيران',
        "IE" => 'أيرلندا',
        "IS" => 'أيسلندا',
        "IT" => 'إيطاليا',
        "PG" => 'بابوا-غينيا الجديدة',
        "PY" => 'باراجواي',
        "BB" => 'باربادوس',
        "PK" => 'باكستان',
        "PW" => 'بالاو',
        "BM" => 'برمودا',
        "BN" => 'بروناي',
        "BE" => 'بلجيكا',
        "BG" => 'بلغاريا',
        "BD" => 'بنجلاديش',
        "PA" => 'بنما',
        "BJ" => 'بنين',
        "BT" => 'بوتان',
        "BW" => 'بوتسوانا',
        "PR" => 'بورتو ريكو',
        "BF" => 'بوركينا فاسو',
        "BI" => 'بوروندي',
        "PL" => 'بولندا',
        "BO" => 'بوليفيا',
        "PF" => 'بولينزيا الفرنسية',
        "PE" => 'بيرو',
        "BY" => 'بيلاروس',
        "BZ" => 'بيليز',
        "TH" => 'تايلاند',
        "TW" => 'تايوان',
        "TM" => 'تركمانستان',
        "TR" => 'تركيا',
        "TT" => 'ترينيداد وتوباجو',
        "TD" => 'تشاد',
        "CL" => 'تشيلي',
        "TZ" => 'تنزانيا',
        "TG" => 'توجو',
        "TV" => 'توفالو',
        "TK" => 'توكيلاو',
        "TO" => 'تونجا',
        "TP" => 'تيمور الشرقية (تيمور الشرقية)',
        "JM" => 'جامايكا',
        "GM" => 'جامبيا',
        "GI" => 'جبل طارق',
        "GL" => 'جرينلاند',
        "AN" => 'جزر الأنتيل الهولندية',
        "PN" => 'جزر البتكارين',
        "BS" => 'جزر البهاما',
        "VG" => 'جزر العذراء البريطانية',
        "VI" => 'جزر العذراء، الولايات المتحدة',
        "KM" => 'جزر القمر',
        "CC" => 'جزر الكوكوس (كيلين)',
        "MV" => 'جزر المالديف',
        "TC" => 'جزر تركس وكايكوس',
        "AS" => 'جزر ساموا الأمريكية',
        "SB" => 'جزر سولومون',
        "FO" => 'جزر فايرو',
        "UM" => 'جزر فرعية تابعة للولايات المتحدة',
        "FK" => 'جزر فوكلاند (أيزلاس مالفيناس)',
        "FJ" => 'جزر فيجي',
        "KY" => 'جزر كايمان',
        "CK" => 'جزر كوك',
        "MH" => 'جزر مارشال',
        "MP" => 'جزر ماريانا الشمالية',
        "CX" => 'جزيرة الكريسماس',
        "BV" => 'جزيرة بوفيه',
        "IM" => 'جزيرة مان',
        "NF" => 'جزيرة نورفوك',
        "HM" => 'جزيرة هيرد وجزر ماكدونالد',
        "CF" => 'جمهورية أفريقيا الوسطى',
        "CZ" => 'جمهورية التشيك',
        "DO" => 'جمهورية الدومينيكان',
        "ZA" => 'جنوب أفريقيا',
        "GT" => 'جواتيمالا',
        "GP" => 'جواديلوب',
        "GU" => 'جوام',
        "GE" => 'جورجيا',
        "GS" => 'جورجيا الجنوبية وجزر ساندويتش الجنوبية',
        "GY" => 'جيانا',
        "GF" => 'جيانا الفرنسية',
        "DJ" => 'جيبوتي',
        "JE" => 'جيرسي',
        "GG" => 'جيرنزي',
        "VA" => 'دولة الفاتيكان',
        "DM" => 'دومينيكا',
        "RW" => 'رواندا',
        "RU" => 'روسيا',
        "RO" => 'رومانيا',
        "RE" => 'ريونيون',
        "ZM" => 'زامبيا',
        "ZW" => 'زيمبابوي',
        "WS" => 'ساموا',
        "SM" => 'سان مارينو',
        "PM" => 'سانت بيير وميكولون',
        "VC" => 'سانت فينسنت وجرينادينز',
        "KN" => 'سانت كيتس ونيفيس',
        "LC" => 'سانت لوشيا',
        "SH" => 'سانت هيلينا',
        "ST" => 'ساوتوماي وبرينسيبا',
        "SJ" => 'سفالبارد وجان ماين',
        "SK" => 'سلوفاكيا',
        "SI" => 'سلوفينيا',
        "SG" => 'سنغافورة',
        "SZ" => 'سوازيلاند',
        "SY" => 'سوريا',
        "SR" => 'سورينام',
        "CH" => 'سويسرا',
        "SL" => 'سيراليون',
        "LK" => 'سيريلانكا',
        "SC" => 'سيشل',
        "RS" => 'صربيا',
        "TJ" => 'طاجيكستان',
        "GH" => 'غانا',
        "GD" => 'غرينادا',
        "GN" => 'غينيا',
        "GQ" => 'غينيا الاستوائية',
        "GW" => 'غينيا بيساو',
        "VU" => 'فانواتو',
        "FR" => 'فرنسا',
        "VE" => 'فنزويلا',
        "FI" => 'فنلندا',
        "VN" => 'فيتنام',
        "CY" => 'قبرص',
        "KG" => 'قيرقيزستان',
        "KZ" => 'كازاخستان',
        "NC" => 'كاليدونيا الجديدة',
        "KH" => 'كامبوديا',
        "HR" => 'كرواتيا',
        "CA" => 'كندا',
        "CU" => 'كوبا',
        "CI" => 'كوت ديفوار (ساحل العاج)',
        "KR" => 'كوريا',
        "KP" => 'كوريا الشمالية',
        "CR" => 'كوستاريكا',
        "CO" => 'كولومبيا',
        "KI" => 'كيريباتي',
        "KE" => 'كينيا',
        "LV" => 'لاتفيا',
        "LA" => 'لاوس',
        "LB" => 'لبنان',
        "LI" => 'لختنشتاين',
        "LU" => 'لوكسمبورج',
        "LY" => 'ليبيا',
        "LR" => 'ليبيريا',
        "LT" => 'ليتوانيا',
        "LS" => 'ليسوتو',
        "MQ" => 'مارتينيك',
        "MO" => 'ماكاو',
        "FM" => 'ماكرونيزيا',
        "MW" => 'مالاوي',
        "MT" => 'مالطا',
        "ML" => 'مالي',
        "MY" => 'ماليزيا',
        "YT" => 'مايوت',
        "MG" => 'مدغشقر',
        "MK" => 'مقدونيا، جمهورية يوغوسلافيا السابقة',
        "BH" => 'مملكة البحرين',
        "MN" => 'منغوليا',
        "MR" => 'موريتانيا',
        "MU" => 'موريشيوس',
        "MZ" => 'موزمبيق',
        "MD" => 'مولدوفا',
        "MC" => 'موناكو',
        "MS" => 'مونتسيرات',
        "ME" => 'مونتينيغرو',
        "MM" => 'ميانمار',
        "NA" => 'ناميبيا',
        "NR" => 'ناورو',
        "NP" => 'نيبال',
        "NG" => 'نيجيريا',
        "NI" => 'نيكاراجوا',
        "NU" => 'نيوا',
        "NZ" => 'نيوزيلندا',
        "HT" => 'هايتي',
        "HN" => 'هندوراس',
        "NL" => 'هولندا',
        "HK" => 'هونغ كونغ SAR',
        "WF" => 'واليس وفوتونا'
    ];

    // list of monthes
    public $listmonthes = [
        '1' => 'يناير',
        '2' => 'فبراير',
        '3' => 'مارس',
        '4' => 'ابريل',
        '5' => 'مايو',
        '6' => 'يونيو',
        '7' => 'يوليو',
        '8' => 'أغسطس',
        '9' => 'سبتمبر',
        '10' => 'اكتوبر',
        '11' => 'نوفمبر',
        '12' => 'ديسمبر',
    ];

    // props for schadule
    public $timebyhours = [
        '01:00',
        '01:15',
        '01:30',
        '01:45',
        '02:00',
        '02:15',
        '02:30',
        '02:45',
        '03:00',
        '03:15',
        '03:30',
        '03:45',
        '04:00',
        '04:15',
        '04:30',
        '04:45',
        '05:00',
        '05:15',
        '05:30',
        '05:45',
        '06:00',
        '06:15',
        '06:30',
        '06:45',
        '07:00',
        '07:15',
        '07:30',
        '07:45',
        '08:00',
        '08:15',
        '08:30',
        '08:45',
        '09:00',
        '09:15',
        '09:30',
        '09:45',
        '10:00',
        '10:15',
        '10:30',
        '10:45',
        '11:00',
        '11:15',
        '11:30',
        '11:45',
        '12:00',
        '12:15',
        '12:30',
        '12:45',
    ];
    public $timebydays = [
        '6' => 'السبت',
        '0' => 'الأحد',
        '1' => 'الاثنين',
        '2' => 'الثلاثاء',
        '3' => 'الأربعاء',
        '4' => 'الخميس',
        '5' => 'الجمعة',
    ];

    public function getCurrencyCode($country)
    {
        switch ($country) {
            case 'المملكة العربية السعودية':
                return 'SAR';
                break;

            case 'مصر':
                return 'EGP';
                break;

            case 'الإمارات العربية المتحدة':
                return 'AED';
                break;

            case 'الكويت':
                return 'KWD';
                break;

            case 'قطر':
                return 'QAR';
                break;

            case 'عمان':
                return 'OMR';
                break;

            default:
                return 'USD';
                break;
        }
    }
    public function convertPaidToSAR($country)
    {
        switch ($country) {
            case 'المملكة العربية السعودية':
                return $this->settings->saudirate;
                break;

            case 'مصر':
                return $this->settings->egyptrate;
                break;

            case 'الإمارات العربية المتحدة':
                return $this->settings->uaerate;
                break;

            case 'الكويت':
                return $this->settings->kuwaitrate;
                break;

            case 'قطر':
                return $this->settings->qatarrate;
                break;

            case 'عمان':
                return $this->settings->omanrate;
                break;

            default:
                return $this->settings->usdrate;
                break;
        }
    }
    public function errormessage($case)
    {
        switch ($case) {
            case 'no-zoomapp':
                $this->errormessage = '.. هذا المعلم ليس لدية بيانات لتطبيق زوم .. ';
                break;

                default:
                $this->errormessage = '';
                break;
        }
        // dd($this->errormessage);
        $this->openmodal('show-error-message');
    }
}
