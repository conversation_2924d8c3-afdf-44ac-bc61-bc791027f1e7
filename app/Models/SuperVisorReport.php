<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SuperVisorReport extends Model
{
    use HasFactory;

    protected $table = 'super_visor_reports';

    protected $fillable = [
        'class_id',
        'user_id',
        'internet',
        'camera',
        'smartboard',
        'audio',
        'teachbackground',
        'teachenvironment',
        'teachrelationship',
        'teacheducational',
        'notes',
    ];

    public function clss()
    {
        return $this->belongsTo(Clss::class, 'class_id', 'id');
    }

    public function supervisor()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

}
