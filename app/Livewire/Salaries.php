<?php

namespace App\Livewire;

use App\Models\Clss;
use App\Models\ClssReport;
use App\Models\SalesReport;
use App\Models\Settings;
use App\Models\User;
use App\Traits\UseDefault;
use App\Traits\UseFilter;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;

class Salaries extends Component
{
    use WithPagination, UseFilter, UseDefault;

    public $allpaid = 0;
    public $salesmantotalsales = 0;
    public $teacherloggedinduration = 0;
    public $theallteachers = [];

    public function gettotalhours($teacher)
    {
        $totalclasses = 0;
        $classes = Clss::filterdate($this->filterdatestart, $this->filterdateend)
        ->with('clssreport')
        ->filterteacher($teacher->id)->get();
        foreach ($classes as $class)
        {
            $totalclasses += $class->clssreport->where('loggedin_user_role', 'teacher')->first()->loggedinduration ?? 0;
        }
        // dd($totalclasses);
        return $totalclasses;
    }

    public function gettotalsalary($teacher)
    {
        // $rateperclass = $teacher->teacher->rateperclass ?? 0;
        // return ($this->gettotalhours($teacher)/60) * $rateperclass;
        return ClssReport::filterdate($this->filterdatestart, $this->filterdateend)
        ->filterteacher($teacher->id)
        ->sum('amount_due');
    }

    #[Computed]
    public function updatedfiltermonth()
    {
        $this->filterbyofspecmonth($this->filtermonth);
    }

    public function mount()
    {
        $this->theallteachers = User::filterrole('teacher')->get();

        $this->perpage = Settings::find(1)->perpage;
        $this->sortBy = 'created_at';
        $this->sortDir = 'ASC';

        $this->filterbymonth();

        // 'classes' => Clss::filterdate($this->filterdatestart, $this->filterdateend)
        // ->with('clssreport')
        // ->search($this->search)
        // ->filterteacher($this->filterteacher)
        // ->filterstudent($this->filterstudent)
        // ->filteractive($this->filteractive)
        // ->orderBy($this->sortBy, $this->sortDir)
        // ->paginate($this->perpage),
    }

    public function render()
    {
        return view('livewire.salaries', [
            'allteachers' => User::filterrole('teacher')
            ->filterteacher($this->filterteacher)
            ->paginate($this->perpage),
        ]);
    }
}
