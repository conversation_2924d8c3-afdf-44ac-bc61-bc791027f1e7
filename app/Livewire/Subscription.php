<?php

namespace App\Livewire;

use Carbon\Carbon;
use App\Models\Settings;
use App\Traits\UseFilter;
use App\Traits\UseDefault;
use App\Models\Notification;
use App\Models\Subscriptions;

use Livewire\WithPagination;
use Livewire\Attributes\Computed;

use Livewire\Component;

class Subscription extends Component
{
    use WithPagination, UseFilter, UseDefault;

    public $selectedsubscriptions = [];
    public $selectall = false;

    public $subscription_id;
    public $name = '';
    public $numofclasses;
    public $amount_sar = 0;
    public $amount_aed = 0;
    public $amount_kwd = 0;
    public $amount_qar = 0;
    public $amount_omr = 0;
    public $amount_egp = 0;
    public $amount_usd = 0;
    public $notes = '';

    public $lastupdatedat = '';
    public $lastupdateuser_id = '';

    public function save()
    {
        $validated = $this->validate([
            'name' => 'required|string|max:120',
            'numofclasses' => 'required|numeric|min:0',
            'amount_sar' => 'required|numeric|min:0',
            'amount_aed' => 'required|numeric|min:0',
            'amount_kwd' => 'required|numeric|min:0',
            'amount_qar' => 'required|numeric|min:0',
            'amount_omr' => 'required|numeric|min:0',
            'amount_egp' => 'required|numeric|min:0',
            'amount_usd' => 'required|numeric|min:0',
            'notes' => 'string|max:500',
        ]);

        $subscription = Subscriptions::create($validated);

        // $notifiedusers = User::whereIn('role', ['superadmin','admin','acountant'])->pluck('id');
        // foreach ($notifiedusers as $notifieduser) {
        //     Notification::create([
        //         'user_id' => $notifieduser,
        //         'title' => 'new course',
        //         'url' => 'courses?q=' . $subscription->name,
        //         'message' => 'تم اضافة كورس جديد باسم "' . $subscription->name . '" بواسطة "' . auth()->user()->name . '".',
        //     ]);
        // }

        $this->closemodal('add');
        $this->dispatch('flashsaved');
    }
    public function showedit(Subscriptions $subscription)
    {
        $this->subscription_id = $subscription->id;
        $this->name = $subscription->name;
        $this->numofclasses = $subscription->numofclasses;
        $this->amount_sar = $subscription->amount_sar;
        $this->amount_aed = $subscription->amount_aed;
        $this->amount_kwd = $subscription->amount_kwd;
        $this->amount_qar = $subscription->amount_qar;
        $this->amount_omr = $subscription->amount_omr;
        $this->amount_egp = $subscription->amount_egp;
        $this->amount_usd = $subscription->amount_usd;
        $this->notes = $subscription->notes;
        $this->lastupdateuser_id = auth()->user()->id;
        $this->lastupdatedat = Carbon::parse($subscription->updated_at)->format('Y-m-d || A g:i:s');
        $this->openmodal('edit');
    }
    public function update()
    {
        $validated = $this->validate([
            'name' => 'required|string|max:120',
            'numofclasses' => 'required|numeric|min:0',
            'amount_sar' => 'required|numeric|min:0',
            'amount_aed' => 'required|numeric|min:0',
            'amount_kwd' => 'required|numeric|min:0',
            'amount_qar' => 'required|numeric|min:0',
            'amount_omr' => 'required|numeric|min:0',
            'amount_egp' => 'required|numeric|min:0',
            'amount_usd' => 'required|numeric|min:0',
            'notes' => 'string|max:500',
        ]);

        $subscription = Subscriptions::find($this->subscription_id);
        $subscription->update($validated);

        // $notifiedusers = User::where('role', 'admin')->pluck('id');
        // foreach ($notifiedusers as $notifieduser) {
        //     Notification::create([
        //         'user_id' => $notifieduser,
        //         'title' => 'course updated',
        //         'url' => 'courses?q=' . $subscription->name,
        //         'message' => 'تم تعديل كورس باسم "' . $subscription->name . '" بواسطة "' . auth()->user()->name . '".',
        //     ]);
        // }

        $this->closemodal('edit');
        $this->dispatch('flashupdated');
    }
    public function showdelete(Subscriptions $subscription)
    {
        $this->subscription_id = $subscription->id;
        $this->name = $subscription->name;
        $this->openmodal('delete');
    }
    public function delete(){

        $subscription = Subscriptions::find($this->subscription_id);
        $subscription->delete();

        // $notifiedusers = User::where('role', 'admin')->pluck('id');
        // foreach ($notifiedusers as $notifieduser) {
        //     Notification::create([
        //         'user_id' => $notifieduser,
        //         'title' => 'course updated',
        //         'url' => '',
        //         'message' => 'تم حذف كورس باسم "' . $subscription->name . '" بواسطة "' . auth()->user()->name . '".',
        //     ]);
        // }
        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }
    public function deleteselected()
    {
        Subscriptions::whereIn('id', $this->selectedsubscriptions)->delete();
        $this->selectedsubscriptions = [];
        $this->closemodal('deleteselected');
    }
    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedsubscriptions = Subscriptions::paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedsubscriptions = [];
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'name',
            'numofclasses',
            'amount_sar',
            'amount_aed',
            'amount_kwd',
            'amount_qar',
            'amount_omr',
            'amount_egp',
            'amount_usd',
            'notes',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function generateCode()
    {
        $this->amount_aed = $this->amount_sar * $this->settings->uaerate;
        $this->amount_kwd = $this->amount_sar * $this->settings->kuwaitrate;
        $this->amount_qar = $this->amount_sar * $this->settings->qatarrate;
        $this->amount_omr = $this->amount_sar * $this->settings->omanrate;
        $this->amount_egp = $this->amount_sar * $this->settings->egyptrate;
        $this->amount_usd = $this->amount_sar * $this->settings->usdrate;
    }
    public function mount()
    {
        $this->settings = Settings::find(1);

        if (empty($this->perpage)) {
            $this->perpage = $this->settings->perpage;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'DESC';
    }
    public function render()
    {
        return view('livewire.subscription', [
            'subscriptions' => Subscriptions::search($this->search)
            ->paginate($this->perpage),
        ]);
    }
}
