<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Courses extends Model
{
    use HasFactory;

    protected $table = 'courses';

    protected $fillable = [
        'name',
        'description',
        'teacher_id',
        'price',
        'access_code',
        'notes',
        'lastupdateuser_id',
    ];

    public function lecture()
    {
        return $this->hasMany(lectures::class, 'course_id', 'id');
    }
    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_id', 'id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'lastupdateuser_id', 'id');
    }


    public function scopeSearch($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->where('name', 'like', '%' . $value . '%');
        });
    }
    public function scopeFilterteacher($query, $value)
    {
        return $query->when($value, function($query, $value)
        {
            return $query->whereHas('teacher', function($query) use ($value){
                return $query->where('id', $value);
            });
        });
    }
}
