<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Clss extends Model
{
    use HasFactory;

    protected $table = 'clsses';

    protected $fillable = [
        'teacher_id',
        'student_id',
        'qualifi_id',
        'date_time',
        'zmeeting_id',
        'start_meeting',
        'join_meeting',
        'is_active',
        'lastupdateuser_id',
    ];

    public function teacher ()
    {
        return $this->belongsTo(User::class, 'teacher_id', 'id');
    }
    public function student ()
    {
        return $this->belongsTo(User::class, 'student_id', 'id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'lastupdateuser_id', 'id');
    }
    public function supreport ()
    {
        return $this->hasOne(SuperVisorReport::class, 'class_id', 'id');
    }
    public function teachreport ()
    {
        return $this->hasOne(TeacherReport::class, 'class_id', 'id');
    }
    public function clssreport()
    {
        return $this->hasMany(ClssReport::class, 'class_id', 'id');
    }
    public function qualification()
    {
        return $this->belongsTo(TeacherQualifications::class, 'qualifi_id', 'id');
    }

    public function scopeFilterclass($query, $value)
    {
        return $query->when($value, function($query, $value) {
            return $query->where('id', $value );
        });
    }
    public function scopeFilterteacher($query, $value)
    {
        return $query->when($value, function($query, $value) {
            return $query->where('teacher_id', $value );
        });
    }
    public function scopeFilterstudent($query, $value)
    {
        return $query->when($value, function($query, $value) {
            return $query->where('student_id', $value);
        });
    }
    public function scopeFilteractive($query, $value)
    {
        return $query->when($value, function($query, $value) {
            return $query->where('is_active', $value);
        });
    }
    public function scopeFilterdate($query, $startdate, $enddate)
    {
        return $query->when($startdate, function ($query, $startdate){
            return $query->whereDate('date_time', '>=', $startdate);
        })
        ->when($enddate, function ($query, $enddate){
            return $query->whereDate('date_time', '<=', $enddate);
        });
    }
    public function scopeSearch($query, $value)
    {
        return $query->whereHas('student', function ($query) use ($value) {
            return $query->when($value, function($query, $value) {
                return $query->where('name', 'like', '%' . $value . '%');
            });
        })
        ->orWhereHas('teacher', function ($query) use ($value) {
            return $query->when($value, function($query, $value) {
                return $query->where('name', 'like', '%' . $value . '%');
            });
        });
    }
    public function scopeFilterloggedrole($query, $value)
    {
        return $query->whereHas('clssreport', function ($query) use ($value) {
            return $query->when($value, function($query, $value) {
                return $query->where('loggedin_user_role', $value);
            });
        });
    }
    public function scopeFilterloggedsupervisor($query, $value)
    {
        return $query->whereHas('clssreport', function ($query) use ($value) {
            return $query->when($value, function($query, $value) {
                return $query->where('loggedin_user', $value);
            });
        });
    }
    public function scopeFilterqualifi($query, $value)
    {
        return $query->whereHas('qualification', function ($query) use ($value) {
            return $query->when($value, function($query, $value) {
                return $query->where('id', $value);
            });
        });
    }

}
