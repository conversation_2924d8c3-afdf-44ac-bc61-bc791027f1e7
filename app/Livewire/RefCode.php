<?php

namespace App\Livewire;

use App\Models\ReferalCode;
use App\Models\Settings;
use App\Models\Student;
use App\Models\User;
use App\Traits\UseFilter;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;

class RefCode extends Component
{
    use WithPagination, UseFilter;


    public $selectedrefcodes = [];
    public $selectall = false;

    public $refcode_id = '';
    public $name = '';
    public $short_code = '';
    public $discount = 0;
    public $used_by = [];
    public $notes = '';

    public $allusers = [];


    public function getusersnames($ids)
    {
        if(!empty($ids))
        {
            $names = [];
            foreach ($ids as $userid) {
                $user = User::find($userid);
                $names[] = $user->name;
            }
            return implode('", "', $names);
        }
        return '';
    }

    public function save()
    {
        $validated = $this->validate([
            'name' => 'required|string|max:120',
            'short_code' => 'required|unique:referal_codes,short_code|string|min:7|max:9',
            'discount' => 'required|integer|max:100|min:0',
            'used_by' => 'nullable|array',
            'notes' => 'string|max:500',
        ]);

        if (!empty($validated['used_by']))
        {
            $students = Student::whereIn('user_id', $validated['used_by'])->get();
            foreach ($students as $student) {
                $balance = $student->balance;
                $student->update([
                    'ref_code' => $validated['short_code'],
                    'balance' => $balance + ($student->paid * ($validated['discount']/100)),
                    'refcode_active_date' => Carbon::today('Africa/Cairo')->format('Y-m-d'),
                ]);
            }
        }

        $validated['used_by'] = json_encode($validated['used_by']);
        $validated['short_code'] = $this->short_code . $this->discount;

        // dd($validated);

        ReferalCode::create($validated);
        $this->closemodal('add');
        $this->dispatch('flashsaved');
    }
    public function showedit(ReferalCode $refcode)
    {
        $this->refcode_id = $refcode->id;
        $this->name = $refcode->name;
        $this->short_code = $refcode->short_code;
        $this->discount = $refcode->discount;
        $this->used_by = json_decode($refcode->used_by);
        $this->notes = $refcode->notes;
        $this->openmodal('edit');
    }
    public function update()
    {
        $validated = $this->validate([
            'name' => 'required|string|max:120',
            'short_code' => 'required|unique:referal_codes,short_code,' . $this->refcode_id . '|string|min:7|max:9',
            'discount' => 'required|integer|max:100|min:0',
            'used_by' => 'nullable|array',
            'notes' => 'string|max:500',
        ]);

        if ($this->used_by !== $validated['used_by'])
        {
            $students = Student::whereIn('user_id', $validated['used_by'])->get();
            foreach ($students as $student) {
                $balance = $student->balance;
                $student->update([
                    'ref_code' => $validated['short_code'],
                    'balance' => $balance + ($student->paid * ($validated['discount']/100)),
                    'refcode_active_date' => Carbon::today('Africa/Cairo')->format('Y-m-d'),
                ]);
            }
            $validated['used_by'] = json_encode($validated['used_by']);
        }


        ReferalCode::find($this->refcode_id)->update($validated);

        $this->closemodal('edit');
        $this->dispatch('flashupdated');

    }
    public function showdelete(ReferalCode $refcode)
    {
        $this->refcode_id = $refcode->id;
        $this->name = $refcode->name;
        $this->short_code = $refcode->short_code;
        $this->discount = $refcode->discount;
        $this->used_by = $refcode->used_by;
        $this->notes = $refcode->notes;
        $this->openmodal('delete');
    }
    public function delete(){

        ReferalCode::find($this->refcode_id)->delete();

        $this->closemodal('delete');
        $this->dispatch('flashdeleted');
    }
    public function showdeleteselected()
    {
        $this->openmodal('deleteselected');
    }
    public function deleteselected()
    {
        ReferalCode::whereIn('id', $this->selectedrefcodes)->delete();
        $this->selectedrefcodes = [];
        $this->closemodal('deleteselected');
    }


    #[Computed]
    public function updatedselectall($value)
    {
        if($value)
        {
            $this->selectedrefcodes = ReferalCode::paginate($this->perpage)
            ->pluck('id');
        } else {
            $this->selectedrefcodes = [];
        }
    }
    public function openmodal($name)
    {
        $this->dispatch('open-modal', $name);
    }
    public function closemodal($name)
    {
        $this->reset([
            'refcode_id',
            'name',
            'short_code',
            'discount',
            'used_by',
            'notes',
        ]);
        $this->resetValidation();
        $this->dispatch('close-modal', $name);
    }
    public function flashmessage($name)
    {
        $this->dispatch('flash-message', $name);
    }
    public function mount()
    {
        if(empty($this->perpage))
        {
            $this->perpage = Settings::find(1)->perpage;
        }
        $this->sortBy = 'created_at';
        $this->sortDir = 'ASC';

        $this->allusers = User::filterrole('student')->filtersubscription('fixed')->get();

        // $this->short_code = strtoupper('d' . fake('en')->regexify('[a-z]{5}') . 'f');
    }

    public function render()
    {
        if(empty($this->short_code))
        {
            $this->short_code = strtoupper('d' . fake('en')->regexify('[a-z]{5}') . 'f');
        }
        return view('livewire.ref-code', [
            'refcodes' => ReferalCode::search($this->search)
            ->paginate($this->perpage),
        ]);
    }
}
